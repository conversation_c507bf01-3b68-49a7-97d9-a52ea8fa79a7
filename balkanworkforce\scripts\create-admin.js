// Skripta za kreiranje admin korisnika
const { initializeApp } = require('firebase/app');
const { getFirestore, doc, setDoc, collection, query, where, getDocs } = require('firebase/firestore');

// Firebase konfiguracija
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Inicijalizacija Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

async function findUserByEmail(email) {
  try {
    // Provjera u employers kolekciji
    const employersQuery = query(collection(db, 'employers'), where('email', '==', email));
    const employersSnapshot = await getDocs(employersQuery);
    
    if (!employersSnapshot.empty) {
      const employer = employersSnapshot.docs[0].data();
      console.log('Korisnik pronađen u employers kolekciji:');
      console.log('UID:', employersSnapshot.docs[0].id);
      console.log('Email:', employer.email);
      console.log('Uloga:', employer.role);
      return { id: employersSnapshot.docs[0].id, ...employer };
    }
    
    // Provjera u candidates kolekciji
    const candidatesQuery = query(collection(db, 'candidates'), where('email', '==', email));
    const candidatesSnapshot = await getDocs(candidatesQuery);
    
    if (!candidatesSnapshot.empty) {
      const candidate = candidatesSnapshot.docs[0].data();
      console.log('Korisnik pronađen u candidates kolekciji:');
      console.log('UID:', candidatesSnapshot.docs[0].id);
      console.log('Email:', candidate.email);
      console.log('Uloga:', candidate.role);
      return { id: candidatesSnapshot.docs[0].id, ...candidate };
    }
    
    console.log('Korisnik s email adresom', email, 'nije pronađen.');
    return null;
  } catch (error) {
    console.error('Greška prilikom traženja korisnika:', error);
    return null;
  }
}

async function createAdminUser(email) {
  try {
    const user = await findUserByEmail(email);
    
    if (!user) {
      console.error('Korisnik nije pronađen. Ne mogu kreirati admin korisnika.');
      return;
    }
    
    // Kreiranje admin dokumenta
    const adminData = {
      uid: user.id,
      email: user.email,
      role: 'admin',
      name: user.companyName || user.firstName || 'Admin User',
      permissions: {
        users: true,
        content: true,
        settings: true,
        finance: true,
        superAdmin: true
      },
      createdAt: new Date(),
    };
    
    // Dodavanje u admins kolekciju
    await setDoc(doc(db, 'admins', user.id), adminData);
    
    console.log('Admin korisnik uspješno kreiran!');
    console.log('Možete se prijaviti s email adresom', email, 'i pristupiti admin panelu na /admin URL-u.');
  } catch (error) {
    console.error('Greška prilikom kreiranja admin korisnika:', error);
  }
}

// Email korisnika kojeg želimo postaviti kao admina
const userEmail = '<EMAIL>';

// Poziv funkcije za kreiranje admin korisnika
createAdminUser(userEmail).then(() => {
  console.log('Proces završen.');
}).catch(error => {
  console.error('Greška:', error);
});
