# BalkanWorkForce - Plan implementacije višejezičnosti i prevoditeljskih usluga

## 1. Pregled

Ovaj dokument opisuje plan implementacije višejezičnosti i prevoditeljskih usluga za BalkanWorkForce platformu, kao i poboljšanja strukture radnog iskustva i edukacije kandidata. Cilj je omogućiti efikasnu komunikaciju između poslodavaca iz EU (prvenstveno Njemačke) i kandidata iz balkanskih zemalja, prevladavajući jezične barijere.

## 2. Ključne funkcionalnosti

### 2.1. Višejezičnost korisničkog sučelja

- Implementacija podrške za više jezika u korisničkom sučelju (UI)
- Početna podrška za sljedeće jezike:
  - <PERSON><PERSON><PERSON><PERSON><PERSON> (primarni za poslodavce)
  - Engleski (univerzalni)
  - Bosanski/Hrvatski/Srpski (za kandidate)
  - Albanski (za kandidate)
  - Makedonski (za kandidate)
- Automatska detekcija jezika na osnovu lokacije korisnika
- Mogućnost ručnog odabira jezika

### 2.2. Prevođenje oglasa za poslove

- Automatsko prevođenje oglasa za poslove na više jezika
- Očuvanje originalnog teksta uz prijevode
- Označavanje izvornog jezika oglasa
- Mogućnost za poslodavce da odaberu na koje jezike žele prevesti oglas
- Sistem kredita za prevođenje kao premium funkcionalnost

### 2.3. Prevođenje chata u stvarnom vremenu

- Automatsko prevođenje poruka između korisnika koji govore različite jezike
- Označavanje izvornog jezika poruke
- Prikaz originalnog teksta uz prijevod
- Mogućnost uključivanja/isključivanja automatskog prevođenja

### 2.4. Standardizacija profesija i pozicija

- Kreiranje baze standardiziranih profesija i pozicija na više jezika
- Mapiranje ekvivalentnih pozicija između jezika (npr. "Elektriker" = "Električar" = "Electrician")
- Implementacija pretrage koja radi preko jezičnih barijera

### 2.5. Standardizacija obrazovnih stupnjeva

- Kreiranje baze standardiziranih obrazovnih stupnjeva na više jezika
- Mapiranje ekvivalentnih stupnjeva između različitih obrazovnih sistema
- Podrška za različite formate obrazovnih kvalifikacija

## 3. Tehnička implementacija

### 3.1. Struktura podataka

#### 3.1.1. Kandidati

```typescript
// Dodati u Candidate interfejs
country: string; // ISO kod zemlje (npr. "BA", "RS", "HR")
preferredLanguage: string; // Preferirani jezik korisničkog sučelja

// Strukturirano radno iskustvo
workExperience: {
  company: string;
  position: string;
  positionId?: string; // Referenca na standardiziranu poziciju
  startDate: Date; // Puni datum (mjesec/godina)
  endDate?: Date; // Puni datum ili undefined za trenutni posao
  current: boolean;
  description?: string;
  country?: string; // Zemlja u kojoj je radio
}[];

// Strukturirana edukacija
education: {
  institution: string;
  degree: string;
  degreeId?: string; // Referenca na standardizirani stupanj
  field: string;
  startDate: Date; // Puni datum (mjesec/godina)
  endDate?: Date; // Puni datum ili undefined za trenutno obrazovanje
  current: boolean;
  description?: string;
  country?: string; // Zemlja obrazovne institucije
}[];
```

#### 3.1.2. Poslovi

```typescript
// Dodati u JobListing interfejs
originalLanguage: string; // Izvorni jezik oglasa (npr. "de", "en")
translations: {
  [languageCode: string]: {
    title: string;
    description: string;
    requirements: string[];
    translatedAt: Date;
  }
};
```

#### 3.1.3. Poruke

```typescript
// Dodati u Message interfejs
originalLanguage: string; // Izvorni jezik poruke
translations: {
  [languageCode: string]: {
    content: string;
    translatedAt: Date;
  }
};
```

#### 3.1.4. Nove kolekcije

```typescript
// Standardizirane pozicije
interface Position {
  id: string;
  translations: {
    [languageCode: string]: string; // npr. { "de": "Elektriker", "en": "Electrician", "bs": "Električar" }
  };
  category: string; // Kategorija pozicije (npr. "construction", "healthcare")
  skills?: string[]; // Povezane vještine
}

// Standardizirani obrazovni stupnjevi
interface Degree {
  id: string;
  translations: {
    [languageCode: string]: string; // npr. { "de": "Bachelor", "en": "Bachelor's Degree", "bs": "Bakalaureat" }
  };
  level: string; // Nivo obrazovanja (npr. "bachelor", "master", "phd")
  equivalentTo?: string[]; // ID-ovi ekvivalentnih stupnjeva u drugim sistemima
}

// Prijevodi
interface Translation {
  id: string;
  contentHash: string; // Hash originalnog sadržaja
  originalLanguage: string;
  originalContent: string;
  translations: {
    [languageCode: string]: {
      content: string;
      translatedAt: Date;
      model?: string; // Model korišten za prevođenje
    }
  };
  type: "job" | "message" | "profile"; // Tip sadržaja
  referenceId: string; // ID povezanog entiteta (posao, poruka, itd.)
  employerId?: string; // ID poslodavca (za praćenje korištenja)
}

// Krediti za prevođenje
interface TranslationCredit {
  id: string;
  employerId: string;
  totalCredits: number;
  remainingCredits: number;
  purchasedAt: Date;
  expiresAt?: Date;
  packageId: string; // Referenca na paket
}
```

### 3.2. API za prevođenje

#### 3.2.1. OpenAI API integracija

```typescript
// Servis za prevođenje
interface TranslationService {
  translateText(
    text: string,
    sourceLanguage: string,
    targetLanguage: string,
    context?: string
  ): Promise<string>;
  
  translateJob(
    job: JobListing,
    targetLanguages: string[]
  ): Promise<Record<string, JobTranslation>>;
  
  translateMessage(
    message: string,
    sourceLanguage: string,
    targetLanguage: string
  ): Promise<string>;
  
  detectLanguage(text: string): Promise<string>;
}

// Implementacija s OpenAI
class OpenAITranslationService implements TranslationService {
  private apiKey: string;
  private model: string = "gpt-4";
  
  constructor(apiKey: string) {
    this.apiKey = apiKey;
  }
  
  async translateText(
    text: string,
    sourceLanguage: string,
    targetLanguage: string,
    context?: string
  ): Promise<string> {
    // Implementacija s OpenAI API
    // Koristi context za bolje prijevode specifične za domenu
  }
  
  // Ostale metode...
}
```

#### 3.2.2. Keširanje prijevoda

```typescript
// Servis za keširanje prijevoda
interface TranslationCacheService {
  getTranslation(
    contentHash: string,
    targetLanguage: string
  ): Promise<string | null>;
  
  saveTranslation(
    contentHash: string,
    sourceLanguage: string,
    targetLanguage: string,
    originalContent: string,
    translatedContent: string
  ): Promise<void>;
  
  invalidateTranslation(contentHash: string): Promise<void>;
}

// Implementacija s Firebase
class FirebaseTranslationCache implements TranslationCacheService {
  private db: Firestore;
  
  constructor(db: Firestore) {
    this.db = db;
  }
  
  // Implementacija metoda...
}
```

### 3.3. Korisničko sučelje

#### 3.3.1. Odabir jezika

- Implementacija dropdown menija za odabir jezika u navigaciji
- Spremanje preferiranog jezika u localStorage i profil korisnika
- Automatsko učitavanje prijevoda UI-a na osnovu odabranog jezika

#### 3.3.2. Prevođenje oglasa

- Dodavanje opcije za prevođenje u formi za kreiranje/uređivanje oglasa
- Prikaz dostupnih jezika za prevođenje
- Prikaz troškova prevođenja (krediti)
- Pregled prijevoda prije objavljivanja

#### 3.3.3. Chat s prevođenjem

- Indikator izvornog jezika poruke
- Opcija za prikaz originalnog teksta
- Automatsko prevođenje dolaznih poruka
- Indikator da je poruka prevedena

#### 3.3.4. Strukturirano radno iskustvo i edukacija

- Poboljšana forma za unos radnog iskustva s poljima za mjesec/godinu
- Poboljšana forma za unos edukacije s poljima za mjesec/godinu
- Dropdown za odabir standardiziranih pozicija i stupnjeva
- Automatsko računanje ukupnog radnog iskustva po poziciji

## 4. Plan implementacije

### 4.1. Faza 1: Priprema podataka

1. Kreirati kolekcije za standardizirane pozicije i stupnjeve
2. Popuniti bazu s početnim setom pozicija i stupnjeva na više jezika
3. Ažurirati model podataka za kandidate, poslove i poruke
4. Implementirati migraciju postojećih podataka

### 4.2. Faza 2: Osnovni UI prijevodi

1. Implementirati sistem za učitavanje prijevoda UI-a
2. Kreirati prijevode za ključne stranice (početna, prijava, registracija)
3. Implementirati odabir jezika u navigaciji
4. Dodati automatsku detekciju jezika

### 4.3. Faza 3: Strukturirano radno iskustvo i edukacija

1. Ažurirati forme za unos radnog iskustva i edukacije
2. Implementirati odabir standardiziranih pozicija i stupnjeva
3. Dodati validaciju datuma i računanje ukupnog iskustva
4. Ažurirati prikaz profila kandidata

### 4.4. Faza 4: Prevođenje oglasa

1. Implementirati OpenAI API integraciju za prevođenje
2. Kreirati sistem za keširanje prijevoda
3. Dodati opcije za prevođenje u formi za oglase
4. Implementirati prikaz oglasa na preferiranom jeziku korisnika

### 4.5. Faza 5: Prevođenje chata

1. Ažurirati model podataka za poruke
2. Implementirati automatsko prevođenje poruka
3. Dodati indikatore jezika i opcije za prikaz originala
4. Implementirati praćenje korištenja prijevoda

### 4.6. Faza 6: Sistem kredita za prevođenje

1. Kreirati pakete kredita za prevođenje
2. Implementirati praćenje korištenja kredita
3. Dodati obavijesti o niskom stanju kredita
4. Integrirati s postojećim sistemom plaćanja

## 5. Testiranje

### 5.1. Testiranje prijevoda

- Testirati kvalitetu prijevoda za različite jezične parove
- Provjeriti očuvanje formatiranja i strukture
- Testirati prijevod tehničkih termina i specifičnog vokabulara

### 5.2. Testiranje performansi

- Mjeriti vrijeme odziva za prijevode različitih veličina
- Testirati efikasnost keširanja
- Provjeriti opterećenje baze podataka

### 5.3. Testiranje korisničkog iskustva

- Testirati intuitivnost sučelja za odabir jezika
- Provjeriti jasnoću indikatora prevedenog sadržaja
- Testirati proces unosa strukturiranog radnog iskustva i edukacije

## 6. Zaključak

Implementacija višejezičnosti i prevoditeljskih usluga značajno će poboljšati korisničko iskustvo i funkcionalnost BalkanWorkForce platforme. Strukturirano radno iskustvo i edukacija omogućit će preciznije podudaranje kandidata s poslovima, dok će standardizacija pozicija i stupnjeva olakšati pretragu preko jezičnih barijera.

Ova implementacija zahtijeva značajne promjene u modelu podataka i korisničkom sučelju, ali će rezultirati platformom koja efikasno povezuje poslodavce i kandidate bez obzira na jezične razlike.
