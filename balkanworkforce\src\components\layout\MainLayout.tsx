"use client";

import React from "react";
import { useAuth } from "@/contexts/AuthContext";
import Link from "next/link";

interface MainLayoutProps {
  children: React.ReactNode;
}

export default function MainLayout({ children }: MainLayoutProps) {
  const { user, logout } = useAuth();

  return (
    <div className="min-h-screen flex flex-col">
      <header className="bg-neutral-900 text-white">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <Link href="/" className="text-xl font-bold">
            BalkanWorkForce
          </Link>
          <nav className="flex items-center gap-6">
            <Link href="/jobs" className="hover:text-neutral-300">
              Jobs
            </Link>
            {user ? (
              <>
                {user.role === "employer" && (
                  <>
                    <Link href="/employer/dashboard" className="hover:text-neutral-300">
                      Dashboard
                    </Link>
                    <Link href="/employer/profile" className="hover:text-neutral-300">
                      Company Profile
                    </Link>
                    <Link href="/employer/candidates" className="hover:text-neutral-300">
                      Browse Candidates
                    </Link>
                    <Link href="/employer/applications" className="hover:text-neutral-300">
                      Applications
                    </Link>
                    <Link href="/settings" className="hover:text-neutral-300">
                      Settings
                    </Link>
                  </>
                )}
                {user.role === "candidate" && (
                  <>
                    <Link href="/candidate/dashboard" className="hover:text-neutral-300">
                      Dashboard
                    </Link>
                    <Link href="/candidate/profile" className="hover:text-neutral-300">
                      My Profile
                    </Link>
                    <Link href="/candidate/applications" className="hover:text-neutral-300">
                      My Applications
                    </Link>
                    <Link href="/settings" className="hover:text-neutral-300">
                      Settings
                    </Link>
                  </>
                )}
                <button
                  onClick={() => logout()}
                  className="bg-neutral-700 hover:bg-neutral-600 px-4 py-2 rounded"
                >
                  Logout
                </button>
              </>
            ) : (
              <Link
                href="/auth"
                className="bg-neutral-700 hover:bg-neutral-600 px-4 py-2 rounded"
              >
                Sign In
              </Link>
            )}
          </nav>
        </div>
      </header>
      <main className="flex-grow container mx-auto px-4 py-8">{children}</main>
      <footer className="bg-neutral-900 text-white py-8">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <div>
              <h3 className="text-lg font-semibold mb-4">BalkanWorkForce</h3>
              <p className="text-neutral-400">
                Connecting Balkan workers with German and EU companies.
              </p>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Quick Links</h3>
              <ul className="space-y-2">
                <li>
                  <Link href="/jobs" className="text-neutral-400 hover:text-white">
                    Browse Jobs
                  </Link>
                </li>
                <li>
                  <Link href="/about" className="text-neutral-400 hover:text-white">
                    About Us
                  </Link>
                </li>
                <li>
                  <Link href="/contact" className="text-neutral-400 hover:text-white">
                    Contact
                  </Link>
                </li>
              </ul>
            </div>
            <div>
              <h3 className="text-lg font-semibold mb-4">Contact</h3>
              <address className="text-neutral-400 not-italic">
                Email: <EMAIL><br />
                Phone: +49 123 456 789
              </address>
            </div>
          </div>
          <div className="border-t border-neutral-800 mt-8 pt-8 text-center text-neutral-400">
            <p>&copy; {new Date().getFullYear()} BalkanWorkForce. All rights reserved.</p>
          </div>
        </div>
      </footer>
    </div>
  );
}
