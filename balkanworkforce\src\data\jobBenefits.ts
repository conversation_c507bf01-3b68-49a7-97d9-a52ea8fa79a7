// Job benefits/features that employers can select when creating a job
// and candidates can select as preferences

export const jobBenefits = [
  {
    id: "flexible_hours",
    label: "Flexible Working Hours",
    description: "Flexible start and end times or the ability to work remotely"
  },
  {
    id: "remote_work",
    label: "Remote Work Options",
    description: "Possibility to work remotely part-time or full-time"
  },
  {
    id: "health_insurance",
    label: "Health Insurance",
    description: "Comprehensive health insurance coverage"
  },
  {
    id: "paid_vacation",
    label: "Paid Vacation",
    description: "Generous paid time off and vacation days"
  },
  {
    id: "professional_development",
    label: "Professional Development",
    description: "Training, courses, and career advancement opportunities"
  },
  {
    id: "retirement_plan",
    label: "Retirement Plan",
    description: "Company pension or retirement savings plan"
  },
  {
    id: "relocation_assistance",
    label: "Relocation Assistance",
    description: "Help with moving expenses and finding accommodation"
  },
  {
    id: "accommodation_provided_free",
    label: "Free Accommodation Provided",
    description: "Accommodation is provided and fully paid by the employer"
  },
  {
    id: "accommodation_provided_paid",
    label: "Accommodation Provided (Paid)",
    description: "Accommodation is arranged by the employer but paid by the employee"
  },
  {
    id: "accommodation_not_provided",
    label: "Accommodation Not Provided",
    description: "Employee must arrange their own accommodation"
  },
  {
    id: "accommodation_assistance",
    label: "Accommodation Finding Assistance",
    description: "Employer assists in finding accommodation but does not provide it directly"
  },
  {
    id: "language_courses",
    label: "Language Courses",
    description: "Free or subsidized language training"
  },
  {
    id: "childcare",
    label: "Childcare Support",
    description: "On-site childcare or childcare subsidies"
  },
  {
    id: "gym_membership",
    label: "Gym Membership",
    description: "Free or discounted gym or wellness program"
  },
  {
    id: "company_car",
    label: "Company Car",
    description: "Company vehicle or car allowance"
  },
  {
    id: "meal_allowance",
    label: "Meal Allowance",
    description: "Free or subsidized meals or meal vouchers"
  },
  {
    id: "performance_bonus",
    label: "Performance Bonuses",
    description: "Financial bonuses based on performance"
  },
  {
    id: "housing_allowance",
    label: "Housing Allowance",
    description: "Assistance with housing costs"
  },
  {
    id: "public_transport",
    label: "Public Transport Subsidy",
    description: "Free or subsidized public transportation"
  }
];

// Minimum number of benefits that must be selected when creating a job
export const MIN_BENEFITS_REQUIRED = 3;
