"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useLanguage } from "@/contexts/LanguageContext";
import { useRouter } from "next/navigation";
import { doc, updateDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { languages, Language } from "@/translations";
import Link from "next/link";

export default function SettingsPage() {
  const { user, loading: authLoading } = useAuth();
  const { language, setLanguage, t } = useLanguage();
  const router = useRouter();
  const [saving, setSaving] = useState(false);
  const [success, setSuccess] = useState(false);
  const [selectedLanguage, setSelectedLanguage] = useState<Language>(language);

  useEffect(() => {
    // If not authenticated, redirect to auth page
    if (!authLoading && !user) {
      router.push("/auth");
    }
  }, [user, authLoading, router]);

  const handleLanguageChange = async (newLanguage: Language) => {
    setSelectedLanguage(newLanguage);
    setSaving(true);
    setSuccess(false);

    try {
      // Update user's preferred language in Firestore
      if (user) {
        const userRef = doc(db, user.role === 'employer' ? 'employers' : 'candidates', user.uid);
        await updateDoc(userRef, {
          preferredLanguage: newLanguage,
          updatedAt: new Date(),
        });
      }

      // Update language in context
      setLanguage(newLanguage);
      setSuccess(true);
    } catch (error) {
      console.error("Error updating language preference:", error);
    } finally {
      setSaving(false);
    }
  };

  if (authLoading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto py-12">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">{t('nav.settings')}</h1>
        <Link
          href={user?.role === "employer" ? "/employer/dashboard" : "/candidate/dashboard"}
          className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
        >
          {t('nav.dashboard')}
        </Link>
      </div>

      {success && (
        <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 rounded-md">
          {t('settings.languageUpdated')}
        </div>
      )}

      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8">
        <h2 className="text-xl font-semibold mb-6">{t('settings.language')}</h2>
        
        <div className="mb-6">
          <label className="block text-sm font-medium mb-2">
            {t('settings.selectLanguage')}
          </label>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {languages.map((lang) => (
              <button
                key={lang.code}
                onClick={() => handleLanguageChange(lang.code as Language)}
                className={`p-4 border rounded-md flex items-center justify-between ${
                  selectedLanguage === lang.code
                    ? "border-neutral-900 dark:border-white bg-neutral-50 dark:bg-neutral-700"
                    : "border-neutral-200 dark:border-neutral-700 hover:border-neutral-400 dark:hover:border-neutral-500"
                }`}
                disabled={saving}
              >
                <span>{lang.name}</span>
                {selectedLanguage === lang.code && (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                )}
              </button>
            ))}
          </div>
          <p className="mt-2 text-sm text-neutral-500 dark:text-neutral-400">
            {t('settings.languageDescription')}
          </p>
        </div>
      </div>
    </div>
  );
}
