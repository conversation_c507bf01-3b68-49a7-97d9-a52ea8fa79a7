"use client";

import { useState, useEffect } from "react";
import { collection, query, getDocs, orderBy, doc, updateDoc, deleteDoc, setDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { DriverLicense } from "@/data/driverLicenses";

export default function AdminLicensesPage() {
  const [licenses, setLicenses] = useState<DriverLicense[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [editingLicense, setEditingLicense] = useState<DriverLicense | null>(null);
  const [newLicense, setNewLicense] = useState<Partial<DriverLicense>>({
    id: "",
    name: "",
    description: "",
    translations: { 
      en: { name: "", description: "" },
      de: { name: "", description: "" },
      bs: { name: "", description: "" }
    },
    category: "car",
  });
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const supportedLanguages = [
    { code: "en", name: "English" },
    { code: "de", name: "German" },
    { code: "bs", name: "Bosnian" },
  ];

  const categories = [
    { id: "car", name: "Car" },
    { id: "motorcycle", name: "Motorcycle" },
    { id: "truck", name: "Truck" },
    { id: "bus", name: "Bus" },
    { id: "special", name: "Special" },
    { id: "none", name: "None" },
  ];

  useEffect(() => {
    const fetchLicenses = async () => {
      try {
        const licensesQuery = query(
          collection(db, "driverLicenses"),
          orderBy("category", "asc")
        );
        const snapshot = await getDocs(licensesQuery);
        const licensesData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        })) as DriverLicense[];
        setLicenses(licensesData);
      } catch (error) {
        console.error("Error fetching driver licenses:", error);
        setError("Failed to load driver licenses. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchLicenses();
  }, []);

  const filteredLicenses = licenses.filter(license => {
    const searchLower = searchTerm.toLowerCase();
    return (
      license.id.toLowerCase().includes(searchLower) ||
      license.name.toLowerCase().includes(searchLower) ||
      license.description.toLowerCase().includes(searchLower) ||
      license.category.toLowerCase().includes(searchLower) ||
      Object.values(license.translations).some(translation => 
        translation.name.toLowerCase().includes(searchLower) ||
        translation.description.toLowerCase().includes(searchLower)
      )
    );
  });

  const handleEditLicense = (license: DriverLicense) => {
    setEditingLicense(license);
    setError(null);
  };

  const handleCancelEdit = () => {
    setEditingLicense(null);
    setError(null);
  };

  const handleUpdateLicense = async () => {
    if (!editingLicense) return;

    // Validate
    if (!editingLicense.name || !editingLicense.description || !editingLicense.category) {
      setError("Name, description, and category are required");
      return;
    }

    // Check if at least one translation is provided
    const hasTranslation = Object.values(editingLicense.translations).some(
      t => t.name.trim() !== "" && t.description.trim() !== ""
    );
    if (!hasTranslation) {
      setError("At least one complete translation is required");
      return;
    }

    try {
      await updateDoc(doc(db, "driverLicenses", editingLicense.id), {
        name: editingLicense.name,
        description: editingLicense.description,
        translations: editingLicense.translations,
        category: editingLicense.category,
        updatedAt: new Date(),
      });

      // Update local state
      setLicenses(licenses.map(l => 
        l.id === editingLicense.id ? editingLicense : l
      ));
      
      setEditingLicense(null);
      setSuccess("Driver license updated successfully");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Error updating driver license:", error);
      setError("Failed to update driver license. Please try again.");
    }
  };

  const handleDeleteLicense = async (licenseId: string) => {
    if (!confirm("Are you sure you want to delete this driver license? This action cannot be undone.")) {
      return;
    }

    try {
      await deleteDoc(doc(db, "driverLicenses", licenseId));
      
      // Update local state
      setLicenses(licenses.filter(l => l.id !== licenseId));
      setSuccess("Driver license deleted successfully");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Error deleting driver license:", error);
      setError("Failed to delete driver license. Please try again.");
    }
  };

  const handleAddNewLicense = () => {
    setIsAddingNew(true);
    setNewLicense({
      id: "",
      name: "",
      description: "",
      translations: { 
        en: { name: "", description: "" },
        de: { name: "", description: "" },
        bs: { name: "", description: "" }
      },
      category: "car",
    });
    setError(null);
  };

  const handleCancelAdd = () => {
    setIsAddingNew(false);
    setError(null);
  };

  const handleCreateLicense = async () => {
    // Validate
    if (!newLicense.id || !newLicense.id.trim()) {
      setError("License ID is required");
      return;
    }

    if (!newLicense.name || !newLicense.description || !newLicense.category) {
      setError("Name, description, and category are required");
      return;
    }

    // Check if at least one translation is provided
    const hasTranslation = Object.values(newLicense.translations || {}).some(
      t => t.name.trim() !== "" && t.description.trim() !== ""
    );
    if (!hasTranslation) {
      setError("At least one complete translation is required");
      return;
    }

    // Check if ID already exists
    if (licenses.some(l => l.id === newLicense.id)) {
      setError("A driver license with this ID already exists");
      return;
    }

    try {
      const licenseData: DriverLicense = {
        id: newLicense.id,
        name: newLicense.name || "",
        description: newLicense.description || "",
        translations: newLicense.translations || { 
          en: { name: "", description: "" },
          de: { name: "", description: "" },
          bs: { name: "", description: "" }
        },
        category: newLicense.category as "car" | "motorcycle" | "truck" | "bus" | "special" | "none",
      };

      await setDoc(doc(db, "driverLicenses", newLicense.id), {
        ...licenseData,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Update local state
      setLicenses([...licenses, licenseData]);
      setIsAddingNew(false);
      setSuccess("Driver license created successfully");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Error creating driver license:", error);
      setError("Failed to create driver license. Please try again.");
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Driver's License Management</h1>
        <button
          onClick={handleAddNewLicense}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
        >
          Add New License
        </button>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 p-4 rounded-md mb-6">
          {success}
        </div>
      )}

      {/* Search */}
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <label htmlFor="search" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Search Driver's Licenses
            </label>
            <input
              type="text"
              id="search"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by ID, name, or description"
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
            />
          </div>
        </div>
      </div>

      {/* Add New License Form */}
      {isAddingNew && (
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-lg font-medium mb-4">Add New Driver's License</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                License ID <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={newLicense.id || ""}
                onChange={(e) => setNewLicense({...newLicense, id: e.target.value})}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                placeholder="e.g., b, c1, am"
              />
              <p className="text-xs text-neutral-500 mt-1">
                Use lowercase letters and numbers only. This ID will be used in the database.
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                Category <span className="text-red-500">*</span>
              </label>
              <select
                value={newLicense.category || "car"}
                onChange={(e) => setNewLicense({...newLicense, category: e.target.value})}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              >
                {categories.map(category => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                Name <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={newLicense.name || ""}
                onChange={(e) => setNewLicense({...newLicense, name: e.target.value})}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                placeholder="e.g., B, C1, AM"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                Description <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={newLicense.description || ""}
                onChange={(e) => setNewLicense({...newLicense, description: e.target.value})}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                placeholder="e.g., Cars and light vans"
              />
            </div>
          </div>
          
          <h3 className="text-md font-medium mb-2">Translations</h3>
          {supportedLanguages.map(language => (
            <div key={language.code} className="mb-4 border border-neutral-200 dark:border-neutral-700 rounded-md p-4">
              <h4 className="text-sm font-medium mb-2">{language.name}</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <label className="block text-sm text-neutral-700 dark:text-neutral-300 mb-1">
                    Name
                  </label>
                  <input
                    type="text"
                    value={newLicense.translations?.[language.code]?.name || ""}
                    onChange={(e) => {
                      const updatedTranslations = { 
                        ...newLicense.translations,
                        [language.code]: {
                          ...newLicense.translations?.[language.code],
                          name: e.target.value 
                        }
                      };
                      setNewLicense({...newLicense, translations: updatedTranslations});
                    }}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                    placeholder={`${language.name} name`}
                  />
                </div>
                <div>
                  <label className="block text-sm text-neutral-700 dark:text-neutral-300 mb-1">
                    Description
                  </label>
                  <input
                    type="text"
                    value={newLicense.translations?.[language.code]?.description || ""}
                    onChange={(e) => {
                      const updatedTranslations = { 
                        ...newLicense.translations,
                        [language.code]: {
                          ...newLicense.translations?.[language.code],
                          description: e.target.value 
                        }
                      };
                      setNewLicense({...newLicense, translations: updatedTranslations});
                    }}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                    placeholder={`${language.name} description`}
                  />
                </div>
              </div>
            </div>
          ))}
          
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleCancelAdd}
              className="px-4 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-700"
            >
              Cancel
            </button>
            <button
              onClick={handleCreateLicense}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
            >
              Create License
            </button>
          </div>
        </div>
      )}

      {/* Driver Licenses Table */}
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-neutral-200 dark:divide-neutral-700">
            <thead className="bg-neutral-50 dark:bg-neutral-900">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Description
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Category
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-neutral-800 divide-y divide-neutral-200 dark:divide-neutral-700">
              {filteredLicenses.map((license) => (
                <tr key={license.id}>
                  {editingLicense && editingLicense.id === license.id ? (
                    // Edit mode
                    <>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-neutral-900 dark:text-white">
                          {license.id}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="text"
                          value={editingLicense.name}
                          onChange={(e) => setEditingLicense({...editingLicense, name: e.target.value})}
                          className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-sm"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="text"
                          value={editingLicense.description}
                          onChange={(e) => setEditingLicense({...editingLicense, description: e.target.value})}
                          className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-sm"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <select
                          value={editingLicense.category}
                          onChange={(e) => setEditingLicense({
                            ...editingLicense, 
                            category: e.target.value as "car" | "motorcycle" | "truck" | "bus" | "special" | "none"
                          })}
                          className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-sm"
                        >
                          {categories.map(category => (
                            <option key={category.id} value={category.id}>
                              {category.name}
                            </option>
                          ))}
                        </select>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={handleCancelEdit}
                          className="text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white mr-3"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={handleUpdateLicense}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
                        >
                          Save
                        </button>
                      </td>
                    </>
                  ) : (
                    // View mode
                    <>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-neutral-900 dark:text-white">
                          {license.id}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-neutral-500 dark:text-neutral-400">
                          {license.name}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-neutral-500 dark:text-neutral-400">
                          {license.description}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-neutral-500 dark:text-neutral-400">
                          {categories.find(c => c.id === license.category)?.name || license.category}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleEditLicense(license)}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-3"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDeleteLicense(license.id)}
                          className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                        >
                          Delete
                        </button>
                      </td>
                    </>
                  )}
                </tr>
              ))}
              {filteredLicenses.length === 0 && (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-neutral-500 dark:text-neutral-400">
                    No driver licenses found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
