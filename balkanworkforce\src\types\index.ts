export type UserRole = "employer" | "candidate" | "admin";

export interface User {
  uid: string;
  email: string | null;
  role: UserRole | null;
  displayName?: string | null;
  photoURL?: string | null;
  createdAt?: Date;
}

export interface Employer extends User {
  role: "employer";
  companyName?: string;
  companyDescription?: string;
  industry?: string;
  location?: string;
  website?: string;
  contactPerson?: string;
  contactEmail?: string;
  contactPhone?: string;
  preferences?: string[];
}

export interface Admin extends User {
  role: "admin";
  name?: string;
  permissions?: {
    users?: boolean;
    content?: boolean;
    settings?: boolean;
    finance?: boolean;
    superAdmin?: boolean;
  };
  lastLogin?: Date;
}

export interface WorkExperience {
  company: string;
  position: string;
  positionId?: string; // Referenca na standardiziranu poziciju
  startDate: Date;
  endDate?: Date;
  current?: boolean;
  description?: string;
  country?: string; // Zemlja u kojoj je radio
  industry?: string; // Industrija/sektor
}

export interface Education {
  degree: string;
  degreeId?: string; // Referenca na standardizirani stupanj
  field: string;
  fieldId?: string; // Referenca na standardizirano polje studija
  startDate: Date;
  endDate?: Date;
  current?: boolean;
  description?: string;
  country?: string; // Zemlja obrazovne institucije
}

export type LanguageLevel = "None" | "A1" | "A2" | "B1" | "B2" | "C1" | "C2" | "Native";

export interface LanguageSkill {
  language: string;
  level: LanguageLevel;
}

export type Gender = "male" | "female" | "other" | "prefer-not-to-say";

export interface Candidate extends User {
  role: "candidate";
  profession: string;
  age: number;
  gender: Gender;
  country?: string; // ISO kod zemlje (npr. "BA", "RS", "HR")
  preferredLanguage?: string; // Preferirani jezik korisničkog sučelja
  workExperience: WorkExperience[];
  noWorkExperience?: boolean; // For candidates with no work experience
  education: Education[];
  germanLanguageLevel: LanguageLevel;
  englishLanguageLevel: LanguageLevel;
  otherLanguages?: LanguageSkill[];
  desiredCountries?: string[]; // Countries where the candidate wants to work
  benefits?: string[]; // Selected job benefits/features for matching
  driverLicense?: string; // ID kategorije vozačke dozvole (npr. "b", "c", "ce")
  profileCompleted?: boolean; // Flag to indicate if profile is complete
  // The name will be hidden and represented as "Profession, Age"
  // e.g., "Electrician, 29" or "KFZ-Mechaniker, 22"
}

export interface JobRequirements {
  // Jezične vještine
  germanLanguageLevel?: LanguageLevel; // Minimalni nivo njemačkog jezika
  englishLanguageLevel?: LanguageLevel; // Minimalni nivo engleskog jezika
  otherLanguages?: { language: string; level: LanguageLevel }[]; // Ostali jezici

  // Radno iskustvo
  requiredPositions?: {
    positionId: string; // ID standardizirane pozicije
    minExperienceMonths: number; // Minimalno iskustvo u mjesecima
  }[];

  // Obrazovanje
  minEducationLevel?: string; // ID standardiziranog stupnja obrazovanja

  // Vozačka dozvola
  driverLicense?: string; // ID kategorije vozačke dozvole (npr. "b", "c", "ce")

  // Lokacija
  requiredCountries?: string[]; // Zemlje u kojima kandidat mora biti spreman raditi
}

export interface JobListing {
  id: string;
  employerId: string;
  title: string;
  description: string;
  requirements: string[];
  location: string;
  salary?: {
    min: number;
    max: number;
    currency: string;
  };
  employmentType: "full-time" | "part-time" | "contract" | "temporary" | "internship";
  isPremium: boolean;
  premiumUntil?: Date;
  premiumCountries?: string[]; // Array of country IDs where the job is promoted
  originalLanguage?: string; // Izvorni jezik oglasa (npr. "de", "en")
  translations?: {
    [languageCode: string]: {
      title: string;
      description: string;
      requirements: string[];
      translatedAt: Date;
    }
  };
  createdAt: Date;
  updatedAt: Date;
  tags: string[];
  benefits: string[]; // Array of benefit IDs
  applications: string[]; // Array of application IDs
  structuredRequirements?: JobRequirements; // Strukturirani zahtjevi za bolje matchiranje
}

export interface JobApplication {
  id: string;
  jobId: string;
  candidateId: string;
  employerId: string;
  status: "pending" | "reviewed" | "contacted" | "rejected" | "hired";
  coverLetter?: string;
  createdAt: Date;
  updatedAt: Date;
  matchPercentage?: number;
  notes?: string; // Employer notes about the application
  lastContactDate?: Date;
  // References to related entities for easier access
  jobTitle?: string;
  candidateProfession?: string;
  candidateAge?: number;
  employerCompanyName?: string;
}

export interface Country {
  id: string;
  name: string;
  code: string; // ISO country code
  flagUrl?: string;
  translations?: {
    [languageCode: string]: string; // npr. { "de": "Bosnien und Herzegowina", "en": "Bosnia and Herzegovina" }
  };
  languages?: string[]; // Službeni jezici
  region?: "balkans" | "eu" | "other"; // Regija
}

export interface PremiumPackage {
  id: string;
  name: string;
  duration: 7 | 14 | 30; // Days
  pricePerCountry: {
    7: number;
    14: number;
    30: number;
  };
  currency: string;
  description: string;
}

export interface JobPremiumSubscription {
  id: string;
  jobId: string;
  employerId: string;
  duration: 7 | 14 | 30; // Days
  countries: string[]; // Country IDs
  startDate: Date;
  endDate: Date;
  totalPrice: number;
  currency: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export type PackageType = 'job' | 'cv' | 'combined';

export interface CVPackage {
  id: string;
  name: string;
  type: PackageType;
  inviteCount: number;
  price: number;
  currency: string;
  description: string;
  // For combined packages
  jobDuration?: 7 | 14 | 30; // Days
  isPremiumJob?: boolean;
}

export interface EmployerSubscription {
  id: string;
  employerId: string;
  packageId: string;
  packageType: PackageType;
  startDate: Date;
  endDate?: Date; // For job packages
  invitesTotal: number; // For CV packages
  invitesRemaining: number; // For CV packages
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CandidateInvite {
  id: string;
  employerId: string;
  candidateId: string;
  jobId: string;
  message?: string;
  status: 'pending' | 'accepted' | 'declined' | 'expired';
  createdAt: Date;
  updatedAt: Date;
  expiresAt: Date;
  // References to related entities for easier access
  jobTitle?: string;
  employerCompanyName?: string;
  candidateProfession?: string;
  candidateAge?: number;
}

export interface Message {
  id: string;
  conversationId: string;
  senderId: string;
  content: string;
  createdAt: Date;
  read: boolean;
  originalLanguage?: string; // Izvorni jezik poruke
  translations?: {
    [languageCode: string]: {
      content: string;
      translatedAt: Date;
    }
  };
}

export interface Conversation {
  id: string;
  participants: string[]; // Array of user IDs
  lastMessage?: string;
  lastMessageDate?: Date;
  createdAt: Date;
  updatedAt: Date;
  // References to related entities for easier access
  employerId?: string;
  candidateId?: string;
  jobId?: string;
  employerCompanyName?: string;
  candidateProfession?: string;
  candidateAge?: number;
  jobTitle?: string;
}

export interface Position {
  id: string;
  translations: {
    [languageCode: string]: string; // npr. { "de": "Elektriker", "en": "Electrician", "bs": "Električar" }
  };
  category: string; // Kategorija pozicije (npr. "construction", "healthcare")
  skills?: string[]; // Povezane vještine
  aliases?: string[]; // Alternativni nazivi
}

export interface Degree {
  id: string;
  translations: {
    [languageCode: string]: string; // npr. { "de": "Bachelor", "en": "Bachelor's Degree", "bs": "Bakalaureat" }
  };
  level: string; // Nivo obrazovanja (npr. "bachelor", "master", "phd")
  equivalentTo?: string[]; // ID-ovi ekvivalentnih stupnjeva u drugim sistemima
}

export interface StudyField {
  id: string;
  translations: {
    [languageCode: string]: string; // npr. { "de": "Elektrotechnik", "en": "Electrical Engineering" }
  };
  category: string; // Kategorija polja (npr. "engineering", "vocational", "healthcare")
}

export interface Translation {
  id: string;
  contentHash: string; // Hash originalnog sadržaja
  originalLanguage: string;
  originalContent: string;
  translations: {
    [languageCode: string]: {
      content: string;
      translatedAt: Date;
      model?: string; // Model korišten za prevođenje
    }
  };
  type: "job" | "message" | "profile"; // Tip sadržaja
  referenceId: string; // ID povezanog entiteta (posao, poruka, itd.)
  employerId?: string; // ID poslodavca (za praćenje korištenja)
}

export interface TranslationCredit {
  id: string;
  employerId: string;
  totalCredits: number;
  remainingCredits: number;
  purchasedAt: Date;
  expiresAt?: Date;
  packageId: string; // Referenca na paket
}
