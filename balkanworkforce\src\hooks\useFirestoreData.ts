import { useState, useEffect } from "react";
import { collection, getDocs, query, orderBy } from "firebase/firestore";
import { db } from "@/lib/firebase";

export function useFirestoreData<T>(collectionName: string) {
  const [data, setData] = useState<T[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchData = async () => {
    try {
      setLoading(true);
      setError(null);
      
      // Try to fetch from Firestore first
      const q = query(collection(db, collectionName));
      const snapshot = await getDocs(q);
      
      if (snapshot.empty) {
        // If no data in Firestore, fall back to static data
        const staticData = await getStaticData<T>(collectionName);
        setData(staticData);
      } else {
        const items = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data()
        })) as T[];
        setData(items);
      }
    } catch (err) {
      console.error(`Error fetching ${collectionName}:`, err);
      
      // Fall back to static data on error
      try {
        const staticData = await getStaticData<T>(collectionName);
        setData(staticData);
      } catch (staticErr) {
        console.error(`Error loading static data for ${collectionName}:`, staticErr);
        setError(`Failed to load ${collectionName}`);
      }
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchData();
  }, [collectionName]);

  return { data, loading, error, refetch: fetchData };
}

// Helper function to get static data as fallback
async function getStaticData<T>(collectionName: string): Promise<T[]> {
  switch (collectionName) {
    case 'positions':
      const { positions } = await import('@/data/positions');
      return positions as T[];
    case 'studyFields':
      const { studyFields } = await import('@/data/studyFields');
      return studyFields as T[];
    case 'driverLicenses':
      const { driverLicenses } = await import('@/data/driverLicenses');
      return driverLicenses as T[];
    case 'benefits':
      const { jobBenefits } = await import('@/data/jobBenefits');
      return jobBenefits as T[];
    case 'countries':
      const { countries } = await import('@/data/countries');
      return countries as T[];
    default:
      return [];
  }
}
