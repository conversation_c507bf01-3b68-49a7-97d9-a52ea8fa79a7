module.exports = {

"[project]/src/data/positions.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_data_positions_ts_a11cb430._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/data/positions.ts [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/src/data/studyFields.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_data_studyFields_ts_4ffb691d._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/data/studyFields.ts [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/src/data/driverLicenses.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/data/driverLicenses.ts [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/src/data/jobBenefits.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/data/jobBenefits.ts [app-ssr] (ecmascript)");
    });
});
}}),
"[project]/src/data/countries.ts [app-ssr] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/src_data_countries_ts_906fcee9._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/data/countries.ts [app-ssr] (ecmascript)");
    });
});
}}),

};