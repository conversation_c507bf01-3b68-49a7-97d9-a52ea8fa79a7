"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { doc, getDoc, collection, addDoc, updateDoc, arrayUnion } from "firebase/firestore";
import { db } from "@/lib/firebase";
import Link from "next/link";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { isCandidateProfileComplete, getMissingProfileItems, hasMinimumProfileInfo } from "@/utils/profileUtils";
import { JobListing, Candidate } from "@/types";

// Form validation schema
const applicationSchema = z.object({
  coverLetter: z.string().min(50, "Cover letter should be at least 50 characters").max(2000, "Cover letter should not exceed 2000 characters"),
});

type ApplicationFormValues = z.infer<typeof applicationSchema>;

export default function JobApplicationPage() {
  const { id } = useParams();
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [job, setJob] = useState<JobListing | null>(null);
  const [candidateData, setCandidateData] = useState<Partial<Candidate> | null>(null);
  const [loading, setLoading] = useState(true);
  const [submitting, setSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);
  const [hasMinimumProfile, setHasMinimumProfile] = useState(false);
  const [profileComplete, setProfileComplete] = useState(false);
  const [missingItems, setMissingItems] = useState<string[]>([]);
  const [matchPercentage, setMatchPercentage] = useState<number | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<ApplicationFormValues>({
    resolver: zodResolver(applicationSchema),
    defaultValues: {
      coverLetter: "",
    },
  });

  useEffect(() => {
    // If not authenticated or not a candidate, redirect to auth page
    if (!authLoading && (!user || user.role !== "candidate")) {
      router.push("/auth");
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    const fetchData = async () => {
      if (!user || !id || typeof id !== "string") return;

      try {
        // Fetch job data
        const jobDoc = await getDoc(doc(db, "jobs", id));

        if (!jobDoc.exists()) {
          setError("Job not found");
          return;
        }

        const jobData = jobDoc.data() as Omit<JobListing, "id">;
        const jobWithId = {
          id: jobDoc.id,
          ...jobData,
          createdAt: jobData.createdAt.toDate(),
          updatedAt: jobData.updatedAt.toDate(),
          premiumUntil: jobData.premiumUntil ? jobData.premiumUntil.toDate() : undefined,
        };

        setJob(jobWithId);

        // Fetch candidate data
        const candidateDoc = await getDoc(doc(db, "candidates", user.uid));

        if (candidateDoc.exists()) {
          const candidateData = candidateDoc.data() as Partial<Candidate>;
          setCandidateData(candidateData);

          // Check if profile has minimum info
          const hasMinimum = hasMinimumProfileInfo(candidateData);
          setHasMinimumProfile(hasMinimum);

          // Check if profile is complete
          const isComplete = isCandidateProfileComplete(candidateData);
          setProfileComplete(isComplete);

          if (!isComplete) {
            setMissingItems(getMissingProfileItems(candidateData));
          }

          // Calculate match percentage if both job and candidate have benefits
          if (jobWithId.benefits && candidateData.benefits) {
            const candidateBenefits = candidateData.benefits;
            const jobBenefits = jobWithId.benefits;

            // Count matching benefits
            const matchingBenefits = candidateBenefits.filter(benefit =>
              jobBenefits.includes(benefit)
            ).length;

            // Calculate percentage based on candidate's preferences
            const percentage = Math.round((matchingBenefits / candidateBenefits.length) * 100);
            setMatchPercentage(percentage);
          }
        } else {
          setError("Candidate profile not found. Please create your profile first.");
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        setError("Failed to load job details. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    if (user && user.role === "candidate") {
      fetchData();
    }
  }, [user, id, router]);

  const onSubmit = async (data: ApplicationFormValues) => {
    if (!user || !job || !candidateData) return;

    // Double check profile completeness
    if (!profileComplete) {
      setError("Please complete your profile before applying");
      return;
    }

    setSubmitting(true);
    setError(null);

    try {
      // Create application document
      const applicationData = {
        jobId: job.id,
        candidateId: user.uid,
        employerId: job.employerId,
        status: "pending",
        coverLetter: data.coverLetter,
        createdAt: new Date(),
        updatedAt: new Date(),
        matchPercentage: matchPercentage || 0,
        jobTitle: job.title,
        candidateProfession: candidateData.profession,
        candidateAge: candidateData.age,
      };

      const applicationRef = await addDoc(collection(db, "applications"), applicationData);

      // Update job document with application ID
      await updateDoc(doc(db, "jobs", job.id), {
        applications: arrayUnion(applicationRef.id)
      });

      setSuccess(true);

      // Redirect to applications page after a short delay
      setTimeout(() => {
        router.push("/candidate/applications");
      }, 2000);
    } catch (error) {
      console.error("Error submitting application:", error);
      setError("Failed to submit application. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto py-12">
        <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6">
          {error}
        </div>
        <div className="flex gap-4">
          <Link
            href={`/jobs/${id}`}
            className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
          >
            Back to Job Details
          </Link>
          {!profileComplete && (
            <Link
              href="/candidate/profile"
              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            >
              Complete Your Profile
            </Link>
          )}
        </div>
      </div>
    );
  }

  // If the user doesn't have the minimum profile info (profession and age)
  if (!loading && !hasMinimumProfile) {
    return (
      <div className="max-w-4xl mx-auto py-12">
        <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6">
          You need to set up your basic profile information before you can apply for jobs.
        </div>
        <div className="flex gap-4">
          <Link
            href={`/jobs/${id}`}
            className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
          >
            Back to Job Details
          </Link>
          <Link
            href="/candidate/dashboard"
            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          >
            Set Up Your Profile
          </Link>
        </div>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="max-w-4xl mx-auto py-12">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Job Not Found</h2>
          <p className="text-neutral-600 dark:text-neutral-400 mb-6">
            The job you're looking for doesn't exist or has been removed.
          </p>
          <Link
            href="/jobs"
            className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
          >
            Browse Jobs
          </Link>
        </div>
      </div>
    );
  }

  if (success) {
    return (
      <div className="max-w-4xl mx-auto py-12">
        <div className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 p-6 rounded-md mb-6 text-center">
          <h2 className="text-2xl font-bold mb-2">Application Submitted!</h2>
          <p className="mb-4">
            Your application has been successfully submitted. You will be redirected to your applications page.
          </p>
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-600 dark:border-green-400 mx-auto"></div>
        </div>
      </div>
    );
  }

  if (!profileComplete) {
    return (
      <div className="max-w-4xl mx-auto py-12">
        <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-6 mb-8">
          <h2 className="text-xl font-medium text-amber-800 dark:text-amber-400 mb-4">
            Complete Your Profile to Apply
          </h2>
          <p className="text-amber-700 dark:text-amber-500 mb-4">
            Before you can apply for jobs, you need to complete your profile with the following information:
          </p>
          <ul className="list-disc list-inside mb-6 text-amber-700 dark:text-amber-500">
            {missingItems.map((item, index) => (
              <li key={index}>{item}</li>
            ))}
          </ul>
          <div className="flex justify-between">
            <Link
              href={`/jobs/${id}`}
              className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
            >
              Back to Job Details
            </Link>
            <Link
              href="/candidate/profile"
              className="bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-md"
            >
              Complete Profile
            </Link>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto py-12">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Apply for Job</h1>
        <Link
          href={`/jobs/${id}`}
          className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
        >
          Back to Job Details
        </Link>
      </div>

      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6 mb-8">
        <h2 className="text-xl font-semibold mb-2">{job.title}</h2>
        <div className="flex flex-wrap gap-2 mb-4">
          <span className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 text-xs px-2 py-1 rounded">
            {job.location}
          </span>
          <span className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 text-xs px-2 py-1 rounded">
            {job.employmentType.replace("-", " ")}
          </span>
          {job.salary && (
            <span className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 text-xs px-2 py-1 rounded">
              {job.salary.min} - {job.salary.max} {job.salary.currency}
            </span>
          )}
        </div>

        {matchPercentage !== null && (
          <div className="mb-6">
            <div className="flex items-center mb-2">
              <span className="text-sm font-medium mr-2">Match:</span>
              <div className="w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2.5 mr-2">
                <div
                  className={`h-2.5 rounded-full ${
                    matchPercentage >= 80 ? "bg-green-500" :
                    matchPercentage >= 50 ? "bg-yellow-500" :
                    "bg-red-500"
                  }`}
                  style={{ width: `${matchPercentage}%` }}
                ></div>
              </div>
              <span className="text-sm font-medium">{matchPercentage}%</span>
            </div>
            <p className="text-xs text-neutral-500 dark:text-neutral-400">
              This shows how well your preferences match with this job's benefits.
            </p>
          </div>
        )}
      </div>

      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6">
        <h2 className="text-xl font-semibold mb-6">Application Form</h2>

        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="mb-6">
            <label htmlFor="coverLetter" className="block text-sm font-medium mb-2">
              Cover Letter <span className="text-red-500">*</span>
            </label>
            <textarea
              id="coverLetter"
              {...register("coverLetter")}
              rows={8}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              placeholder="Introduce yourself and explain why you're a good fit for this position..."
            ></textarea>
            {errors.coverLetter && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.coverLetter.message}</p>
            )}
            <p className="mt-1 text-xs text-neutral-500 dark:text-neutral-400">
              Your profile information will be automatically included with your application.
            </p>
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={submitting}
              className={`px-6 py-3 rounded-md font-medium ${
                submitting
                  ? "bg-neutral-700 text-white cursor-wait"
                  : "bg-neutral-900 hover:bg-neutral-800 text-white"
              }`}
            >
              {submitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Submitting...
                </>
              ) : (
                "Submit Application"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
