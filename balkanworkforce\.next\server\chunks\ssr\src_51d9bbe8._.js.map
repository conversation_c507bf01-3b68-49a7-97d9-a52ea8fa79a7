{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/data/jobBenefits.ts"], "sourcesContent": ["// Job benefits/features that employers can select when creating a job\n// and candidates can select as preferences\n\nexport const jobBenefits = [\n  {\n    id: \"flexible_hours\",\n    label: \"Flexible Working Hours\",\n    description: \"Flexible start and end times or the ability to work remotely\"\n  },\n  {\n    id: \"remote_work\",\n    label: \"Remote Work Options\",\n    description: \"Possibility to work remotely part-time or full-time\"\n  },\n  {\n    id: \"health_insurance\",\n    label: \"Health Insurance\",\n    description: \"Comprehensive health insurance coverage\"\n  },\n  {\n    id: \"paid_vacation\",\n    label: \"Paid Vacation\",\n    description: \"Generous paid time off and vacation days\"\n  },\n  {\n    id: \"professional_development\",\n    label: \"Professional Development\",\n    description: \"Training, courses, and career advancement opportunities\"\n  },\n  {\n    id: \"retirement_plan\",\n    label: \"Retirement Plan\",\n    description: \"Company pension or retirement savings plan\"\n  },\n  {\n    id: \"relocation_assistance\",\n    label: \"Relocation Assistance\",\n    description: \"Help with moving expenses and finding accommodation\"\n  },\n  {\n    id: \"accommodation_provided_free\",\n    label: \"Free Accommodation Provided\",\n    description: \"Accommodation is provided and fully paid by the employer\"\n  },\n  {\n    id: \"accommodation_provided_paid\",\n    label: \"Accommodation Provided (Paid)\",\n    description: \"Accommodation is arranged by the employer but paid by the employee\"\n  },\n  {\n    id: \"accommodation_not_provided\",\n    label: \"Accommodation Not Provided\",\n    description: \"Employee must arrange their own accommodation\"\n  },\n  {\n    id: \"accommodation_assistance\",\n    label: \"Accommodation Finding Assistance\",\n    description: \"Employer assists in finding accommodation but does not provide it directly\"\n  },\n  {\n    id: \"language_courses\",\n    label: \"Language Courses\",\n    description: \"Free or subsidized language training\"\n  },\n  {\n    id: \"childcare\",\n    label: \"Childcare Support\",\n    description: \"On-site childcare or childcare subsidies\"\n  },\n  {\n    id: \"gym_membership\",\n    label: \"Gym Membership\",\n    description: \"Free or discounted gym or wellness program\"\n  },\n  {\n    id: \"company_car\",\n    label: \"Company Car\",\n    description: \"Company vehicle or car allowance\"\n  },\n  {\n    id: \"meal_allowance\",\n    label: \"Meal Allowance\",\n    description: \"Free or subsidized meals or meal vouchers\"\n  },\n  {\n    id: \"performance_bonus\",\n    label: \"Performance Bonuses\",\n    description: \"Financial bonuses based on performance\"\n  },\n  {\n    id: \"housing_allowance\",\n    label: \"Housing Allowance\",\n    description: \"Assistance with housing costs\"\n  },\n  {\n    id: \"public_transport\",\n    label: \"Public Transport Subsidy\",\n    description: \"Free or subsidized public transportation\"\n  }\n];\n\n// Minimum number of benefits that must be selected when creating a job\nexport const MIN_BENEFITS_REQUIRED = 3;\n"], "names": [], "mappings": "AAAA,sEAAsE;AACtE,2CAA2C;;;;;AAEpC,MAAM,cAAc;IACzB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;CACD;AAGM,MAAM,wBAAwB", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/data/degrees.ts"], "sourcesContent": ["import { Degree } from \"@/types\";\n\n// Simplified standardized educational degrees in multiple languages\nexport const degrees: Degree[] = [\n  {\n    id: \"primary-school\",\n    translations: {\n      de: \"Grundschule\",\n      en: \"Primary School\",\n      bs: \"<PERSON>snovna škola\",\n      hr: \"<PERSON>snovna škola\",\n      sr: \"<PERSON>snovna škola\",\n      me: \"Osnovna škola\",\n      mk: \"Основно училиште\",\n      al: \"Shkollë fillore\"\n    },\n    level: \"primary\"\n  },\n  {\n    id: \"high-school\",\n    translations: {\n      de: \"Mittelschule\",\n      en: \"High School\",\n      bs: \"Srednja škola\",\n      hr: \"Srednja škola\",\n      sr: \"Srednja škola\",\n      me: \"Srednja škola\",\n      mk: \"Средно училиште\",\n      al: \"Shkollë e mesme\"\n    },\n    level: \"secondary\"\n  },\n  {\n    id: \"bachelors-degree\",\n    translations: {\n      de: \"Bachelor\",\n      en: \"Bachelor's Degree\",\n      bs: \"Bakal<PERSON>reat\",\n      hr: \"Prvostupnik\",\n      sr: \"Osnovne akademske studije\",\n      me: \"Osnovne akademske studije\",\n      mk: \"Додипломски студии\",\n      al: \"Diplomë Bachelor\"\n    },\n    level: \"bachelor\"\n  },\n  {\n    id: \"masters-degree\",\n    translations: {\n      de: \"Master\",\n      en: \"Master's Degree\",\n      bs: \"Magistar\",\n      hr: \"Magistar\",\n      sr: \"Master\",\n      me: \"Master\",\n      mk: \"Магистерски студии\",\n      al: \"Diplomë Master\"\n    },\n    level: \"master\"\n  },\n  {\n    id: \"doctorate\",\n    translations: {\n      de: \"Doktor\",\n      en: \"Doctorate\",\n      bs: \"Doktorat\",\n      hr: \"Doktorat\",\n      sr: \"Doktorat\",\n      me: \"Doktorat\",\n      mk: \"Докторат\",\n      al: \"Doktoraturë\"\n    },\n    level: \"doctorate\"\n  }\n];\n"], "names": [], "mappings": ";;;AAGO,MAAM,UAAoB;IAC/B;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,OAAO;IACT;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,OAAO;IACT;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,OAAO;IACT;IACA;QACE,IAAI;QAC<PERSON>,cAAc;Y<PERSON><PERSON>,IAAI;Y<PERSON><PERSON>,IAAI;<PERSON><PERSON><PERSON>,IAAI;YACJ,IAAI;YACJ,IAAI;<PERSON><PERSON><PERSON>,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,OAAO;IACT;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,OAAO;IACT;CACD", "debugId": null}}, {"offset": {"line": 198, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/data/driverLicenses.ts"], "sourcesContent": ["export interface DriverLicense {\n  id: string;\n  name: string;\n  description: string;\n  translations: {\n    [languageCode: string]: {\n      name: string;\n      description: string;\n    };\n  };\n  category: \"car\" | \"motorcycle\" | \"truck\" | \"bus\" | \"special\" | \"none\";\n}\n\nexport const driverLicenses: DriverLicense[] = [\n  {\n    id: \"none\",\n    name: \"No License\",\n    description: \"No driver's license\",\n    translations: {\n      en: {\n        name: \"No License\",\n        description: \"No driver's license\"\n      },\n      de: {\n        name: \"Kein Führerschein\",\n        description: \"Kein Führerschein vorhanden\"\n      },\n      bs: {\n        name: \"<PERSON><PERSON> vozačke\",\n        description: \"Bez vozačke dozvole\"\n      }\n    },\n    category: \"none\"\n  },\n  {\n    id: \"am\",\n    name: \"AM\",\n    description: \"Mopeds and light quadricycles\",\n    translations: {\n      en: {\n        name: \"AM\",\n        description: \"Mopeds and light quadricycles\"\n      },\n      de: {\n        name: \"AM\",\n        description: \"Mopeds und leichte Vierradfahrzeuge\"\n      },\n      bs: {\n        name: \"AM\",\n        description: \"Mopedi i laki četverocikli\"\n      }\n    },\n    category: \"motorcycle\"\n  },\n  {\n    id: \"a1\",\n    name: \"<PERSON>\",\n    description: \"Light motorcycles\",\n    translations: {\n      en: {\n        name: \"A1\",\n        description: \"Light motorcycles\"\n      },\n      de: {\n        name: \"A1\",\n        description: \"Leichte Krafträder\"\n      },\n      bs: {\n        name: \"A1\",\n        description: \"Laki motocikli\"\n      }\n    },\n    category: \"motorcycle\"\n  },\n  {\n    id: \"a2\",\n    name: \"A2\",\n    description: \"Medium motorcycles\",\n    translations: {\n      en: {\n        name: \"A2\",\n        description: \"Medium motorcycles\"\n      },\n      de: {\n        name: \"A2\",\n        description: \"Mittlere Krafträder\"\n      },\n      bs: {\n        name: \"A2\",\n        description: \"Srednji motocikli\"\n      }\n    },\n    category: \"motorcycle\"\n  },\n  {\n    id: \"a\",\n    name: \"A\",\n    description: \"Motorcycles\",\n    translations: {\n      en: {\n        name: \"A\",\n        description: \"Motorcycles\"\n      },\n      de: {\n        name: \"A\",\n        description: \"Krafträder\"\n      },\n      bs: {\n        name: \"A\",\n        description: \"Motocikli\"\n      }\n    },\n    category: \"motorcycle\"\n  },\n  {\n    id: \"b1\",\n    name: \"B1\",\n    description: \"Quadricycles\",\n    translations: {\n      en: {\n        name: \"B1\",\n        description: \"Quadricycles\"\n      },\n      de: {\n        name: \"B1\",\n        description: \"Vierradfahrzeuge\"\n      },\n      bs: {\n        name: \"B1\",\n        description: \"Četverocikli\"\n      }\n    },\n    category: \"car\"\n  },\n  {\n    id: \"b\",\n    name: \"B\",\n    description: \"Cars and light vans\",\n    translations: {\n      en: {\n        name: \"B\",\n        description: \"Cars and light vans\"\n      },\n      de: {\n        name: \"B\",\n        description: \"Pkw und leichte Nutzfahrzeuge\"\n      },\n      bs: {\n        name: \"B\",\n        description: \"Automobili i laka dostavna vozila\"\n      }\n    },\n    category: \"car\"\n  },\n  {\n    id: \"be\",\n    name: \"BE\",\n    description: \"Cars with trailer\",\n    translations: {\n      en: {\n        name: \"BE\",\n        description: \"Cars with trailer\"\n      },\n      de: {\n        name: \"BE\",\n        description: \"Pkw mit Anhänger\"\n      },\n      bs: {\n        name: \"BE\",\n        description: \"Automobili s prikolicom\"\n      }\n    },\n    category: \"car\"\n  },\n  {\n    id: \"c1\",\n    name: \"C1\",\n    description: \"Medium-sized vehicles\",\n    translations: {\n      en: {\n        name: \"C1\",\n        description: \"Medium-sized vehicles\"\n      },\n      de: {\n        name: \"C1\",\n        description: \"Mittelgroße Fahrzeuge\"\n      },\n      bs: {\n        name: \"C1\",\n        description: \"Vozila srednje veličine\"\n      }\n    },\n    category: \"truck\"\n  },\n  {\n    id: \"c1e\",\n    name: \"C1E\",\n    description: \"Medium-sized vehicles with trailer\",\n    translations: {\n      en: {\n        name: \"C1E\",\n        description: \"Medium-sized vehicles with trailer\"\n      },\n      de: {\n        name: \"C1E\",\n        description: \"Mittelgroße Fahrzeuge mit Anhänger\"\n      },\n      bs: {\n        name: \"C1E\",\n        description: \"Vozila srednje veličine s prikolicom\"\n      }\n    },\n    category: \"truck\"\n  },\n  {\n    id: \"c\",\n    name: \"C\",\n    description: \"Large goods vehicles\",\n    translations: {\n      en: {\n        name: \"C\",\n        description: \"Large goods vehicles\"\n      },\n      de: {\n        name: \"C\",\n        description: \"Schwere Nutzfahrzeuge\"\n      },\n      bs: {\n        name: \"C\",\n        description: \"Teretna vozila\"\n      }\n    },\n    category: \"truck\"\n  },\n  {\n    id: \"ce\",\n    name: \"CE\",\n    description: \"Large goods vehicles with trailer\",\n    translations: {\n      en: {\n        name: \"CE\",\n        description: \"Large goods vehicles with trailer\"\n      },\n      de: {\n        name: \"CE\",\n        description: \"Schwere Nutzfahrzeuge mit Anhänger\"\n      },\n      bs: {\n        name: \"CE\",\n        description: \"Teretna vozila s prikolicom\"\n      }\n    },\n    category: \"truck\"\n  },\n  {\n    id: \"d1\",\n    name: \"D1\",\n    description: \"Minibuses\",\n    translations: {\n      en: {\n        name: \"D1\",\n        description: \"Minibuses\"\n      },\n      de: {\n        name: \"D1\",\n        description: \"Kleinbusse\"\n      },\n      bs: {\n        name: \"D1\",\n        description: \"Minibusevi\"\n      }\n    },\n    category: \"bus\"\n  },\n  {\n    id: \"d1e\",\n    name: \"D1E\",\n    description: \"Minibuses with trailer\",\n    translations: {\n      en: {\n        name: \"D1E\",\n        description: \"Minibuses with trailer\"\n      },\n      de: {\n        name: \"D1E\",\n        description: \"Kleinbusse mit Anhänger\"\n      },\n      bs: {\n        name: \"D1E\",\n        description: \"Minibusevi s prikolicom\"\n      }\n    },\n    category: \"bus\"\n  },\n  {\n    id: \"d\",\n    name: \"D\",\n    description: \"Buses\",\n    translations: {\n      en: {\n        name: \"D\",\n        description: \"Buses\"\n      },\n      de: {\n        name: \"D\",\n        description: \"Busse\"\n      },\n      bs: {\n        name: \"D\",\n        description: \"Autobusi\"\n      }\n    },\n    category: \"bus\"\n  },\n  {\n    id: \"de\",\n    name: \"DE\",\n    description: \"Buses with trailer\",\n    translations: {\n      en: {\n        name: \"DE\",\n        description: \"Buses with trailer\"\n      },\n      de: {\n        name: \"DE\",\n        description: \"Busse mit Anhänger\"\n      },\n      bs: {\n        name: \"DE\",\n        description: \"Autobusi s prikolicom\"\n      }\n    },\n    category: \"bus\"\n  }\n];\n\n// Group licenses by category for easier display\nexport const driverLicensesByCategory = {\n  none: driverLicenses.filter(license => license.category === \"none\"),\n  car: driverLicenses.filter(license => license.category === \"car\"),\n  motorcycle: driverLicenses.filter(license => license.category === \"motorcycle\"),\n  truck: driverLicenses.filter(license => license.category === \"truck\"),\n  bus: driverLicenses.filter(license => license.category === \"bus\"),\n  special: driverLicenses.filter(license => license.category === \"special\"),\n};\n\n// Helper function to get the highest license category\nexport function getHighestLicenseCategory(licenses: string[]): string {\n  if (!licenses || licenses.length === 0) return \"none\";\n  \n  // Priority order (highest to lowest)\n  const priorityOrder = [\n    \"de\", \"d\", \"d1e\", \"d1\",\n    \"ce\", \"c\", \"c1e\", \"c1\",\n    \"be\", \"b\", \"b1\",\n    \"a\", \"a2\", \"a1\", \"am\",\n    \"none\"\n  ];\n  \n  // Find the license with the highest priority\n  let highestPriority = Number.MAX_SAFE_INTEGER;\n  let highestLicense = \"none\";\n  \n  for (const license of licenses) {\n    const priority = priorityOrder.indexOf(license.toLowerCase());\n    if (priority !== -1 && priority < highestPriority) {\n      highestPriority = priority;\n      highestLicense = license.toLowerCase();\n    }\n  }\n  \n  return highestLicense;\n}\n"], "names": [], "mappings": ";;;;;AAaO,MAAM,iBAAkC;IAC7C;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;CACD;AAGM,MAAM,2BAA2B;IACtC,MAAM,eAAe,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAC5D,KAAK,eAAe,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAC3D,YAAY,eAAe,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAClE,OAAO,eAAe,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAC7D,KAAK,eAAe,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAC3D,SAAS,eAAe,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;AACjE;AAGO,SAAS,0BAA0B,QAAkB;IAC1D,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG,OAAO;IAE/C,qCAAqC;IACrC,MAAM,gBAAgB;QACpB;QAAM;QAAK;QAAO;QAClB;QAAM;QAAK;QAAO;QAClB;QAAM;QAAK;QACX;QAAK;QAAM;QAAM;QACjB;KACD;IAED,6CAA6C;IAC7C,IAAI,kBAAkB,OAAO,gBAAgB;IAC7C,IAAI,iBAAiB;IAErB,KAAK,MAAM,WAAW,SAAU;QAC9B,MAAM,WAAW,cAAc,OAAO,CAAC,QAAQ,WAAW;QAC1D,IAAI,aAAa,CAAC,KAAK,WAAW,iBAAiB;YACjD,kBAAkB;YAClB,iBAAiB,QAAQ,WAAW;QACtC;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 572, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/hooks/useFirestoreData.ts"], "sourcesContent": ["import { useState, useEffect } from \"react\";\nimport { collection, getDocs, query, orderBy } from \"firebase/firestore\";\nimport { db } from \"@/lib/firebase\";\n\nexport function useFirestoreData<T>(collectionName: string) {\n  const [data, setData] = useState<T[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n\n  const fetchData = async () => {\n    try {\n      setLoading(true);\n      setError(null);\n      \n      // Try to fetch from Firestore first\n      const q = query(collection(db, collectionName));\n      const snapshot = await getDocs(q);\n      \n      if (snapshot.empty) {\n        // If no data in Firestore, fall back to static data\n        const staticData = await getStaticData<T>(collectionName);\n        setData(staticData);\n      } else {\n        const items = snapshot.docs.map(doc => ({\n          id: doc.id,\n          ...doc.data()\n        })) as T[];\n        setData(items);\n      }\n    } catch (err) {\n      console.error(`Error fetching ${collectionName}:`, err);\n      \n      // Fall back to static data on error\n      try {\n        const staticData = await getStaticData<T>(collectionName);\n        setData(staticData);\n      } catch (staticErr) {\n        console.error(`Error loading static data for ${collectionName}:`, staticErr);\n        setError(`Failed to load ${collectionName}`);\n      }\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  useEffect(() => {\n    fetchData();\n  }, [collectionName]);\n\n  return { data, loading, error, refetch: fetchData };\n}\n\n// Helper function to get static data as fallback\nasync function getStaticData<T>(collectionName: string): Promise<T[]> {\n  switch (collectionName) {\n    case 'positions':\n      const { positions } = await import('@/data/positions');\n      return positions as T[];\n    case 'studyFields':\n      const { studyFields } = await import('@/data/studyFields');\n      return studyFields as T[];\n    case 'driverLicenses':\n      const { driverLicenses } = await import('@/data/driverLicenses');\n      return driverLicenses as T[];\n    case 'benefits':\n      const { jobBenefits } = await import('@/data/jobBenefits');\n      return jobBenefits as T[];\n    case 'countries':\n      const { countries } = await import('@/data/countries');\n      return countries as T[];\n    default:\n      return [];\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;AACA;;;;AAEO,SAAS,iBAAoB,cAAsB;IACxD,MAAM,CAAC,MAAM,QAAQ,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO,EAAE;IACxC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAElD,MAAM,YAAY;QAChB,IAAI;YACF,WAAW;YACX,SAAS;YAET,oCAAoC;YACpC,MAAM,IAAI,CAAA,GAAA,iKAAA,CAAA,QAAK,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE;YAC/B,MAAM,WAAW,MAAM,CAAA,GAAA,iKAAA,CAAA,UAAO,AAAD,EAAE;YAE/B,IAAI,SAAS,KAAK,EAAE;gBAClB,oDAAoD;gBACpD,MAAM,aAAa,MAAM,cAAiB;gBAC1C,QAAQ;YACV,OAAO;gBACL,MAAM,QAAQ,SAAS,IAAI,CAAC,GAAG,CAAC,CAAA,MAAO,CAAC;wBACtC,IAAI,IAAI,EAAE;wBACV,GAAG,IAAI,IAAI,EAAE;oBACf,CAAC;gBACD,QAAQ;YACV;QACF,EAAE,OAAO,KAAK;YACZ,QAAQ,KAAK,CAAC,CAAC,eAAe,EAAE,eAAe,CAAC,CAAC,EAAE;YAEnD,oCAAoC;YACpC,IAAI;gBACF,MAAM,aAAa,MAAM,cAAiB;gBAC1C,QAAQ;YACV,EAAE,OAAO,WAAW;gBAClB,QAAQ,KAAK,CAAC,CAAC,8BAA8B,EAAE,eAAe,CAAC,CAAC,EAAE;gBAClE,SAAS,CAAC,eAAe,EAAE,gBAAgB;YAC7C;QACF,SAAU;YACR,WAAW;QACb;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR;IACF,GAAG;QAAC;KAAe;IAEnB,OAAO;QAAE;QAAM;QAAS;QAAO,SAAS;IAAU;AACpD;AAEA,iDAAiD;AACjD,eAAe,cAAiB,cAAsB;IACpD,OAAQ;QACN,KAAK;YACH,MAAM,EAAE,SAAS,EAAE,GAAG;YACtB,OAAO;QACT,KAAK;YACH,MAAM,EAAE,WAAW,EAAE,GAAG;YACxB,OAAO;QACT,KAAK;YACH,MAAM,EAAE,cAAc,EAAE,GAAG;YAC3B,OAAO;QACT,KAAK;YACH,MAAM,EAAE,WAAW,EAAE,GAAG;YACxB,OAAO;QACT,KAAK;YACH,MAAM,EAAE,SAAS,EAAE,GAAG;YACtB,OAAO;QACT;YACE,OAAO,EAAE;IACb;AACF", "debugId": null}}, {"offset": {"line": 658, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/components/forms/PositionSelect.tsx"], "sourcesContent": ["import { useFirestoreData } from \"@/hooks/useFirestoreData\";\nimport { Position } from \"@/types\";\nimport { useLanguage } from \"@/contexts/LanguageContext\";\n\ninterface PositionSelectProps {\n  value: string;\n  onChange: (value: string) => void;\n  placeholder?: string;\n  className?: string;\n  required?: boolean;\n}\n\nexport default function PositionSelect({\n  value,\n  onChange,\n  placeholder = \"Select position\",\n  className = \"\",\n  required = false,\n}: PositionSelectProps) {\n  const { language } = useLanguage();\n  const { data: positions, loading, error } = useFirestoreData<Position>(\"positions\");\n\n  if (loading) {\n    return (\n      <select className={`${className} opacity-50`} disabled>\n        <option>Loading positions...</option>\n      </select>\n    );\n  }\n\n  if (error) {\n    return (\n      <select className={`${className} opacity-50`} disabled>\n        <option>Error loading positions</option>\n      </select>\n    );\n  }\n\n  return (\n    <select\n      value={value}\n      onChange={(e) => onChange(e.target.value)}\n      className={className}\n      required={required}\n    >\n      <option value=\"\">{placeholder}</option>\n      {positions.map((position) => (\n        <option key={position.id} value={position.id}>\n          {position.translations[language] || position.translations.en || position.id}\n        </option>\n      ))}\n    </select>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAEA;;;;AAUe,SAAS,eAAe,EACrC,KAAK,EACL,QAAQ,EACR,cAAc,iBAAiB,EAC/B,YAAY,EAAE,EACd,WAAW,KAAK,EACI;IACpB,MAAM,EAAE,QAAQ,EAAE,GAAG,CAAA,GAAA,mIAAA,CAAA,cAAW,AAAD;IAC/B,MAAM,EAAE,MAAM,SAAS,EAAE,OAAO,EAAE,KAAK,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAY;IAEvE,IAAI,SAAS;QACX,qBACE,8OAAC;YAAO,WAAW,GAAG,UAAU,WAAW,CAAC;YAAE,QAAQ;sBACpD,cAAA,8OAAC;0BAAO;;;;;;;;;;;IAGd;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAO,WAAW,GAAG,UAAU,WAAW,CAAC;YAAE,QAAQ;sBACpD,cAAA,8OAAC;0BAAO;;;;;;;;;;;IAGd;IAEA,qBACE,8OAAC;QACC,OAAO;QACP,UAAU,CAAC,IAAM,SAAS,EAAE,MAAM,CAAC,KAAK;QACxC,WAAW;QACX,UAAU;;0BAEV,8OAAC;gBAAO,OAAM;0BAAI;;;;;;YACjB,UAAU,GAAG,CAAC,CAAC,yBACd,8OAAC;oBAAyB,OAAO,SAAS,EAAE;8BACzC,SAAS,YAAY,CAAC,SAAS,IAAI,SAAS,YAAY,CAAC,EAAE,IAAI,SAAS,EAAE;mBADhE,SAAS,EAAE;;;;;;;;;;;AAMhC", "debugId": null}}, {"offset": {"line": 739, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/components/employer/StructuredRequirementsForm.tsx"], "sourcesContent": ["import { useState, useEffect } from \"react\";\nimport { LanguageLevel, JobRequirements } from \"@/types\";\nimport { degrees } from \"@/data/degrees\";\nimport { driverLicensesByCategory } from \"@/data/driverLicenses\";\nimport Image from \"next/image\";\nimport PositionSelect from \"@/components/forms/PositionSelect\";\nimport CountrySelect from \"@/components/forms/CountrySelect\";\nimport { useFirestoreData } from \"@/hooks/useFirestoreData\";\nimport { Position, Country, DriverLicense } from \"@/types\";\n\ninterface StructuredRequirementsFormProps {\n  value: JobRequirements;\n  onChange: (value: JobRequirements) => void;\n}\n\nexport default function StructuredRequirementsForm({\n  value,\n  onChange,\n}: StructuredRequirementsFormProps) {\n  const { data: positions } = useFirestoreData<Position>(\"positions\");\n  const { data: countries } = useFirestoreData<Country>(\"countries\");\n  const { data: driverLicenses } = useFirestoreData<DriverLicense>(\"driverLicenses\");\n\n  const [requirements, setRequirements] = useState<JobRequirements>(\n    value || {\n      germanLanguageLevel: undefined,\n      englishLanguageLevel: undefined,\n      otherLanguages: [],\n      requiredPositions: [],\n      minEducationLevel: undefined,\n      driverLicense: undefined,\n      requiredCountries: [],\n    }\n  );\n\n  useEffect(() => {\n    onChange(requirements);\n  }, [requirements, onChange]);\n\n  // Language requirements handlers\n  const handleLanguageLevelChange = (\n    language: \"germanLanguageLevel\" | \"englishLanguageLevel\",\n    level: LanguageLevel | undefined\n  ) => {\n    setRequirements({\n      ...requirements,\n      [language]: level,\n    });\n  };\n\n  // Position requirements handlers\n  const addPositionRequirement = () => {\n    setRequirements({\n      ...requirements,\n      requiredPositions: [\n        ...(requirements.requiredPositions || []),\n        { positionId: \"\", minExperienceMonths: 0 },\n      ],\n    });\n  };\n\n  const updatePositionRequirement = (\n    index: number,\n    field: \"positionId\" | \"minExperienceMonths\",\n    value: string | number\n  ) => {\n    const updatedPositions = [...(requirements.requiredPositions || [])];\n    updatedPositions[index] = {\n      ...updatedPositions[index],\n      [field]: value,\n    };\n    setRequirements({\n      ...requirements,\n      requiredPositions: updatedPositions,\n    });\n  };\n\n  const removePositionRequirement = (index: number) => {\n    const updatedPositions = [...(requirements.requiredPositions || [])];\n    updatedPositions.splice(index, 1);\n    setRequirements({\n      ...requirements,\n      requiredPositions: updatedPositions,\n    });\n  };\n\n  // Education requirements handlers\n  const handleEducationLevelChange = (level: string | undefined) => {\n    setRequirements({\n      ...requirements,\n      minEducationLevel: level,\n    });\n  };\n\n  // Driver's license handler\n  const handleDriverLicenseChange = (licenseId: string | undefined) => {\n    setRequirements({\n      ...requirements,\n      driverLicense: licenseId,\n    });\n  };\n\n  // Country requirements handlers\n  const handleCountryChange = (countryId: string, checked: boolean) => {\n    const currentCountries = [...(requirements.requiredCountries || [])];\n    if (checked && !currentCountries.includes(countryId)) {\n      setRequirements({\n        ...requirements,\n        requiredCountries: [...currentCountries, countryId],\n      });\n    } else if (!checked && currentCountries.includes(countryId)) {\n      setRequirements({\n        ...requirements,\n        requiredCountries: currentCountries.filter((id) => id !== countryId),\n      });\n    }\n  };\n\n  // Group countries by region (with fallback for when data is loading)\n  const euCountries = countries?.filter(\n    (country) => country.region === \"eu\" || [\"de\", \"at\", \"ch\", \"fr\", \"it\", \"nl\", \"be\", \"lu\", \"dk\", \"se\", \"fi\", \"ie\", \"pt\", \"es\"].includes(country.id)\n  ) || [];\n\n  const balkanCountries = countries?.filter(\n    (country) => country.region === \"balkans\" || [\"ba\", \"rs\", \"hr\", \"me\", \"mk\", \"si\", \"al\", \"bg\", \"ro\", \"gr\"].includes(country.id)\n  ) || [];\n\n  // Group degrees by level\n  const degreesByLevel: Record<string, typeof degrees> = {\n    secondary: degrees.filter((d) => d.level === \"secondary\"),\n    vocational: degrees.filter((d) => d.level === \"vocational\"),\n    tertiary: degrees.filter((d) => d.level === \"tertiary\"),\n    bachelor: degrees.filter((d) => d.level === \"bachelor\"),\n    master: degrees.filter((d) => d.level === \"master\"),\n    doctorate: degrees.filter((d) => d.level === \"doctorate\"),\n  };\n\n  // Define language levels array to match candidate profile\n  const languageLevels: LanguageLevel[] = [\"None\", \"A1\", \"A2\", \"B1\", \"B2\", \"C1\", \"C2\", \"Native\"];\n\n  // Helper function to get language level description\n  function getLevelDescription(level: LanguageLevel): string {\n    switch (level) {\n      case \"None\":\n        return \"\";\n      case \"A1\":\n        return \"(Beginner)\";\n      case \"A2\":\n        return \"(Elementary)\";\n      case \"B1\":\n        return \"(Intermediate)\";\n      case \"B2\":\n        return \"(Upper Intermediate)\";\n      case \"C1\":\n        return \"(Advanced)\";\n      case \"C2\":\n        return \"(Proficient)\";\n      case \"Native\":\n        return \"(Mother Tongue)\";\n      default:\n        return \"\";\n    }\n  }\n\n  return (\n    <div className=\"space-y-8\">\n      {/* Language Requirements */}\n      <div>\n        <h3 className=\"text-lg font-medium mb-4\">Language Requirements</h3>\n        <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6\">\n          <div>\n            <label className=\"block text-sm font-medium mb-1\">\n              German Language Level\n            </label>\n            <select\n              value={requirements.germanLanguageLevel || \"\"}\n              onChange={(e) =>\n                handleLanguageLevelChange(\n                  \"germanLanguageLevel\",\n                  e.target.value ? (e.target.value as LanguageLevel) : undefined\n                )\n              }\n              className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n            >\n              <option value=\"\">Not required</option>\n              {languageLevels.map((level) => (\n                <option key={level} value={level}>\n                  {level === \"None\" ? \"No knowledge\" : level} {level !== \"None\" ? getLevelDescription(level) : \"\"}\n                </option>\n              ))}\n            </select>\n          </div>\n          <div>\n            <label className=\"block text-sm font-medium mb-1\">\n              English Language Level\n            </label>\n            <select\n              value={requirements.englishLanguageLevel || \"\"}\n              onChange={(e) =>\n                handleLanguageLevelChange(\n                  \"englishLanguageLevel\",\n                  e.target.value ? (e.target.value as LanguageLevel) : undefined\n                )\n              }\n              className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n            >\n              <option value=\"\">Not required</option>\n              {languageLevels.map((level) => (\n                <option key={level} value={level}>\n                  {level === \"None\" ? \"No knowledge\" : level} {level !== \"None\" ? getLevelDescription(level) : \"\"}\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n      </div>\n\n      {/* Work Experience Requirements */}\n      <div>\n        <div className=\"flex justify-between items-center mb-4\">\n          <h3 className=\"text-lg font-medium\">Work Experience Requirements</h3>\n          <button\n            type=\"button\"\n            onClick={addPositionRequirement}\n            className=\"text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm flex items-center\"\n          >\n            <svg\n              xmlns=\"http://www.w3.org/2000/svg\"\n              className=\"h-4 w-4 mr-1\"\n              viewBox=\"0 0 20 20\"\n              fill=\"currentColor\"\n            >\n              <path\n                fillRule=\"evenodd\"\n                d=\"M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z\"\n                clipRule=\"evenodd\"\n              />\n            </svg>\n            Add Position\n          </button>\n        </div>\n        {requirements.requiredPositions && requirements.requiredPositions.length > 0 ? (\n          <div className=\"space-y-4\">\n            {requirements.requiredPositions.map((position, index) => (\n              <div\n                key={index}\n                className=\"grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border border-neutral-200 dark:border-neutral-700 rounded-md\"\n              >\n                <div className=\"md:col-span-2\">\n                  <label className=\"block text-sm font-medium mb-1\">\n                    Position\n                  </label>\n                  <PositionSelect\n                    value={position.positionId}\n                    onChange={(value) =>\n                      updatePositionRequirement(index, \"positionId\", value)\n                    }\n                    className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n                    placeholder=\"Select a position\"\n                  />\n                </div>\n                <div>\n                  <label className=\"block text-sm font-medium mb-1\">\n                    Min. Experience (months)\n                  </label>\n                  <div className=\"flex\">\n                    <input\n                      type=\"number\"\n                      min=\"0\"\n                      value={position.minExperienceMonths}\n                      onChange={(e) =>\n                        updatePositionRequirement(\n                          index,\n                          \"minExperienceMonths\",\n                          parseInt(e.target.value) || 0\n                        )\n                      }\n                      className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-l-md\"\n                    />\n                    <button\n                      type=\"button\"\n                      onClick={() => removePositionRequirement(index)}\n                      className=\"bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 px-3 py-2 rounded-r-md border border-red-200 dark:border-red-800\"\n                    >\n                      <svg\n                        xmlns=\"http://www.w3.org/2000/svg\"\n                        className=\"h-5 w-5\"\n                        viewBox=\"0 0 20 20\"\n                        fill=\"currentColor\"\n                      >\n                        <path\n                          fillRule=\"evenodd\"\n                          d=\"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z\"\n                          clipRule=\"evenodd\"\n                        />\n                      </svg>\n                    </button>\n                  </div>\n                </div>\n              </div>\n            ))}\n          </div>\n        ) : (\n          <p className=\"text-neutral-500 dark:text-neutral-400 text-sm\">\n            No position requirements added. Click \"Add Position\" to specify required work experience.\n          </p>\n        )}\n      </div>\n\n      {/* Education Requirements */}\n      <div>\n        <h3 className=\"text-lg font-medium mb-4\">Education Requirements</h3>\n        <div className=\"mb-6\">\n          <label className=\"block text-sm font-medium mb-1\">\n            Minimum Education Level\n          </label>\n          <select\n            value={requirements.minEducationLevel || \"\"}\n            onChange={(e) =>\n              handleEducationLevelChange(\n                e.target.value || undefined\n              )\n            }\n            className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n          >\n            <option value=\"\">Not required</option>\n            {degrees.map((degree) => (\n              <option key={degree.id} value={degree.id}>\n                {degree.translations.en}\n              </option>\n            ))}\n          </select>\n        </div>\n      </div>\n\n      {/* Driver's License Requirements */}\n      <div>\n        <h3 className=\"text-lg font-medium mb-4\">Driver's License Requirements</h3>\n        <p className=\"text-sm text-neutral-600 dark:text-neutral-400 mb-4\">\n          Select the minimum driver's license category required for this job\n        </p>\n\n        <div className=\"mb-6\">\n          <select\n            value={requirements.driverLicense || \"\"}\n            onChange={(e) => handleDriverLicenseChange(e.target.value || undefined)}\n            className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n          >\n            <option value=\"\">Not required</option>\n            <optgroup label=\"Car Licenses\">\n              {driverLicensesByCategory.car.map((license) => (\n                <option key={license.id} value={license.id}>\n                  {license.name} - {license.description}\n                </option>\n              ))}\n            </optgroup>\n            <optgroup label=\"Motorcycle Licenses\">\n              {driverLicensesByCategory.motorcycle.map((license) => (\n                <option key={license.id} value={license.id}>\n                  {license.name} - {license.description}\n                </option>\n              ))}\n            </optgroup>\n            <optgroup label=\"Truck Licenses\">\n              {driverLicensesByCategory.truck.map((license) => (\n                <option key={license.id} value={license.id}>\n                  {license.name} - {license.description}\n                </option>\n              ))}\n            </optgroup>\n            <optgroup label=\"Bus Licenses\">\n              {driverLicensesByCategory.bus.map((license) => (\n                <option key={license.id} value={license.id}>\n                  {license.name} - {license.description}\n                </option>\n              ))}\n            </optgroup>\n          </select>\n        </div>\n      </div>\n\n      {/* Country Requirements */}\n      <div>\n        <h3 className=\"text-lg font-medium mb-4\">Job Location</h3>\n        <p className=\"text-sm text-neutral-600 dark:text-neutral-400 mb-4\">\n          Select the country where this job is located\n        </p>\n\n        <div className=\"grid grid-cols-2 gap-4\">\n          {['de', 'at', 'hr', 'si'].map((countryId) => {\n            const country = countries?.find(c => c.id === countryId);\n            const isSelected = (requirements.requiredCountries || []).includes(countryId);\n            return (\n              <div\n                key={countryId}\n                className={`flex items-center p-4 border rounded-md cursor-pointer ${isSelected ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-700' : 'border-neutral-200 dark:border-neutral-700'}`}\n                onClick={() => {\n                  // Set this as the only selected country\n                  setRequirements({\n                    ...requirements,\n                    requiredCountries: [countryId]\n                  });\n                }}\n              >\n                {country?.flag && (\n                  <span className=\"text-xl mr-3\">{country.flag}</span>\n                )}\n                <span className=\"font-medium\">{country ? (country.translations?.en || country.name) : countryId}</span>\n              </div>\n            );\n          })}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AAEA;AACA;AAEA;AAEA;;;;;;;AAQe,SAAS,2BAA2B,EACjD,KAAK,EACL,QAAQ,EACwB;IAChC,MAAM,EAAE,MAAM,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAY;IACvD,MAAM,EAAE,MAAM,SAAS,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAW;IACtD,MAAM,EAAE,MAAM,cAAc,EAAE,GAAG,CAAA,GAAA,gIAAA,CAAA,mBAAgB,AAAD,EAAiB;IAEjE,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAC7C,SAAS;QACP,qBAAqB;QACrB,sBAAsB;QACtB,gBAAgB,EAAE;QAClB,mBAAmB,EAAE;QACrB,mBAAmB;QACnB,eAAe;QACf,mBAAmB,EAAE;IACvB;IAGF,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;IACX,GAAG;QAAC;QAAc;KAAS;IAE3B,iCAAiC;IACjC,MAAM,4BAA4B,CAChC,UACA;QAEA,gBAAgB;YACd,GAAG,YAAY;YACf,CAAC,SAAS,EAAE;QACd;IACF;IAEA,iCAAiC;IACjC,MAAM,yBAAyB;QAC7B,gBAAgB;YACd,GAAG,YAAY;YACf,mBAAmB;mBACb,aAAa,iBAAiB,IAAI,EAAE;gBACxC;oBAAE,YAAY;oBAAI,qBAAqB;gBAAE;aAC1C;QACH;IACF;IAEA,MAAM,4BAA4B,CAChC,OACA,OACA;QAEA,MAAM,mBAAmB;eAAK,aAAa,iBAAiB,IAAI,EAAE;SAAE;QACpE,gBAAgB,CAAC,MAAM,GAAG;YACxB,GAAG,gBAAgB,CAAC,MAAM;YAC1B,CAAC,MAAM,EAAE;QACX;QACA,gBAAgB;YACd,GAAG,YAAY;YACf,mBAAmB;QACrB;IACF;IAEA,MAAM,4BAA4B,CAAC;QACjC,MAAM,mBAAmB;eAAK,aAAa,iBAAiB,IAAI,EAAE;SAAE;QACpE,iBAAiB,MAAM,CAAC,OAAO;QAC/B,gBAAgB;YACd,GAAG,YAAY;YACf,mBAAmB;QACrB;IACF;IAEA,kCAAkC;IAClC,MAAM,6BAA6B,CAAC;QAClC,gBAAgB;YACd,GAAG,YAAY;YACf,mBAAmB;QACrB;IACF;IAEA,2BAA2B;IAC3B,MAAM,4BAA4B,CAAC;QACjC,gBAAgB;YACd,GAAG,YAAY;YACf,eAAe;QACjB;IACF;IAEA,gCAAgC;IAChC,MAAM,sBAAsB,CAAC,WAAmB;QAC9C,MAAM,mBAAmB;eAAK,aAAa,iBAAiB,IAAI,EAAE;SAAE;QACpE,IAAI,WAAW,CAAC,iBAAiB,QAAQ,CAAC,YAAY;YACpD,gBAAgB;gBACd,GAAG,YAAY;gBACf,mBAAmB;uBAAI;oBAAkB;iBAAU;YACrD;QACF,OAAO,IAAI,CAAC,WAAW,iBAAiB,QAAQ,CAAC,YAAY;YAC3D,gBAAgB;gBACd,GAAG,YAAY;gBACf,mBAAmB,iBAAiB,MAAM,CAAC,CAAC,KAAO,OAAO;YAC5D;QACF;IACF;IAEA,qEAAqE;IACrE,MAAM,cAAc,WAAW,OAC7B,CAAC,UAAY,QAAQ,MAAM,KAAK,QAAQ;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;SAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAC7I,EAAE;IAEP,MAAM,kBAAkB,WAAW,OACjC,CAAC,UAAY,QAAQ,MAAM,KAAK,aAAa;YAAC;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;YAAM;SAAK,CAAC,QAAQ,CAAC,QAAQ,EAAE,MAC1H,EAAE;IAEP,yBAAyB;IACzB,MAAM,iBAAiD;QACrD,WAAW,sHAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,KAAK,KAAK;QAC7C,YAAY,sHAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,KAAK,KAAK;QAC9C,UAAU,sHAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,KAAK,KAAK;QAC5C,UAAU,sHAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,KAAK,KAAK;QAC5C,QAAQ,sHAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,KAAK,KAAK;QAC1C,WAAW,sHAAA,CAAA,UAAO,CAAC,MAAM,CAAC,CAAC,IAAM,EAAE,KAAK,KAAK;IAC/C;IAEA,0DAA0D;IAC1D,MAAM,iBAAkC;QAAC;QAAQ;QAAM;QAAM;QAAM;QAAM;QAAM;QAAM;KAAS;IAE9F,oDAAoD;IACpD,SAAS,oBAAoB,KAAoB;QAC/C,OAAQ;YACN,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT,KAAK;gBACH,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAA2B;;;;;;kCACzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAiC;;;;;;kDAGlD,8OAAC;wCACC,OAAO,aAAa,mBAAmB,IAAI;wCAC3C,UAAU,CAAC,IACT,0BACE,uBACA,EAAE,MAAM,CAAC,KAAK,GAAI,EAAE,MAAM,CAAC,KAAK,GAAqB;wCAGzD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC;oDAAmB,OAAO;;wDACxB,UAAU,SAAS,iBAAiB;wDAAM;wDAAE,UAAU,SAAS,oBAAoB,SAAS;;mDADlF;;;;;;;;;;;;;;;;;0CAMnB,8OAAC;;kDACC,8OAAC;wCAAM,WAAU;kDAAiC;;;;;;kDAGlD,8OAAC;wCACC,OAAO,aAAa,oBAAoB,IAAI;wCAC5C,UAAU,CAAC,IACT,0BACE,wBACA,EAAE,MAAM,CAAC,KAAK,GAAI,EAAE,MAAM,CAAC,KAAK,GAAqB;wCAGzD,WAAU;;0DAEV,8OAAC;gDAAO,OAAM;0DAAG;;;;;;4CAChB,eAAe,GAAG,CAAC,CAAC,sBACnB,8OAAC;oDAAmB,OAAO;;wDACxB,UAAU,SAAS,iBAAiB;wDAAM;wDAAE,UAAU,SAAS,oBAAoB,SAAS;;mDADlF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUvB,8OAAC;;kCACC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAAsB;;;;;;0CACpC,8OAAC;gCACC,MAAK;gCACL,SAAS;gCACT,WAAU;;kDAEV,8OAAC;wCACC,OAAM;wCACN,WAAU;wCACV,SAAQ;wCACR,MAAK;kDAEL,cAAA,8OAAC;4CACC,UAAS;4CACT,GAAE;4CACF,UAAS;;;;;;;;;;;oCAEP;;;;;;;;;;;;;oBAIT,aAAa,iBAAiB,IAAI,aAAa,iBAAiB,CAAC,MAAM,GAAG,kBACzE,8OAAC;wBAAI,WAAU;kCACZ,aAAa,iBAAiB,CAAC,GAAG,CAAC,CAAC,UAAU,sBAC7C,8OAAC;gCAEC,WAAU;;kDAEV,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAGlD,8OAAC,6IAAA,CAAA,UAAc;gDACb,OAAO,SAAS,UAAU;gDAC1B,UAAU,CAAC,QACT,0BAA0B,OAAO,cAAc;gDAEjD,WAAU;gDACV,aAAY;;;;;;;;;;;;kDAGhB,8OAAC;;0DACC,8OAAC;gDAAM,WAAU;0DAAiC;;;;;;0DAGlD,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDACC,MAAK;wDACL,KAAI;wDACJ,OAAO,SAAS,mBAAmB;wDACnC,UAAU,CAAC,IACT,0BACE,OACA,uBACA,SAAS,EAAE,MAAM,CAAC,KAAK,KAAK;wDAGhC,WAAU;;;;;;kEAEZ,8OAAC;wDACC,MAAK;wDACL,SAAS,IAAM,0BAA0B;wDACzC,WAAU;kEAEV,cAAA,8OAAC;4DACC,OAAM;4DACN,WAAU;4DACV,SAAQ;4DACR,MAAK;sEAEL,cAAA,8OAAC;gEACC,UAAS;gEACT,GAAE;gEACF,UAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;+BAhDd;;;;;;;;;6CA0DX,8OAAC;wBAAE,WAAU;kCAAiD;;;;;;;;;;;;0BAOlE,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAA2B;;;;;;kCACzC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAM,WAAU;0CAAiC;;;;;;0CAGlD,8OAAC;gCACC,OAAO,aAAa,iBAAiB,IAAI;gCACzC,UAAU,CAAC,IACT,2BACE,EAAE,MAAM,CAAC,KAAK,IAAI;gCAGtB,WAAU;;kDAEV,8OAAC;wCAAO,OAAM;kDAAG;;;;;;oCAChB,sHAAA,CAAA,UAAO,CAAC,GAAG,CAAC,CAAC,uBACZ,8OAAC;4CAAuB,OAAO,OAAO,EAAE;sDACrC,OAAO,YAAY,CAAC,EAAE;2CADZ,OAAO,EAAE;;;;;;;;;;;;;;;;;;;;;;;0BAS9B,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAA2B;;;;;;kCACzC,8OAAC;wBAAE,WAAU;kCAAsD;;;;;;kCAInE,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BACC,OAAO,aAAa,aAAa,IAAI;4BACrC,UAAU,CAAC,IAAM,0BAA0B,EAAE,MAAM,CAAC,KAAK,IAAI;4BAC7D,WAAU;;8CAEV,8OAAC;oCAAO,OAAM;8CAAG;;;;;;8CACjB,8OAAC;oCAAS,OAAM;8CACb,6HAAA,CAAA,2BAAwB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,wBACjC,8OAAC;4CAAwB,OAAO,QAAQ,EAAE;;gDACvC,QAAQ,IAAI;gDAAC;gDAAI,QAAQ,WAAW;;2CAD1B,QAAQ,EAAE;;;;;;;;;;8CAK3B,8OAAC;oCAAS,OAAM;8CACb,6HAAA,CAAA,2BAAwB,CAAC,UAAU,CAAC,GAAG,CAAC,CAAC,wBACxC,8OAAC;4CAAwB,OAAO,QAAQ,EAAE;;gDACvC,QAAQ,IAAI;gDAAC;gDAAI,QAAQ,WAAW;;2CAD1B,QAAQ,EAAE;;;;;;;;;;8CAK3B,8OAAC;oCAAS,OAAM;8CACb,6HAAA,CAAA,2BAAwB,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,wBACnC,8OAAC;4CAAwB,OAAO,QAAQ,EAAE;;gDACvC,QAAQ,IAAI;gDAAC;gDAAI,QAAQ,WAAW;;2CAD1B,QAAQ,EAAE;;;;;;;;;;8CAK3B,8OAAC;oCAAS,OAAM;8CACb,6HAAA,CAAA,2BAAwB,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,wBACjC,8OAAC;4CAAwB,OAAO,QAAQ,EAAE;;gDACvC,QAAQ,IAAI;gDAAC;gDAAI,QAAQ,WAAW;;2CAD1B,QAAQ,EAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAUjC,8OAAC;;kCACC,8OAAC;wBAAG,WAAU;kCAA2B;;;;;;kCACzC,8OAAC;wBAAE,WAAU;kCAAsD;;;;;;kCAInE,8OAAC;wBAAI,WAAU;kCACZ;4BAAC;4BAAM;4BAAM;4BAAM;yBAAK,CAAC,GAAG,CAAC,CAAC;4BAC7B,MAAM,UAAU,WAAW,KAAK,CAAA,IAAK,EAAE,EAAE,KAAK;4BAC9C,MAAM,aAAa,CAAC,aAAa,iBAAiB,IAAI,EAAE,EAAE,QAAQ,CAAC;4BACnE,qBACE,8OAAC;gCAEC,WAAW,CAAC,uDAAuD,EAAE,aAAa,wEAAwE,8CAA8C;gCACxM,SAAS;oCACP,wCAAwC;oCACxC,gBAAgB;wCACd,GAAG,YAAY;wCACf,mBAAmB;4CAAC;yCAAU;oCAChC;gCACF;;oCAEC,SAAS,sBACR,8OAAC;wCAAK,WAAU;kDAAgB,QAAQ,IAAI;;;;;;kDAE9C,8OAAC;wCAAK,WAAU;kDAAe,UAAW,QAAQ,YAAY,EAAE,MAAM,QAAQ,IAAI,GAAI;;;;;;;+BAbjF;;;;;wBAgBX;;;;;;;;;;;;;;;;;;AAKV", "debugId": null}}, {"offset": {"line": 1487, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/app/employer/jobs/new/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useAuth } from \"@/contexts/AuthContext\";\nimport { useRouter } from \"next/navigation\";\nimport { doc, collection, addDoc, getDoc } from \"firebase/firestore\";\nimport { db } from \"@/lib/firebase\";\nimport { z } from \"zod\";\nimport { useForm } from \"react-hook-form\";\nimport { zodResolver } from \"@hookform/resolvers/zod\";\nimport Link from \"next/link\";\nimport { jobBenefits, MIN_BENEFITS_REQUIRED } from \"@/data/jobBenefits\";\nimport { JobRequirements } from \"@/types\";\nimport StructuredRequirementsForm from \"@/components/employer/StructuredRequirementsForm\";\nimport { countries } from \"@/data/countries\";\n\n// Form validation schema\nconst jobFormSchema = z.object({\n  title: z.string().min(5, \"Job title must be at least 5 characters\"),\n  description: z.string().min(50, \"Job description must be at least 50 characters\"),\n  location: z.string().min(2, \"Location is required\"),\n  employmentType: z.enum([\"full-time\", \"part-time\", \"contract\", \"temporary\", \"internship\"]),\n  salaryMin: z.coerce.number().min(0, \"Minimum salary cannot be negative\").optional(),\n  salaryMax: z.coerce.number().min(0, \"Maximum salary cannot be negative\").optional(),\n  salaryCurrency: z.string().min(1, \"Currency is required\"),\n});\n\ntype JobFormValues = z.infer<typeof jobFormSchema>;\n\nexport default function NewJobPage() {\n  const { user, loading: authLoading } = useAuth();\n  const router = useRouter();\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [employerData, setEmployerData] = useState<any>(null);\n  const [selectedBenefits, setSelectedBenefits] = useState<string[]>([]);\n  const [benefitsError, setBenefitsError] = useState<string | null>(null);\n  const [structuredRequirements, setStructuredRequirements] = useState<JobRequirements>({});\n\n  const {\n    register,\n    handleSubmit,\n    formState: { errors },\n  } = useForm<JobFormValues>({\n    resolver: zodResolver(jobFormSchema),\n    defaultValues: {\n      title: \"\",\n      description: \"\",\n      location: \"\",\n      employmentType: \"full-time\",\n      salaryMin: undefined,\n      salaryMax: undefined,\n      salaryCurrency: \"EUR\",\n    },\n  });\n\n  useEffect(() => {\n    // If not authenticated or not an employer, redirect to auth page\n    if (!authLoading && (!user || user.role !== \"employer\")) {\n      router.push(\"/auth\");\n    }\n  }, [user, authLoading, router]);\n\n  useEffect(() => {\n    const fetchEmployerData = async () => {\n      if (!user) return;\n\n      try {\n        const employerDoc = await getDoc(doc(db, \"employers\", user.uid));\n\n        if (employerDoc.exists()) {\n          setEmployerData(employerDoc.data());\n        } else {\n          // If employer profile doesn't exist, redirect to profile creation\n          router.push(\"/employer/profile\");\n        }\n      } catch (error) {\n        console.error(\"Error fetching employer data:\", error);\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (user && user.role === \"employer\") {\n      fetchEmployerData();\n    }\n  }, [user, router]);\n\n  const toggleBenefit = (benefitId: string) => {\n    setSelectedBenefits(prev => {\n      if (prev.includes(benefitId)) {\n        return prev.filter(id => id !== benefitId);\n      } else {\n        return [...prev, benefitId];\n      }\n    });\n\n    // Clear error if enough benefits are selected\n    if (selectedBenefits.length >= MIN_BENEFITS_REQUIRED - 1) {\n      setBenefitsError(null);\n    }\n  };\n\n  const onSubmit = async (data: JobFormValues) => {\n    if (selectedBenefits.length < MIN_BENEFITS_REQUIRED) {\n      setBenefitsError(`Please select at least ${MIN_BENEFITS_REQUIRED} benefits`);\n      return;\n    }\n\n    if (!user) return;\n\n    setSaving(true);\n    setError(null);\n\n    try {\n      // Process form data\n      console.log(\"User data:\", user);\n      console.log(\"Employer data:\", employerData);\n      const jobData = {\n        title: data.title,\n        description: data.description,\n        location: data.location,\n        employmentType: data.employmentType,\n        salary: data.salaryMin || data.salaryMax ? {\n          min: data.salaryMin || 0,\n          max: data.salaryMax || 0,\n          currency: data.salaryCurrency,\n        } : null,\n        benefits: selectedBenefits,\n        employerId: user.uid,\n        companyName: employerData?.companyName || \"\",\n        companyLogo: employerData?.logoURL || null,\n        isPremium: false,\n        applications: [],\n        structuredRequirements: structuredRequirements,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n      };\n\n      // Save to Firestore\n      console.log(\"Creating job with data:\", jobData);\n      const docRef = await addDoc(collection(db, \"jobs\"), jobData);\n      console.log(\"Job created with ID:\", docRef.id);\n\n      // Redirect to job detail page\n      router.push(`/employer/jobs/${docRef.id}`);\n    } catch (error: any) {\n      console.error(\"Error creating job:\", error);\n      setError(error.message || \"Failed to create job. Please try again.\");\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  if (authLoading || loading) {\n    return (\n      <div className=\"flex justify-center items-center min-h-[60vh]\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto py-12\">\n      <div className=\"flex justify-between items-center mb-8\">\n        <h1 className=\"text-3xl font-bold\">Post a New Job</h1>\n        <Link\n          href=\"/employer/dashboard\"\n          className=\"text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white\"\n        >\n          Back to Dashboard\n        </Link>\n      </div>\n\n      {error && (\n        <div className=\"mb-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded-md\">\n          {error}\n        </div>\n      )}\n\n      <div className=\"bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8\">\n        <form onSubmit={handleSubmit(onSubmit)}>\n          <div className=\"mb-6\">\n            <label htmlFor=\"title\" className=\"block text-sm font-medium mb-1\">\n              Job Title <span className=\"text-red-500\">*</span>\n            </label>\n            <input\n              id=\"title\"\n              {...register(\"title\")}\n              className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n              placeholder=\"e.g., Senior Electrician, Software Developer\"\n            />\n            {errors.title && (\n              <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.title.message}</p>\n            )}\n          </div>\n\n          <div className=\"mb-6\">\n            <label htmlFor=\"description\" className=\"block text-sm font-medium mb-1\">\n              Job Description <span className=\"text-red-500\">*</span>\n            </label>\n            <textarea\n              id=\"description\"\n              {...register(\"description\")}\n              rows={6}\n              className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n              placeholder=\"Provide a detailed description of the job, responsibilities, and what the candidate will be doing\"\n            ></textarea>\n            {errors.description && (\n              <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.description.message}</p>\n            )}\n          </div>\n\n          <div className=\"mb-6\">\n            <StructuredRequirementsForm\n              value={structuredRequirements}\n              onChange={setStructuredRequirements}\n            />\n          </div>\n\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-6 mb-6\">\n            <div>\n              <label htmlFor=\"location\" className=\"block text-sm font-medium mb-1\">\n                Location <span className=\"text-red-500\">*</span>\n              </label>\n              <input\n                id=\"location\"\n                {...register(\"location\")}\n                className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n                placeholder=\"e.g., Berlin, Germany\"\n              />\n              {errors.location && (\n                <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.location.message}</p>\n              )}\n            </div>\n\n            <div>\n              <label htmlFor=\"employmentType\" className=\"block text-sm font-medium mb-1\">\n                Employment Type <span className=\"text-red-500\">*</span>\n              </label>\n              <select\n                id=\"employmentType\"\n                {...register(\"employmentType\")}\n                className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n              >\n                <option value=\"full-time\">Full-time</option>\n                <option value=\"part-time\">Part-time</option>\n                <option value=\"contract\">Contract</option>\n                <option value=\"temporary\">Temporary</option>\n                <option value=\"internship\">Internship</option>\n              </select>\n              {errors.employmentType && (\n                <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.employmentType.message}</p>\n              )}\n            </div>\n          </div>\n\n          <div className=\"mb-6\">\n            <h3 className=\"text-lg font-medium mb-2\">Salary Range (Optional)</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4\">\n              <div>\n                <label htmlFor=\"salaryMin\" className=\"block text-sm font-medium mb-1\">\n                  Minimum\n                </label>\n                <input\n                  id=\"salaryMin\"\n                  type=\"number\"\n                  {...register(\"salaryMin\")}\n                  className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n                  placeholder=\"e.g., 30000\"\n                />\n                {errors.salaryMin && (\n                  <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.salaryMin.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label htmlFor=\"salaryMax\" className=\"block text-sm font-medium mb-1\">\n                  Maximum\n                </label>\n                <input\n                  id=\"salaryMax\"\n                  type=\"number\"\n                  {...register(\"salaryMax\")}\n                  className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n                  placeholder=\"e.g., 50000\"\n                />\n                {errors.salaryMax && (\n                  <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.salaryMax.message}</p>\n                )}\n              </div>\n\n              <div>\n                <label htmlFor=\"salaryCurrency\" className=\"block text-sm font-medium mb-1\">\n                  Currency <span className=\"text-red-500\">*</span>\n                </label>\n                <input\n                  id=\"salaryCurrency\"\n                  {...register(\"salaryCurrency\")}\n                  className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md bg-neutral-100 dark:bg-neutral-800\"\n                  value=\"EUR\"\n                  readOnly\n                />\n                {errors.salaryCurrency && (\n                  <p className=\"mt-1 text-sm text-red-600 dark:text-red-400\">{errors.salaryCurrency.message}</p>\n                )}\n              </div>\n            </div>\n          </div>\n\n\n\n          <div className=\"mb-8\">\n            <h3 className=\"text-lg font-medium mb-2\">\n              Job Benefits <span className=\"text-red-500\">*</span>\n            </h3>\n            <p className=\"text-sm text-neutral-600 dark:text-neutral-400 mb-4\">\n              Select at least {MIN_BENEFITS_REQUIRED} benefits that your company offers for this position.\n              These will be used to match with candidate preferences.\n            </p>\n\n            {benefitsError && (\n              <p className=\"mb-4 text-sm text-red-600 dark:text-red-400\">{benefitsError}</p>\n            )}\n\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n              {jobBenefits.map((benefit) => (\n                <div\n                  key={benefit.id}\n                  className={`p-3 rounded-md border cursor-pointer transition-all ${\n                    selectedBenefits.includes(benefit.id)\n                      ? \"border-neutral-900 dark:border-white bg-neutral-50 dark:bg-neutral-700\"\n                      : \"border-neutral-200 dark:border-neutral-700 hover:border-neutral-400 dark:hover:border-neutral-500\"\n                  }`}\n                  onClick={() => toggleBenefit(benefit.id)}\n                >\n                  <div className=\"flex items-center\">\n                    <div className={`w-5 h-5 rounded-sm border flex items-center justify-center mr-3 ${\n                      selectedBenefits.includes(benefit.id)\n                        ? \"bg-neutral-900 dark:bg-white border-neutral-900 dark:border-white\"\n                        : \"border-neutral-400 dark:border-neutral-500\"\n                    }`}>\n                      {selectedBenefits.includes(benefit.id) && (\n                        <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-3 w-3 text-white dark:text-neutral-900\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                          <path fillRule=\"evenodd\" d=\"M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z\" clipRule=\"evenodd\" />\n                        </svg>\n                      )}\n                    </div>\n                    <div>\n                      <h4 className=\"font-medium\">{benefit.label}</h4>\n                      <p className=\"text-xs text-neutral-500 dark:text-neutral-400\">{benefit.description}</p>\n                    </div>\n                  </div>\n                </div>\n              ))}\n            </div>\n          </div>\n\n          <div className=\"flex justify-end\">\n            <button\n              type=\"submit\"\n              disabled={saving}\n              className={`px-6 py-3 rounded-md font-medium ${\n                saving\n                  ? \"bg-neutral-700 text-white cursor-wait\"\n                  : \"bg-neutral-900 hover:bg-neutral-800 text-white\"\n              }`}\n            >\n              {saving ? (\n                <>\n                  <svg className=\"animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block\" xmlns=\"http://www.w3.org/2000/svg\" fill=\"none\" viewBox=\"0 0 24 24\">\n                    <circle className=\"opacity-25\" cx=\"12\" cy=\"12\" r=\"10\" stroke=\"currentColor\" strokeWidth=\"4\"></circle>\n                    <path className=\"opacity-75\" fill=\"currentColor\" d=\"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z\"></path>\n                  </svg>\n                  Creating Job...\n                </>\n              ) : (\n                \"Post Job\"\n              )}\n            </button>\n          </div>\n        </form>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AAEA;AAbA;;;;;;;;;;;;;AAgBA,yBAAyB;AACzB,MAAM,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC;IAC7B,OAAO,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IACzB,aAAa,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,IAAI;IAChC,UAAU,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;IAC5B,gBAAgB,oIAAA,CAAA,IAAC,CAAC,IAAI,CAAC;QAAC;QAAa;QAAa;QAAY;QAAa;KAAa;IACxF,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,qCAAqC,QAAQ;IACjF,WAAW,oIAAA,CAAA,IAAC,CAAC,MAAM,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG,qCAAqC,QAAQ;IACjF,gBAAgB,oIAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,CAAC,GAAG;AACpC;AAIe,SAAS;IACtB,MAAM,EAAE,IAAI,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACtD,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAY,EAAE;IACrE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClE,MAAM,CAAC,wBAAwB,0BAA0B,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAmB,CAAC;IAEvF,MAAM,EACJ,QAAQ,EACR,YAAY,EACZ,WAAW,EAAE,MAAM,EAAE,EACtB,GAAG,CAAA,GAAA,8JAAA,CAAA,UAAO,AAAD,EAAiB;QACzB,UAAU,CAAA,GAAA,8JAAA,CAAA,cAAW,AAAD,EAAE;QACtB,eAAe;YACb,OAAO;YACP,aAAa;YACb,UAAU;YACV,gBAAgB;YAChB,WAAW;YACX,WAAW;YACX,gBAAgB;QAClB;IACF;IAEA,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iEAAiE;QACjE,IAAI,CAAC,eAAe,CAAC,CAAC,QAAQ,KAAK,IAAI,KAAK,UAAU,GAAG;YACvD,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAa;KAAO;IAE9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,oBAAoB;YACxB,IAAI,CAAC,MAAM;YAEX,IAAI;gBACF,MAAM,cAAc,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,aAAa,KAAK,GAAG;gBAE9D,IAAI,YAAY,MAAM,IAAI;oBACxB,gBAAgB,YAAY,IAAI;gBAClC,OAAO;oBACL,kEAAkE;oBAClE,OAAO,IAAI,CAAC;gBACd;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,iCAAiC;YACjD,SAAU;gBACR,WAAW;YACb;QACF;QAEA,IAAI,QAAQ,KAAK,IAAI,KAAK,YAAY;YACpC;QACF;IACF,GAAG;QAAC;QAAM;KAAO;IAEjB,MAAM,gBAAgB,CAAC;QACrB,oBAAoB,CAAA;YAClB,IAAI,KAAK,QAAQ,CAAC,YAAY;gBAC5B,OAAO,KAAK,MAAM,CAAC,CAAA,KAAM,OAAO;YAClC,OAAO;gBACL,OAAO;uBAAI;oBAAM;iBAAU;YAC7B;QACF;QAEA,8CAA8C;QAC9C,IAAI,iBAAiB,MAAM,IAAI,0HAAA,CAAA,wBAAqB,GAAG,GAAG;YACxD,iBAAiB;QACnB;IACF;IAEA,MAAM,WAAW,OAAO;QACtB,IAAI,iBAAiB,MAAM,GAAG,0HAAA,CAAA,wBAAqB,EAAE;YACnD,iBAAiB,CAAC,uBAAuB,EAAE,0HAAA,CAAA,wBAAqB,CAAC,SAAS,CAAC;YAC3E;QACF;QAEA,IAAI,CAAC,MAAM;QAEX,UAAU;QACV,SAAS;QAET,IAAI;YACF,oBAAoB;YACpB,QAAQ,GAAG,CAAC,cAAc;YAC1B,QAAQ,GAAG,CAAC,kBAAkB;YAC9B,MAAM,UAAU;gBACd,OAAO,KAAK,KAAK;gBACjB,aAAa,KAAK,WAAW;gBAC7B,UAAU,KAAK,QAAQ;gBACvB,gBAAgB,KAAK,cAAc;gBACnC,QAAQ,KAAK,SAAS,IAAI,KAAK,SAAS,GAAG;oBACzC,KAAK,KAAK,SAAS,IAAI;oBACvB,KAAK,KAAK,SAAS,IAAI;oBACvB,UAAU,KAAK,cAAc;gBAC/B,IAAI;gBACJ,UAAU;gBACV,YAAY,KAAK,GAAG;gBACpB,aAAa,cAAc,eAAe;gBAC1C,aAAa,cAAc,WAAW;gBACtC,WAAW;gBACX,cAAc,EAAE;gBAChB,wBAAwB;gBACxB,WAAW,IAAI;gBACf,WAAW,IAAI;YACjB;YAEA,oBAAoB;YACpB,QAAQ,GAAG,CAAC,2BAA2B;YACvC,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,aAAU,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,SAAS;YACpD,QAAQ,GAAG,CAAC,wBAAwB,OAAO,EAAE;YAE7C,8BAA8B;YAC9B,OAAO,IAAI,CAAC,CAAC,eAAe,EAAE,OAAO,EAAE,EAAE;QAC3C,EAAE,OAAO,OAAY;YACnB,QAAQ,KAAK,CAAC,uBAAuB;YACrC,SAAS,MAAM,OAAO,IAAI;QAC5B,SAAU;YACR,UAAU;QACZ;IACF;IAEA,IAAI,eAAe,SAAS;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;YAKF,uBACC,8OAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIL,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAK,UAAU,aAAa;;sCAC3B,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,SAAQ;oCAAQ,WAAU;;wCAAiC;sDACtD,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAE3C,8OAAC;oCACC,IAAG;oCACF,GAAG,SAAS,QAAQ;oCACrB,WAAU;oCACV,aAAY;;;;;;gCAEb,OAAO,KAAK,kBACX,8OAAC;oCAAE,WAAU;8CAA+C,OAAO,KAAK,CAAC,OAAO;;;;;;;;;;;;sCAIpF,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAM,SAAQ;oCAAc,WAAU;;wCAAiC;sDACtD,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAEjD,8OAAC;oCACC,IAAG;oCACF,GAAG,SAAS,cAAc;oCAC3B,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;gCAEb,OAAO,WAAW,kBACjB,8OAAC;oCAAE,WAAU;8CAA+C,OAAO,WAAW,CAAC,OAAO;;;;;;;;;;;;sCAI1F,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC,4JAAA,CAAA,UAA0B;gCACzB,OAAO;gCACP,UAAU;;;;;;;;;;;sCAId,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAW,WAAU;;gDAAiC;8DAC1D,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAE1C,8OAAC;4CACC,IAAG;4CACF,GAAG,SAAS,WAAW;4CACxB,WAAU;4CACV,aAAY;;;;;;wCAEb,OAAO,QAAQ,kBACd,8OAAC;4CAAE,WAAU;sDAA+C,OAAO,QAAQ,CAAC,OAAO;;;;;;;;;;;;8CAIvF,8OAAC;;sDACC,8OAAC;4CAAM,SAAQ;4CAAiB,WAAU;;gDAAiC;8DACzD,8OAAC;oDAAK,WAAU;8DAAe;;;;;;;;;;;;sDAEjD,8OAAC;4CACC,IAAG;4CACF,GAAG,SAAS,iBAAiB;4CAC9B,WAAU;;8DAEV,8OAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,8OAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,8OAAC;oDAAO,OAAM;8DAAW;;;;;;8DACzB,8OAAC;oDAAO,OAAM;8DAAY;;;;;;8DAC1B,8OAAC;oDAAO,OAAM;8DAAa;;;;;;;;;;;;wCAE5B,OAAO,cAAc,kBACpB,8OAAC;4CAAE,WAAU;sDAA+C,OAAO,cAAc,CAAC,OAAO;;;;;;;;;;;;;;;;;;sCAK/F,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAA2B;;;;;;8CACzC,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAY,WAAU;8DAAiC;;;;;;8DAGtE,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACJ,GAAG,SAAS,YAAY;oDACzB,WAAU;oDACV,aAAY;;;;;;gDAEb,OAAO,SAAS,kBACf,8OAAC;oDAAE,WAAU;8DAA+C,OAAO,SAAS,CAAC,OAAO;;;;;;;;;;;;sDAIxF,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAY,WAAU;8DAAiC;;;;;;8DAGtE,8OAAC;oDACC,IAAG;oDACH,MAAK;oDACJ,GAAG,SAAS,YAAY;oDACzB,WAAU;oDACV,aAAY;;;;;;gDAEb,OAAO,SAAS,kBACf,8OAAC;oDAAE,WAAU;8DAA+C,OAAO,SAAS,CAAC,OAAO;;;;;;;;;;;;sDAIxF,8OAAC;;8DACC,8OAAC;oDAAM,SAAQ;oDAAiB,WAAU;;wDAAiC;sEAChE,8OAAC;4DAAK,WAAU;sEAAe;;;;;;;;;;;;8DAE1C,8OAAC;oDACC,IAAG;oDACF,GAAG,SAAS,iBAAiB;oDAC9B,WAAU;oDACV,OAAM;oDACN,QAAQ;;;;;;gDAET,OAAO,cAAc,kBACpB,8OAAC;oDAAE,WAAU;8DAA+C,OAAO,cAAc,CAAC,OAAO;;;;;;;;;;;;;;;;;;;;;;;;sCAQjG,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;;wCAA2B;sDAC1B,8OAAC;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAE9C,8OAAC;oCAAE,WAAU;;wCAAsD;wCAChD,0HAAA,CAAA,wBAAqB;wCAAC;;;;;;;gCAIxC,+BACC,8OAAC;oCAAE,WAAU;8CAA+C;;;;;;8CAG9D,8OAAC;oCAAI,WAAU;8CACZ,0HAAA,CAAA,cAAW,CAAC,GAAG,CAAC,CAAC,wBAChB,8OAAC;4CAEC,WAAW,CAAC,oDAAoD,EAC9D,iBAAiB,QAAQ,CAAC,QAAQ,EAAE,IAChC,2EACA,qGACJ;4CACF,SAAS,IAAM,cAAc,QAAQ,EAAE;sDAEvC,cAAA,8OAAC;gDAAI,WAAU;;kEACb,8OAAC;wDAAI,WAAW,CAAC,gEAAgE,EAC/E,iBAAiB,QAAQ,CAAC,QAAQ,EAAE,IAChC,sEACA,8CACJ;kEACC,iBAAiB,QAAQ,CAAC,QAAQ,EAAE,mBACnC,8OAAC;4DAAI,OAAM;4DAA6B,WAAU;4DAA2C,SAAQ;4DAAY,MAAK;sEACpH,cAAA,8OAAC;gEAAK,UAAS;gEAAU,GAAE;gEAAqH,UAAS;;;;;;;;;;;;;;;;kEAI/J,8OAAC;;0EACC,8OAAC;gEAAG,WAAU;0EAAe,QAAQ,KAAK;;;;;;0EAC1C,8OAAC;gEAAE,WAAU;0EAAkD,QAAQ,WAAW;;;;;;;;;;;;;;;;;;2CAtBjF,QAAQ,EAAE;;;;;;;;;;;;;;;;sCA8BvB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCACC,MAAK;gCACL,UAAU;gCACV,WAAW,CAAC,iCAAiC,EAC3C,SACI,0CACA,kDACJ;0CAED,uBACC;;sDACE,8OAAC;4CAAI,WAAU;4CAA0D,OAAM;4CAA6B,MAAK;4CAAO,SAAQ;;8DAC9H,8OAAC;oDAAO,WAAU;oDAAa,IAAG;oDAAK,IAAG;oDAAK,GAAE;oDAAK,QAAO;oDAAe,aAAY;;;;;;8DACxF,8OAAC;oDAAK,WAAU;oDAAa,MAAK;oDAAe,GAAE;;;;;;;;;;;;wCAC/C;;mDAIR;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQhB", "debugId": null}}]}