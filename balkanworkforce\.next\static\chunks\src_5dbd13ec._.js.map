{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/data/jobBenefits.ts"], "sourcesContent": ["// Job benefits/features that employers can select when creating a job\n// and candidates can select as preferences\n\nexport const jobBenefits = [\n  {\n    id: \"flexible_hours\",\n    label: \"Flexible Working Hours\",\n    description: \"Flexible start and end times or the ability to work remotely\"\n  },\n  {\n    id: \"remote_work\",\n    label: \"Remote Work Options\",\n    description: \"Possibility to work remotely part-time or full-time\"\n  },\n  {\n    id: \"health_insurance\",\n    label: \"Health Insurance\",\n    description: \"Comprehensive health insurance coverage\"\n  },\n  {\n    id: \"paid_vacation\",\n    label: \"Paid Vacation\",\n    description: \"Generous paid time off and vacation days\"\n  },\n  {\n    id: \"professional_development\",\n    label: \"Professional Development\",\n    description: \"Training, courses, and career advancement opportunities\"\n  },\n  {\n    id: \"retirement_plan\",\n    label: \"Retirement Plan\",\n    description: \"Company pension or retirement savings plan\"\n  },\n  {\n    id: \"relocation_assistance\",\n    label: \"Relocation Assistance\",\n    description: \"Help with moving expenses and finding accommodation\"\n  },\n  {\n    id: \"accommodation_provided_free\",\n    label: \"Free Accommodation Provided\",\n    description: \"Accommodation is provided and fully paid by the employer\"\n  },\n  {\n    id: \"accommodation_provided_paid\",\n    label: \"Accommodation Provided (Paid)\",\n    description: \"Accommodation is arranged by the employer but paid by the employee\"\n  },\n  {\n    id: \"accommodation_not_provided\",\n    label: \"Accommodation Not Provided\",\n    description: \"Employee must arrange their own accommodation\"\n  },\n  {\n    id: \"accommodation_assistance\",\n    label: \"Accommodation Finding Assistance\",\n    description: \"Employer assists in finding accommodation but does not provide it directly\"\n  },\n  {\n    id: \"language_courses\",\n    label: \"Language Courses\",\n    description: \"Free or subsidized language training\"\n  },\n  {\n    id: \"childcare\",\n    label: \"Childcare Support\",\n    description: \"On-site childcare or childcare subsidies\"\n  },\n  {\n    id: \"gym_membership\",\n    label: \"Gym Membership\",\n    description: \"Free or discounted gym or wellness program\"\n  },\n  {\n    id: \"company_car\",\n    label: \"Company Car\",\n    description: \"Company vehicle or car allowance\"\n  },\n  {\n    id: \"meal_allowance\",\n    label: \"Meal Allowance\",\n    description: \"Free or subsidized meals or meal vouchers\"\n  },\n  {\n    id: \"performance_bonus\",\n    label: \"Performance Bonuses\",\n    description: \"Financial bonuses based on performance\"\n  },\n  {\n    id: \"housing_allowance\",\n    label: \"Housing Allowance\",\n    description: \"Assistance with housing costs\"\n  },\n  {\n    id: \"public_transport\",\n    label: \"Public Transport Subsidy\",\n    description: \"Free or subsidized public transportation\"\n  }\n];\n\n// Minimum number of benefits that must be selected when creating a job\nexport const MIN_BENEFITS_REQUIRED = 3;\n"], "names": [], "mappings": "AAAA,sEAAsE;AACtE,2CAA2C;;;;;AAEpC,MAAM,cAAc;IACzB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;CACD;AAGM,MAAM,wBAAwB", "debugId": null}}, {"offset": {"line": 120, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/utils/experienceCalculator.ts"], "sourcesContent": ["import { WorkExperience } from \"@/types\";\n\n/**\n * Calculate total work experience in months\n * @param experiences Array of work experiences\n * @param positionId Optional position ID to filter by\n * @returns Total experience in months\n */\nexport function calculateTotalExperience(\n  experiences: WorkExperience[],\n  positionId?: string\n): number {\n  // Filter by position if specified\n  const relevantExperiences = positionId\n    ? experiences.filter(exp => exp.positionId === positionId)\n    : experiences;\n  \n  // Calculate total months\n  let totalMonths = 0;\n  \n  for (const exp of relevantExperiences) {\n    const startDate = new Date(exp.startDate);\n    const endDate = exp.current ? new Date() : new Date(exp.endDate!);\n    \n    // Calculate difference in months\n    const months = (endDate.getFullYear() - startDate.getFullYear()) * 12 +\n      (endDate.getMonth() - startDate.getMonth());\n    \n    totalMonths += Math.max(0, months); // Ensure we don't add negative months\n  }\n  \n  return totalMonths;\n}\n\n/**\n * Format experience duration in a human-readable format\n * @param months Total months of experience\n * @returns Formatted string (e.g., \"2 years, 3 months\")\n */\nexport function formatExperienceDuration(months: number): string {\n  if (months <= 0) {\n    return \"No experience\";\n  }\n  \n  const years = Math.floor(months / 12);\n  const remainingMonths = months % 12;\n  \n  if (years === 0) {\n    return `${remainingMonths} month${remainingMonths !== 1 ? 's' : ''}`;\n  } else if (remainingMonths === 0) {\n    return `${years} year${years !== 1 ? 's' : ''}`;\n  } else {\n    return `${years} year${years !== 1 ? 's' : ''}, ${remainingMonths} month${remainingMonths !== 1 ? 's' : ''}`;\n  }\n}\n\n/**\n * Calculate experience by position and return formatted results\n * @param experiences Array of work experiences\n * @returns Object with position IDs as keys and formatted experience as values\n */\nexport function calculateExperienceByPosition(\n  experiences: WorkExperience[]\n): Record<string, string> {\n  const result: Record<string, string> = {};\n  \n  // Get unique position IDs\n  const positionIds = [...new Set(experiences\n    .filter(exp => exp.positionId)\n    .map(exp => exp.positionId as string))];\n  \n  // Calculate experience for each position\n  for (const positionId of positionIds) {\n    const months = calculateTotalExperience(experiences, positionId);\n    result[positionId] = formatExperienceDuration(months);\n  }\n  \n  // Add total experience\n  const totalMonths = calculateTotalExperience(experiences);\n  result.total = formatExperienceDuration(totalMonths);\n  \n  return result;\n}\n"], "names": [], "mappings": ";;;;;AAQO,SAAS,yBACd,WAA6B,EAC7B,UAAmB;IAEnB,kCAAkC;IAClC,MAAM,sBAAsB,aACxB,YAAY,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,KAAK,cAC7C;IAEJ,yBAAyB;IACzB,IAAI,cAAc;IAElB,KAAK,MAAM,OAAO,oBAAqB;QACrC,MAAM,YAAY,IAAI,KAAK,IAAI,SAAS;QACxC,MAAM,UAAU,IAAI,OAAO,GAAG,IAAI,SAAS,IAAI,KAAK,IAAI,OAAO;QAE/D,iCAAiC;QACjC,MAAM,SAAS,CAAC,QAAQ,WAAW,KAAK,UAAU,WAAW,EAAE,IAAI,KACjE,CAAC,QAAQ,QAAQ,KAAK,UAAU,QAAQ,EAAE;QAE5C,eAAe,KAAK,GAAG,CAAC,GAAG,SAAS,sCAAsC;IAC5E;IAEA,OAAO;AACT;AAOO,SAAS,yBAAyB,MAAc;IACrD,IAAI,UAAU,GAAG;QACf,OAAO;IACT;IAEA,MAAM,QAAQ,KAAK,KAAK,CAAC,SAAS;IAClC,MAAM,kBAAkB,SAAS;IAEjC,IAAI,UAAU,GAAG;QACf,OAAO,GAAG,gBAAgB,MAAM,EAAE,oBAAoB,IAAI,MAAM,IAAI;IACtE,OAAO,IAAI,oBAAoB,GAAG;QAChC,OAAO,GAAG,MAAM,KAAK,EAAE,UAAU,IAAI,MAAM,IAAI;IACjD,OAAO;QACL,OAAO,GAAG,MAAM,KAAK,EAAE,UAAU,IAAI,MAAM,GAAG,EAAE,EAAE,gBAAgB,MAAM,EAAE,oBAAoB,IAAI,MAAM,IAAI;IAC9G;AACF;AAOO,SAAS,8BACd,WAA6B;IAE7B,MAAM,SAAiC,CAAC;IAExC,0BAA0B;IAC1B,MAAM,cAAc;WAAI,IAAI,IAAI,YAC7B,MAAM,CAAC,CAAA,MAAO,IAAI,UAAU,EAC5B,GAAG,CAAC,CAAA,MAAO,IAAI,UAAU;KAAa;IAEzC,yCAAyC;IACzC,KAAK,MAAM,cAAc,YAAa;QACpC,MAAM,SAAS,yBAAyB,aAAa;QACrD,MAAM,CAAC,WAAW,GAAG,yBAAyB;IAChD;IAEA,uBAAuB;IACvB,MAAM,cAAc,yBAAyB;IAC7C,OAAO,KAAK,GAAG,yBAAyB;IAExC,OAAO;AACT", "debugId": null}}, {"offset": {"line": 178, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/data/positions.ts"], "sourcesContent": ["import { Position } from \"@/types\";\n\n// Primjer standardiziranih pozicija na više jezika\nexport const positions: Position[] = [\n  {\n    id: \"electrician\",\n    translations: {\n      de: \"Elektriker\",\n      en: \"Electrician\",\n      bs: \"Elek<PERSON><PERSON><PERSON>\",\n      hr: \"Elektri<PERSON><PERSON>\",\n      sr: \"Električar\",\n      me: \"Električ<PERSON>\",\n      mk: \"Електричар\",\n      al: \"Elektricist\"\n    },\n    category: \"construction\",\n    skills: [\"electrical-wiring\", \"electrical-maintenance\", \"electrical-installation\"]\n  },\n  {\n    id: \"plumber\",\n    translations: {\n      de: \"<PERSON><PERSON><PERSON><PERSON>\",\n      en: \"Plumber\",\n      bs: \"Vodoinstalater\",\n      hr: \"Vodoinstalater\",\n      sr: \"Vodoinstalater\",\n      me: \"Vodoinstalater\",\n      mk: \"Водоводџија\",\n      al: \"Hidraulik\"\n    },\n    category: \"construction\",\n    skills: [\"plumbing\", \"pipe-fitting\", \"water-systems\"]\n  },\n  {\n    id: \"carpenter\",\n    translations: {\n      de: \"Tischler\",\n      en: \"Carpenter\",\n      bs: \"Stolar\",\n      hr: \"Stolar\",\n      sr: \"Stolar\",\n      me: \"Stolar\",\n      mk: \"Столар\",\n      al: \"Marangoz\"\n    },\n    category: \"construction\",\n    skills: [\"woodworking\", \"furniture-making\", \"carpentry\"]\n  },\n  {\n    id: \"welder\",\n    translations: {\n      de: \"Schweißer\",\n      en: \"Welder\",\n      bs: \"Zavarivač\",\n      hr: \"Zavarivač\",\n      sr: \"Zavarivač\",\n      me: \"Zavarivač\",\n      mk: \"Заварувач\",\n      al: \"Saldator\"\n    },\n    category: \"manufacturing\",\n    skills: [\"welding\", \"metal-fabrication\", \"blueprint-reading\"]\n  },\n  {\n    id: \"chef\",\n    translations: {\n      de: \"Koch\",\n      en: \"Chef\",\n      bs: \"Kuhar\",\n      hr: \"Kuhar\",\n      sr: \"Kuvar\",\n      me: \"Kuvar\",\n      mk: \"Готвач\",\n      al: \"Kuzhinier\"\n    },\n    category: \"hospitality\",\n    skills: [\"cooking\", \"food-preparation\", \"menu-planning\"]\n  },\n  {\n    id: \"waiter\",\n    translations: {\n      de: \"Kellner\",\n      en: \"Waiter\",\n      bs: \"Konobar\",\n      hr: \"Konobar\",\n      sr: \"Konobar\",\n      me: \"Konobar\",\n      mk: \"Келнер\",\n      al: \"Kamarier\"\n    },\n    category: \"hospitality\",\n    skills: [\"customer-service\", \"food-service\", \"order-taking\"]\n  },\n  {\n    id: \"nurse\",\n    translations: {\n      de: \"Krankenpfleger\",\n      en: \"Nurse\",\n      bs: \"Medicinska sestra/tehničar\",\n      hr: \"Medicinska sestra/tehničar\",\n      sr: \"Medicinska sestra/tehničar\",\n      me: \"Medicinska sestra/tehničar\",\n      mk: \"Медицинска сестра/техничар\",\n      al: \"Infermier\"\n    },\n    category: \"healthcare\",\n    skills: [\"patient-care\", \"medical-assistance\", \"health-monitoring\"]\n  },\n  {\n    id: \"caregiver\",\n    translations: {\n      de: \"Pflegekraft\",\n      en: \"Caregiver\",\n      bs: \"Njegovatelj\",\n      hr: \"Njegovatelj\",\n      sr: \"Negovatelj\",\n      me: \"Njegovatelj\",\n      mk: \"Негувател\",\n      al: \"Kujdestar\"\n    },\n    category: \"healthcare\",\n    skills: [\"elderly-care\", \"personal-care\", \"medication-management\"]\n  },\n  {\n    id: \"driver\",\n    translations: {\n      de: \"Fahrer\",\n      en: \"Driver\",\n      bs: \"Vozač\",\n      hr: \"Vozač\",\n      sr: \"Vozač\",\n      me: \"Vozač\",\n      mk: \"Возач\",\n      al: \"Shofer\"\n    },\n    category: \"transportation\",\n    skills: [\"driving\", \"vehicle-maintenance\", \"route-planning\"]\n  },\n  {\n    id: \"software-developer\",\n    translations: {\n      de: \"Softwareentwickler\",\n      en: \"Software Developer\",\n      bs: \"Programer\",\n      hr: \"Programer\",\n      sr: \"Programer\",\n      me: \"Programer\",\n      mk: \"Програмер\",\n      al: \"Zhvillues Softueri\"\n    },\n    category: \"it\",\n    skills: [\"programming\", \"software-development\", \"problem-solving\"]\n  },\n  {\n    id: \"accountant\",\n    translations: {\n      de: \"Buchhalter\",\n      en: \"Accountant\",\n      bs: \"Računovođa\",\n      hr: \"Računovođa\",\n      sr: \"Računovođa\",\n      me: \"Računovođa\",\n      mk: \"Сметководител\",\n      al: \"Kontabilist\"\n    },\n    category: \"finance\",\n    skills: [\"accounting\", \"bookkeeping\", \"financial-reporting\"]\n  },\n  {\n    id: \"teacher\",\n    translations: {\n      de: \"Lehrer\",\n      en: \"Teacher\",\n      bs: \"Nastavnik\",\n      hr: \"Nastavnik\",\n      sr: \"Nastavnik\",\n      me: \"Nastavnik\",\n      mk: \"Наставник\",\n      al: \"Mësues\"\n    },\n    category: \"education\",\n    skills: [\"teaching\", \"curriculum-development\", \"student-assessment\"]\n  },\n  {\n    id: \"sales-representative\",\n    translations: {\n      de: \"Vertriebsmitarbeiter\",\n      en: \"Sales Representative\",\n      bs: \"Prodajni predstavnik\",\n      hr: \"Prodajni predstavnik\",\n      sr: \"Prodajni predstavnik\",\n      me: \"Prodajni predstavnik\",\n      mk: \"Продажен претставник\",\n      al: \"Përfaqësues Shitjesh\"\n    },\n    category: \"sales\",\n    skills: [\"sales\", \"customer-relations\", \"negotiation\"]\n  },\n  {\n    id: \"warehouse-worker\",\n    translations: {\n      de: \"Lagerarbeiter\",\n      en: \"Warehouse Worker\",\n      bs: \"Skladištar\",\n      hr: \"Skladištar\",\n      sr: \"Magacioner\",\n      me: \"Magacioner\",\n      mk: \"Магационер\",\n      al: \"Punëtor Magazinimi\"\n    },\n    category: \"logistics\",\n    skills: [\"inventory-management\", \"forklift-operation\", \"order-picking\"]\n  },\n  {\n    id: \"cleaner\",\n    translations: {\n      de: \"Reinigungskraft\",\n      en: \"Cleaner\",\n      bs: \"Čistač\",\n      hr: \"Čistač\",\n      sr: \"Čistač\",\n      me: \"Čistač\",\n      mk: \"Хигиеничар\",\n      al: \"Pastrues\"\n    },\n    category: \"facility-services\",\n    skills: [\"cleaning\", \"sanitation\", \"housekeeping\"]\n  }\n];\n"], "names": [], "mappings": ";;;AAGO,MAAM,YAAwB;IACnC;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAqB;YAA0B;SAA0B;IACpF;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAY;YAAgB;SAAgB;IACvD;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAe;YAAoB;SAAY;IAC1D;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAW;YAAqB;SAAoB;IAC/D;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAW;YAAoB;SAAgB;IAC1D;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAoB;YAAgB;SAAe;IAC9D;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAgB;YAAsB;SAAoB;IACrE;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAgB;YAAiB;SAAwB;IACpE;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAW;YAAuB;SAAiB;IAC9D;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAe;YAAwB;SAAkB;IACpE;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAc;YAAe;SAAsB;IAC9D;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAY;YAA0B;SAAqB;IACtE;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAS;YAAsB;SAAc;IACxD;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAwB;YAAsB;SAAgB;IACzE;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAY;YAAc;SAAe;IACpD;CACD", "debugId": null}}, {"offset": {"line": 477, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/data/countries.ts"], "sourcesContent": ["import { Country } from \"@/types\";\n\nexport const countries: Country[] = [\n  {\n    id: \"de\",\n    name: \"Germany\",\n    code: \"DE\",\n    flagUrl: \"/flags/de.svg\",\n    region: \"eu\"\n  },\n  {\n    id: \"at\",\n    name: \"Austria\",\n    code: \"AT\",\n    flagUrl: \"/flags/at.svg\",\n    region: \"eu\"\n  },\n  {\n    id: \"hr\",\n    name: \"Croatia\",\n    code: \"HR\",\n    flagUrl: \"/flags/hr.svg\",\n    region: \"eu\"\n  },\n  {\n    id: \"si\",\n    name: \"Slovenia\",\n    code: \"SI\",\n    flagUrl: \"/flags/si.svg\",\n    region: \"eu\"\n  },\n  {\n    id: \"ba\",\n    name: \"Bosnia and Herzegovina\",\n    code: \"BA\",\n    flagUrl: \"/flags/ba.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"rs\",\n    name: \"Serbia\",\n    code: \"RS\",\n    flagUrl: \"/flags/rs.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"me\",\n    name: \"Montenegro\",\n    code: \"ME\",\n    flagUrl: \"/flags/me.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"mk\",\n    name: \"North Macedonia\",\n    code: \"MK\",\n    flagUrl: \"/flags/mk.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"al\",\n    name: \"Albania\",\n    code: \"AL\",\n    flagUrl: \"/flags/al.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"bg\",\n    name: \"Bulgaria\",\n    code: \"BG\",\n    flagUrl: \"/flags/bg.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"ro\",\n    name: \"Romania\",\n    code: \"RO\",\n    flagUrl: \"/flags/ro.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"gr\",\n    name: \"Greece\",\n    code: \"GR\",\n    flagUrl: \"/flags/gr.svg\",\n    region: \"balkans\"\n  }\n];\n"], "names": [], "mappings": ";;;AAEO,MAAM,YAAuB;IAClC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;CACD", "debugId": null}}, {"offset": {"line": 575, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/components/candidate/ExperienceDisplay.tsx"], "sourcesContent": ["import { WorkExperience } from \"@/types\";\nimport { calculateExperienceByPosition, formatExperienceDuration } from \"@/utils/experienceCalculator\";\nimport { positions } from \"@/data/positions\";\nimport { countries } from \"@/data/countries\";\n\ninterface ExperienceDisplayProps {\n  experiences: WorkExperience[];\n  noWorkExperience?: boolean;\n}\n\nexport default function ExperienceDisplay({ experiences, noWorkExperience }: ExperienceDisplayProps) {\n  if (noWorkExperience) {\n    return (\n      <div className=\"text-neutral-600 dark:text-neutral-400 italic\">\n        No work experience\n      </div>\n    );\n  }\n\n  if (!experiences || experiences.length === 0) {\n    return (\n      <div className=\"text-neutral-600 dark:text-neutral-400 italic\">\n        No work experience provided\n      </div>\n    );\n  }\n\n  // Sort experiences by date (most recent first)\n  const sortedExperiences = [...experiences].sort((a, b) => {\n    const dateA = a.current ? new Date() : new Date(a.endDate!);\n    const dateB = b.current ? new Date() : new Date(b.endDate!);\n    return dateB.getTime() - dateA.getTime();\n  });\n\n  // Calculate experience by position\n  const experienceByPosition = calculateExperienceByPosition(experiences);\n\n  return (\n    <div className=\"space-y-6\">\n      <div className=\"bg-neutral-50 dark:bg-neutral-800 p-4 rounded-lg border border-neutral-200 dark:border-neutral-700\">\n        <h4 className=\"font-medium mb-2\">Total Experience</h4>\n        <div className=\"text-lg font-bold\">{experienceByPosition.total}</div>\n        \n        {Object.entries(experienceByPosition)\n          .filter(([key]) => key !== 'total')\n          .map(([positionId, duration]) => {\n            const position = positions.find(p => p.id === positionId);\n            if (!position) return null;\n            \n            return (\n              <div key={positionId} className=\"mt-2 flex items-center justify-between\">\n                <span>{position.translations.en}</span>\n                <span className=\"text-neutral-600 dark:text-neutral-400\">{duration}</span>\n              </div>\n            );\n          })}\n      </div>\n\n      {sortedExperiences.map((experience, index) => {\n        const startDate = new Date(experience.startDate);\n        const endDate = experience.current ? new Date() : new Date(experience.endDate!);\n        \n        const formattedStartDate = `${startDate.toLocaleString('default', { month: 'short' })} ${startDate.getFullYear()}`;\n        const formattedEndDate = experience.current \n          ? 'Present' \n          : `${endDate.toLocaleString('default', { month: 'short' })} ${endDate.getFullYear()}`;\n        \n        const position = experience.positionId \n          ? positions.find(p => p.id === experience.positionId) \n          : null;\n          \n        const country = experience.country \n          ? countries.find(c => c.id === experience.country) \n          : null;\n        \n        return (\n          <div key={index} className=\"border-l-2 border-neutral-300 dark:border-neutral-700 pl-4 pb-6 relative\">\n            <div className=\"absolute w-3 h-3 bg-neutral-300 dark:bg-neutral-700 rounded-full -left-[7px] top-1\"></div>\n            \n            <div className=\"flex flex-col md:flex-row md:justify-between md:items-start\">\n              <div>\n                <h4 className=\"font-medium\">{experience.position}</h4>\n                <div className=\"text-neutral-600 dark:text-neutral-400\">{experience.company}</div>\n              </div>\n              <div className=\"text-sm text-neutral-500 dark:text-neutral-400 mt-1 md:mt-0 md:text-right\">\n                {formattedStartDate} - {formattedEndDate}\n              </div>\n            </div>\n            \n            {(position || country || experience.industry) && (\n              <div className=\"flex flex-wrap gap-2 mt-2\">\n                {position && (\n                  <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300\">\n                    {position.translations.en}\n                  </span>\n                )}\n                \n                {country && (\n                  <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300\">\n                    {country.name}\n                  </span>\n                )}\n                \n                {experience.industry && (\n                  <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300\">\n                    {experience.industry.charAt(0).toUpperCase() + experience.industry.slice(1)}\n                  </span>\n                )}\n              </div>\n            )}\n            \n            {experience.description && (\n              <div className=\"mt-2 text-sm text-neutral-600 dark:text-neutral-400\">\n                {experience.description}\n              </div>\n            )}\n          </div>\n        );\n      })}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAOe,SAAS,kBAAkB,EAAE,WAAW,EAAE,gBAAgB,EAA0B;IACjG,IAAI,kBAAkB;QACpB,qBACE,6LAAC;YAAI,WAAU;sBAAgD;;;;;;IAInE;IAEA,IAAI,CAAC,eAAe,YAAY,MAAM,KAAK,GAAG;QAC5C,qBACE,6LAAC;YAAI,WAAU;sBAAgD;;;;;;IAInE;IAEA,+CAA+C;IAC/C,MAAM,oBAAoB;WAAI;KAAY,CAAC,IAAI,CAAC,CAAC,GAAG;QAClD,MAAM,QAAQ,EAAE,OAAO,GAAG,IAAI,SAAS,IAAI,KAAK,EAAE,OAAO;QACzD,MAAM,QAAQ,EAAE,OAAO,GAAG,IAAI,SAAS,IAAI,KAAK,EAAE,OAAO;QACzD,OAAO,MAAM,OAAO,KAAK,MAAM,OAAO;IACxC;IAEA,mCAAmC;IACnC,MAAM,uBAAuB,CAAA,GAAA,uIAAA,CAAA,gCAA6B,AAAD,EAAE;IAE3D,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmB;;;;;;kCACjC,6LAAC;wBAAI,WAAU;kCAAqB,qBAAqB,KAAK;;;;;;oBAE7D,OAAO,OAAO,CAAC,sBACb,MAAM,CAAC,CAAC,CAAC,IAAI,GAAK,QAAQ,SAC1B,GAAG,CAAC,CAAC,CAAC,YAAY,SAAS;wBAC1B,MAAM,WAAW,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;wBAC9C,IAAI,CAAC,UAAU,OAAO;wBAEtB,qBACE,6LAAC;4BAAqB,WAAU;;8CAC9B,6LAAC;8CAAM,SAAS,YAAY,CAAC,EAAE;;;;;;8CAC/B,6LAAC;oCAAK,WAAU;8CAA0C;;;;;;;2BAFlD;;;;;oBAKd;;;;;;;YAGH,kBAAkB,GAAG,CAAC,CAAC,YAAY;gBAClC,MAAM,YAAY,IAAI,KAAK,WAAW,SAAS;gBAC/C,MAAM,UAAU,WAAW,OAAO,GAAG,IAAI,SAAS,IAAI,KAAK,WAAW,OAAO;gBAE7E,MAAM,qBAAqB,GAAG,UAAU,cAAc,CAAC,WAAW;oBAAE,OAAO;gBAAQ,GAAG,CAAC,EAAE,UAAU,WAAW,IAAI;gBAClH,MAAM,mBAAmB,WAAW,OAAO,GACvC,YACA,GAAG,QAAQ,cAAc,CAAC,WAAW;oBAAE,OAAO;gBAAQ,GAAG,CAAC,EAAE,QAAQ,WAAW,IAAI;gBAEvF,MAAM,WAAW,WAAW,UAAU,GAClC,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,UAAU,IAClD;gBAEJ,MAAM,UAAU,WAAW,OAAO,GAC9B,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,WAAW,OAAO,IAC/C;gBAEJ,qBACE,6LAAC;oBAAgB,WAAU;;sCACzB,6LAAC;4BAAI,WAAU;;;;;;sCAEf,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;;sDACC,6LAAC;4CAAG,WAAU;sDAAe,WAAW,QAAQ;;;;;;sDAChD,6LAAC;4CAAI,WAAU;sDAA0C,WAAW,OAAO;;;;;;;;;;;;8CAE7E,6LAAC;oCAAI,WAAU;;wCACZ;wCAAmB;wCAAI;;;;;;;;;;;;;wBAI3B,CAAC,YAAY,WAAW,WAAW,QAAQ,mBAC1C,6LAAC;4BAAI,WAAU;;gCACZ,0BACC,6LAAC;oCAAK,WAAU;8CACb,SAAS,YAAY,CAAC,EAAE;;;;;;gCAI5B,yBACC,6LAAC;oCAAK,WAAU;8CACb,QAAQ,IAAI;;;;;;gCAIhB,WAAW,QAAQ,kBAClB,6LAAC;oCAAK,WAAU;8CACb,WAAW,QAAQ,CAAC,MAAM,CAAC,GAAG,WAAW,KAAK,WAAW,QAAQ,CAAC,KAAK,CAAC;;;;;;;;;;;;wBAMhF,WAAW,WAAW,kBACrB,6LAAC;4BAAI,WAAU;sCACZ,WAAW,WAAW;;;;;;;mBArCnB;;;;;YA0Cd;;;;;;;AAGN;KA/GwB", "debugId": null}}, {"offset": {"line": 806, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/data/degrees.ts"], "sourcesContent": ["import { Degree } from \"@/types\";\n\n// Simplified standardized educational degrees in multiple languages\nexport const degrees: Degree[] = [\n  {\n    id: \"primary-school\",\n    translations: {\n      de: \"Grundschule\",\n      en: \"Primary School\",\n      bs: \"<PERSON>snovna škola\",\n      hr: \"<PERSON>snovna škola\",\n      sr: \"<PERSON>snovna škola\",\n      me: \"Osnovna škola\",\n      mk: \"Основно училиште\",\n      al: \"Shkollë fillore\"\n    },\n    level: \"primary\"\n  },\n  {\n    id: \"high-school\",\n    translations: {\n      de: \"Mittelschule\",\n      en: \"High School\",\n      bs: \"Srednja škola\",\n      hr: \"Srednja škola\",\n      sr: \"Srednja škola\",\n      me: \"Srednja škola\",\n      mk: \"Средно училиште\",\n      al: \"Shkollë e mesme\"\n    },\n    level: \"secondary\"\n  },\n  {\n    id: \"bachelors-degree\",\n    translations: {\n      de: \"Bachelor\",\n      en: \"Bachelor's Degree\",\n      bs: \"Bakal<PERSON>reat\",\n      hr: \"Prvostupnik\",\n      sr: \"Osnovne akademske studije\",\n      me: \"Osnovne akademske studije\",\n      mk: \"Додипломски студии\",\n      al: \"Diplomë Bachelor\"\n    },\n    level: \"bachelor\"\n  },\n  {\n    id: \"masters-degree\",\n    translations: {\n      de: \"Master\",\n      en: \"Master's Degree\",\n      bs: \"Magistar\",\n      hr: \"Magistar\",\n      sr: \"Master\",\n      me: \"Master\",\n      mk: \"Магистерски студии\",\n      al: \"Diplomë Master\"\n    },\n    level: \"master\"\n  },\n  {\n    id: \"doctorate\",\n    translations: {\n      de: \"Doktor\",\n      en: \"Doctorate\",\n      bs: \"Doktorat\",\n      hr: \"Doktorat\",\n      sr: \"Doktorat\",\n      me: \"Doktorat\",\n      mk: \"Докторат\",\n      al: \"Doktoraturë\"\n    },\n    level: \"doctorate\"\n  }\n];\n"], "names": [], "mappings": ";;;AAGO,MAAM,UAAoB;IAC/B;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,OAAO;IACT;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,OAAO;IACT;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,OAAO;IACT;IACA;QACE,IAAI;QAC<PERSON>,cAAc;Y<PERSON><PERSON>,IAAI;Y<PERSON><PERSON>,IAAI;<PERSON><PERSON><PERSON>,IAAI;YACJ,IAAI;YACJ,IAAI;<PERSON><PERSON><PERSON>,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,OAAO;IACT;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,OAAO;IACT;CACD", "debugId": null}}, {"offset": {"line": 890, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/data/studyFields.ts"], "sourcesContent": ["import { StudyField } from \"@/types\";\n\n// Standardized study fields in multiple languages\nexport const studyFields: StudyField[] = [\n  {\n    id: \"electrical-engineering\",\n    translations: {\n      de: \"Elektrotechnik\",\n      en: \"Electrical Engineering\",\n      bs: \"Elektrotehnika\",\n      hr: \"Elektrotehnika\",\n      sr: \"Elektrotehnika\",\n      me: \"Elektrotehn<PERSON>\",\n      mk: \"Електротехника\",\n      al: \"Inxhinieri Elektrike\"\n    },\n    category: \"engineering\"\n  },\n  {\n    id: \"mechanical-engineering\",\n    translations: {\n      de: \"Maschinenbau\",\n      en: \"Mechanical Engineering\",\n      bs: \"Mašinstvo\",\n      hr: \"St<PERSON>jarst<PERSON>\",\n      sr: \"<PERSON><PERSON>inst<PERSON>\",\n      me: \"<PERSON><PERSON>instvo\",\n      mk: \"Машинство\",\n      al: \"Inxhinieri Mekanike\"\n    },\n    category: \"engineering\"\n  },\n  {\n    id: \"automotive-mechanic\",\n    translations: {\n      de: \"Kfz-Mechaniker\",\n      en: \"Automotive Mechanic\",\n      bs: \"Automehaničar\",\n      hr: \"Autome<PERSON><PERSON>ar\",\n      sr: \"Automehaničar\",\n      me: \"Automehaničar\",\n      mk: \"Автомеханичар\",\n      al: \"Mekanik Automjetesh\"\n    },\n    category: \"vocational\"\n  },\n  {\n    id: \"automotive-painter\",\n    translations: {\n      de: \"Kfz-Lackierer\",\n      en: \"Automotive Painter\",\n      bs: \"Autolakirer\",\n      hr: \"Autolakirer\",\n      sr: \"Autolakirer\",\n      me: \"Autolakirer\",\n      mk: \"Автолакер\",\n      al: \"Bojaxhi Automjetesh\"\n    },\n    category: \"vocational\"\n  },\n  {\n    id: \"carpentry\",\n    translations: {\n      de: \"Tischlerei\",\n      en: \"Carpentry\",\n      bs: \"Stolarstvo\",\n      hr: \"Stolarstvo\",\n      sr: \"Stolarstvo\",\n      me: \"Stolarstvo\",\n      mk: \"Столарство\",\n      al: \"Marangozi\"\n    },\n    category: \"vocational\"\n  },\n  {\n    id: \"plumbing\",\n    translations: {\n      de: \"Sanitärinstallation\",\n      en: \"Plumbing\",\n      bs: \"Vodoinstalaterstvo\",\n      hr: \"Vodoinstalaterstvo\",\n      sr: \"Vodoinstalaterstvo\",\n      me: \"Vodoinstalaterstvo\",\n      mk: \"Водоинсталатерство\",\n      al: \"Hidraulikë\"\n    },\n    category: \"vocational\"\n  },\n  {\n    id: \"welding\",\n    translations: {\n      de: \"Schweißen\",\n      en: \"Welding\",\n      bs: \"Zavarivanje\",\n      hr: \"Zavarivanje\",\n      sr: \"Zavarivanje\",\n      me: \"Zavarivanje\",\n      mk: \"Заварување\",\n      al: \"Saldim\"\n    },\n    category: \"vocational\"\n  },\n  {\n    id: \"culinary-arts\",\n    translations: {\n      de: \"Kochkunst\",\n      en: \"Culinary Arts\",\n      bs: \"Kuharstvo\",\n      hr: \"Kuharstvo\",\n      sr: \"Kuvarstvo\",\n      me: \"Kuvarstvo\",\n      mk: \"Готварство\",\n      al: \"Arti Kulinar\"\n    },\n    category: \"hospitality\"\n  },\n  {\n    id: \"nursing\",\n    translations: {\n      de: \"Krankenpflege\",\n      en: \"Nursing\",\n      bs: \"Sestrinstvo\",\n      hr: \"Sestrinstvo\",\n      sr: \"Sestrinstvo\",\n      me: \"Sestrinstvo\",\n      mk: \"Медицинска нега\",\n      al: \"Infermieri\"\n    },\n    category: \"healthcare\"\n  },\n  {\n    id: \"general-high-school\",\n    translations: {\n      de: \"Allgemeine Hochschulreife\",\n      en: \"General High School\",\n      bs: \"Gimnazija\",\n      hr: \"Gimnazija\",\n      sr: \"Gimnazija\",\n      me: \"Gimnazija\",\n      mk: \"Гимназија\",\n      al: \"Gjimnaz\"\n    },\n    category: \"general-education\"\n  }\n];\n"], "names": [], "mappings": ";;;AAGO,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;Q<PERSON><PERSON>,cAAc;Y<PERSON><PERSON>,IAAI;Y<PERSON><PERSON>,IAAI;<PERSON><PERSON><PERSON>,IAAI;Y<PERSON><PERSON>,IAAI;YACJ,IAAI;<PERSON><PERSON><PERSON>,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;CACD", "debugId": null}}, {"offset": {"line": 1044, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/components/candidate/EducationDisplay.tsx"], "sourcesContent": ["import { Education } from \"@/types\";\nimport { degrees } from \"@/data/degrees\";\nimport { studyFields } from \"@/data/studyFields\";\nimport { countries } from \"@/data/countries\";\n\ninterface EducationDisplayProps {\n  educations: Education[];\n}\n\nexport default function EducationDisplay({ educations }: EducationDisplayProps) {\n  if (!educations || !Array.isArray(educations) || educations.length === 0) {\n    return (\n      <div className=\"text-neutral-600 dark:text-neutral-400 italic\">\n        No education provided\n      </div>\n    );\n  }\n\n  // Sort educations by date (most recent first)\n  const sortedEducations = Array.isArray(educations) ? [...educations].sort((a, b) => {\n    const dateA = new Date(a.endDate!);\n    const dateB = new Date(b.endDate!);\n    return dateB.getTime() - dateA.getTime();\n  }) : [];\n\n  return (\n    <div className=\"space-y-6\">\n      {sortedEducations.map((education, index) => {\n        const startDate = new Date(education.startDate);\n        const endDate = new Date(education.endDate!);\n\n        const formattedStartDate = `${startDate.toLocaleString('default', { month: 'short' })} ${startDate.getFullYear()}`;\n        const formattedEndDate = `${endDate.toLocaleString('default', { month: 'short' })} ${endDate.getFullYear()}`;\n\n        const degree = education.degreeId\n          ? degrees.find(d => d.id === education.degreeId)\n          : null;\n\n        const field = education.fieldId\n          ? studyFields.find(f => f.id === education.fieldId)\n          : null;\n\n        const country = education.country\n          ? countries.find(c => c.id === education.country)\n          : null;\n\n        return (\n          <div key={index} className=\"border-l-2 border-neutral-300 dark:border-neutral-700 pl-4 pb-6 relative\">\n            <div className=\"absolute w-3 h-3 bg-neutral-300 dark:bg-neutral-700 rounded-full -left-[7px] top-1\"></div>\n\n            <div className=\"flex flex-col md:flex-row md:justify-between md:items-start\">\n              <div>\n                <h4 className=\"font-medium\">{education.degree}</h4>\n                <div className=\"text-neutral-600 dark:text-neutral-400\">{education.field}</div>\n              </div>\n              <div className=\"text-sm text-neutral-500 dark:text-neutral-400 mt-1 md:mt-0 md:text-right\">\n                {formattedStartDate} - {formattedEndDate}\n              </div>\n            </div>\n\n            {(degree || field || country) && (\n              <div className=\"flex flex-wrap gap-2 mt-2\">\n                {degree && (\n                  <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300\">\n                    {degree.translations.en}\n                  </span>\n                )}\n\n                {field && (\n                  <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300\">\n                    {field.translations.en}\n                  </span>\n                )}\n\n                {country && (\n                  <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300\">\n                    {country.name}\n                  </span>\n                )}\n              </div>\n            )}\n\n            {education.description && (\n              <div className=\"mt-2 text-sm text-neutral-600 dark:text-neutral-400\">\n                {education.description}\n              </div>\n            )}\n          </div>\n        );\n      })}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AACA;AACA;AACA;;;;;AAMe,SAAS,iBAAiB,EAAE,UAAU,EAAyB;IAC5E,IAAI,CAAC,cAAc,CAAC,MAAM,OAAO,CAAC,eAAe,WAAW,MAAM,KAAK,GAAG;QACxE,qBACE,6LAAC;YAAI,WAAU;sBAAgD;;;;;;IAInE;IAEA,8CAA8C;IAC9C,MAAM,mBAAmB,MAAM,OAAO,CAAC,cAAc;WAAI;KAAW,CAAC,IAAI,CAAC,CAAC,GAAG;QAC5E,MAAM,QAAQ,IAAI,KAAK,EAAE,OAAO;QAChC,MAAM,QAAQ,IAAI,KAAK,EAAE,OAAO;QAChC,OAAO,MAAM,OAAO,KAAK,MAAM,OAAO;IACxC,KAAK,EAAE;IAEP,qBACE,6LAAC;QAAI,WAAU;kBACZ,iBAAiB,GAAG,CAAC,CAAC,WAAW;YAChC,MAAM,YAAY,IAAI,KAAK,UAAU,SAAS;YAC9C,MAAM,UAAU,IAAI,KAAK,UAAU,OAAO;YAE1C,MAAM,qBAAqB,GAAG,UAAU,cAAc,CAAC,WAAW;gBAAE,OAAO;YAAQ,GAAG,CAAC,EAAE,UAAU,WAAW,IAAI;YAClH,MAAM,mBAAmB,GAAG,QAAQ,cAAc,CAAC,WAAW;gBAAE,OAAO;YAAQ,GAAG,CAAC,EAAE,QAAQ,WAAW,IAAI;YAE5G,MAAM,SAAS,UAAU,QAAQ,GAC7B,yHAAA,CAAA,UAAO,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,UAAU,QAAQ,IAC7C;YAEJ,MAAM,QAAQ,UAAU,OAAO,GAC3B,6HAAA,CAAA,cAAW,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,UAAU,OAAO,IAChD;YAEJ,MAAM,UAAU,UAAU,OAAO,GAC7B,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,UAAU,OAAO,IAC9C;YAEJ,qBACE,6LAAC;gBAAgB,WAAU;;kCACzB,6LAAC;wBAAI,WAAU;;;;;;kCAEf,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;kDAAe,UAAU,MAAM;;;;;;kDAC7C,6LAAC;wCAAI,WAAU;kDAA0C,UAAU,KAAK;;;;;;;;;;;;0CAE1E,6LAAC;gCAAI,WAAU;;oCACZ;oCAAmB;oCAAI;;;;;;;;;;;;;oBAI3B,CAAC,UAAU,SAAS,OAAO,mBAC1B,6LAAC;wBAAI,WAAU;;4BACZ,wBACC,6LAAC;gCAAK,WAAU;0CACb,OAAO,YAAY,CAAC,EAAE;;;;;;4BAI1B,uBACC,6LAAC;gCAAK,WAAU;0CACb,MAAM,YAAY,CAAC,EAAE;;;;;;4BAIzB,yBACC,6LAAC;gCAAK,WAAU;0CACb,QAAQ,IAAI;;;;;;;;;;;;oBAMpB,UAAU,WAAW,kBACpB,6LAAC;wBAAI,WAAU;kCACZ,UAAU,WAAW;;;;;;;eArClB;;;;;QA0Cd;;;;;;AAGN;KAnFwB", "debugId": null}}, {"offset": {"line": 1209, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/components/candidate/LanguageSkillsDisplay.tsx"], "sourcesContent": ["import { LanguageLevel, LanguageSkill } from \"@/types\";\n\ninterface LanguageSkillsDisplayProps {\n  germanLevel: LanguageLevel;\n  englishLevel: LanguageLevel;\n  otherLanguages?: LanguageSkill[];\n}\n\nexport default function LanguageSkillsDisplay({\n  germanLevel,\n  englishLevel,\n  otherLanguages = [],\n}: LanguageSkillsDisplayProps) {\n  return (\n    <div className=\"space-y-4\">\n      <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n        <div className=\"bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg p-4\">\n          <div className=\"flex justify-between items-center mb-2\">\n            <h4 className=\"font-medium\">German</h4>\n            <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300\">\n              {germanLevel}\n            </span>\n          </div>\n          <div className=\"w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2.5\">\n            <div\n              className=\"bg-blue-600 h-2.5 rounded-full\"\n              style={{ width: `${getLanguageLevelPercentage(germanLevel)}%` }}\n            ></div>\n          </div>\n          <div className=\"mt-1 text-xs text-neutral-500 dark:text-neutral-400\">\n            {getLanguageLevelDescription(germanLevel)}\n          </div>\n        </div>\n\n        <div className=\"bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg p-4\">\n          <div className=\"flex justify-between items-center mb-2\">\n            <h4 className=\"font-medium\">English</h4>\n            <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300\">\n              {englishLevel}\n            </span>\n          </div>\n          <div className=\"w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2.5\">\n            <div\n              className=\"bg-green-600 h-2.5 rounded-full\"\n              style={{ width: `${getLanguageLevelPercentage(englishLevel)}%` }}\n            ></div>\n          </div>\n          <div className=\"mt-1 text-xs text-neutral-500 dark:text-neutral-400\">\n            {getLanguageLevelDescription(englishLevel)}\n          </div>\n        </div>\n      </div>\n\n      {otherLanguages.length > 0 && (\n        <div className=\"mt-4\">\n          <h4 className=\"font-medium mb-3\">Other Languages</h4>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-4\">\n            {otherLanguages.map((language, index) => (\n              <div\n                key={index}\n                className=\"bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg p-4\"\n              >\n                <div className=\"flex justify-between items-center mb-2\">\n                  <h4 className=\"font-medium\">{language.language}</h4>\n                  <span className=\"inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300\">\n                    {language.level}\n                  </span>\n                </div>\n                <div className=\"w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2.5\">\n                  <div\n                    className=\"bg-purple-600 h-2.5 rounded-full\"\n                    style={{ width: `${getLanguageLevelPercentage(language.level)}%` }}\n                  ></div>\n                </div>\n                <div className=\"mt-1 text-xs text-neutral-500 dark:text-neutral-400\">\n                  {getLanguageLevelDescription(language.level)}\n                </div>\n              </div>\n            ))}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n\nfunction getLanguageLevelPercentage(level: LanguageLevel): number {\n  switch (level) {\n    case \"A1\":\n      return 16.7;\n    case \"A2\":\n      return 33.3;\n    case \"B1\":\n      return 50;\n    case \"B2\":\n      return 66.7;\n    case \"C1\":\n      return 83.3;\n    case \"C2\":\n    case \"Native\":\n      return 100;\n    default:\n      return 0;\n  }\n}\n\nfunction getLanguageLevelDescription(level: LanguageLevel): string {\n  switch (level) {\n    case \"A1\":\n      return \"Beginner - Can understand and use familiar everyday expressions\";\n    case \"A2\":\n      return \"Elementary - Can communicate in simple and routine tasks\";\n    case \"B1\":\n      return \"Intermediate - Can deal with most situations likely to arise while traveling\";\n    case \"B2\":\n      return \"Upper Intermediate - Can interact with a degree of fluency and spontaneity\";\n    case \"C1\":\n      return \"Advanced - Can express ideas fluently and spontaneously without much searching for expressions\";\n    case \"C2\":\n      return \"Proficient - Can understand with ease virtually everything heard or read\";\n    case \"Native\":\n      return \"Native Speaker - Mother tongue proficiency\";\n    default:\n      return \"\";\n  }\n}\n"], "names": [], "mappings": ";;;;;AAQe,SAAS,sBAAsB,EAC5C,WAAW,EACX,YAAY,EACZ,iBAAiB,EAAE,EACQ;IAC3B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAc;;;;;;kDAC5B,6LAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;0CAGL,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,2BAA2B,aAAa,CAAC,CAAC;oCAAC;;;;;;;;;;;0CAGlE,6LAAC;gCAAI,WAAU;0CACZ,4BAA4B;;;;;;;;;;;;kCAIjC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAAc;;;;;;kDAC5B,6LAAC;wCAAK,WAAU;kDACb;;;;;;;;;;;;0CAGL,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCACC,WAAU;oCACV,OAAO;wCAAE,OAAO,GAAG,2BAA2B,cAAc,CAAC,CAAC;oCAAC;;;;;;;;;;;0CAGnE,6LAAC;gCAAI,WAAU;0CACZ,4BAA4B;;;;;;;;;;;;;;;;;;YAKlC,eAAe,MAAM,GAAG,mBACvB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAmB;;;;;;kCACjC,6LAAC;wBAAI,WAAU;kCACZ,eAAe,GAAG,CAAC,CAAC,UAAU,sBAC7B,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAG,WAAU;0DAAe,SAAS,QAAQ;;;;;;0DAC9C,6LAAC;gDAAK,WAAU;0DACb,SAAS,KAAK;;;;;;;;;;;;kDAGnB,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CACC,WAAU;4CACV,OAAO;gDAAE,OAAO,GAAG,2BAA2B,SAAS,KAAK,EAAE,CAAC,CAAC;4CAAC;;;;;;;;;;;kDAGrE,6LAAC;wCAAI,WAAU;kDACZ,4BAA4B,SAAS,KAAK;;;;;;;+BAhBxC;;;;;;;;;;;;;;;;;;;;;;AAyBrB;KA5EwB;AA8ExB,SAAS,2BAA2B,KAAoB;IACtD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;QACL,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF;AAEA,SAAS,4BAA4B,KAAoB;IACvD,OAAQ;QACN,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT,KAAK;YACH,OAAO;QACT;YACE,OAAO;IACX;AACF", "debugId": null}}, {"offset": {"line": 1486, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/app/employer/candidates/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useParams, useRouter } from \"next/navigation\";\nimport { useAuth } from \"@/contexts/AuthContext\";\nimport { doc, getDoc, collection, query, where, getDocs, addDoc, serverTimestamp } from \"firebase/firestore\";\nimport { db } from \"@/lib/firebase\";\nimport Link from \"next/link\";\nimport Image from \"next/image\";\nimport { jobBenefits } from \"@/data/jobBenefits\";\nimport { CandidateInvite, EmployerSubscription } from \"@/types\";\nimport ExperienceDisplay from \"@/components/candidate/ExperienceDisplay\";\nimport EducationDisplay from \"@/components/candidate/EducationDisplay\";\nimport LanguageSkillsDisplay from \"@/components/candidate/LanguageSkillsDisplay\";\nimport { countries } from \"@/data/countries\";\n\nexport default function CandidateDetailPage() {\n  const { id } = useParams();\n  const router = useRouter();\n  const { user, loading: authLoading } = useAuth();\n  const [candidate, setCandidate] = useState<any>(null);\n  const [employerJobs, setEmployerJobs] = useState<any[]>([]);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [showInviteModal, setShowInviteModal] = useState(false);\n  const [selectedJobId, setSelectedJobId] = useState<string>(\"\");\n  const [inviteMessage, setInviteMessage] = useState<string>(\"\");\n  const [sending, setSending] = useState(false);\n  const [remainingInvites, setRemainingInvites] = useState<number>(0);\n  const [subscriptions, setSubscriptions] = useState<EmployerSubscription[]>([]);\n\n  useEffect(() => {\n    // If not authenticated or not an employer, redirect to auth page\n    if (!authLoading && (!user || user.role !== \"employer\")) {\n      router.push(\"/auth\");\n    }\n  }, [user, authLoading, router]);\n\n  useEffect(() => {\n    const fetchData = async () => {\n      if (!user || !id || typeof id !== \"string\") return;\n\n      try {\n        // Fetch candidate data\n        const candidateDoc = await getDoc(doc(db, \"candidates\", id));\n\n        if (!candidateDoc.exists()) {\n          setError(\"Candidate not found\");\n          return;\n        }\n\n        const candidateData = candidateDoc.data();\n        setCandidate({\n          id: candidateDoc.id,\n          ...candidateData,\n          createdAt: candidateData.createdAt?.toDate(),\n          updatedAt: candidateData.updatedAt?.toDate(),\n        });\n\n        // Fetch employer's jobs\n        const jobsCollection = collection(db, \"jobs\");\n        const jobsQuery = query(\n          jobsCollection,\n          where(\"employerId\", \"==\", user.uid)\n        );\n\n        const querySnapshot = await getDocs(jobsQuery);\n        const jobsList: any[] = [];\n\n        querySnapshot.forEach((doc) => {\n          const data = doc.data();\n          jobsList.push({\n            id: doc.id,\n            ...data,\n            createdAt: data.createdAt.toDate(),\n            updatedAt: data.updatedAt.toDate(),\n            premiumUntil: data.premiumUntil ? data.premiumUntil.toDate() : undefined,\n          });\n        });\n\n        setEmployerJobs(jobsList);\n\n        // Fetch employer's active subscriptions\n        const subscriptionsCollection = collection(db, \"subscriptions\");\n        const subscriptionsQuery = query(\n          subscriptionsCollection,\n          where(\"employerId\", \"==\", user.uid),\n          where(\"isActive\", \"==\", true)\n        );\n\n        const subscriptionsSnapshot = await getDocs(subscriptionsQuery);\n        const subscriptionsList: EmployerSubscription[] = [];\n        let totalInvites = 0;\n\n        subscriptionsSnapshot.forEach((doc) => {\n          const data = doc.data();\n          const subscription = {\n            id: doc.id,\n            ...data,\n            startDate: data.startDate.toDate(),\n            endDate: data.endDate ? data.endDate.toDate() : undefined,\n            createdAt: data.createdAt.toDate(),\n            updatedAt: data.updatedAt.toDate(),\n          } as EmployerSubscription;\n\n          subscriptionsList.push(subscription);\n\n          if (subscription.packageType === 'cv' || subscription.packageType === 'combined') {\n            totalInvites += subscription.invitesRemaining;\n          }\n        });\n\n        setSubscriptions(subscriptionsList);\n        setRemainingInvites(totalInvites);\n      } catch (error) {\n        console.error(\"Error fetching data:\", error);\n        setError(\"Failed to load candidate details. Please try again.\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (user && user.role === \"employer\") {\n      fetchData();\n    }\n  }, [user, id, router]);\n\n  // Calculate match percentage between candidate and job\n  const calculateMatchPercentage = (job: any) => {\n    if (!candidate.benefits || !job.benefits) return 0;\n\n    const candidateBenefits = candidate.benefits;\n    const jobBenefits = job.benefits;\n\n    // Count matching benefits\n    const matchingBenefits = candidateBenefits.filter((benefit: string) =>\n      jobBenefits.includes(benefit)\n    ).length;\n\n    // Calculate percentage based on candidate's preferences\n    return Math.round((matchingBenefits / candidateBenefits.length) * 100);\n  };\n\n  const handleInvite = (jobId: string) => {\n    if (remainingInvites <= 0) {\n      // Redirect to packages page if no invites remaining\n      router.push('/employer/packages');\n      return;\n    }\n\n    setSelectedJobId(jobId);\n    setInviteMessage(\"\");\n    setShowInviteModal(true);\n  };\n\n  const sendInvite = async () => {\n    if (!user || !candidate || !selectedJobId) return;\n\n    setSending(true);\n\n    try {\n      // Find the job details\n      const selectedJob = employerJobs.find(job => job.id === selectedJobId);\n\n      if (!selectedJob) {\n        throw new Error(\"Selected job not found\");\n      }\n\n      // Create invite document\n      const inviteData = {\n        employerId: user.uid,\n        candidateId: candidate.id,\n        jobId: selectedJobId,\n        message: inviteMessage.trim() || undefined,\n        status: \"pending\",\n        createdAt: new Date(),\n        updatedAt: new Date(),\n        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now\n        jobTitle: selectedJob.title,\n        employerCompanyName: user.companyName || \"Company\",\n        candidateProfession: candidate.profession,\n        candidateAge: candidate.age\n      };\n\n      await addDoc(collection(db, \"invites\"), inviteData);\n\n      // Find the subscription to deduct an invite from\n      // For simplicity, we'll just use the first subscription with remaining invites\n      const subscriptionToUpdate = subscriptions.find(sub =>\n        (sub.packageType === 'cv' || sub.packageType === 'combined') && sub.invitesRemaining > 0\n      );\n\n      if (subscriptionToUpdate) {\n        // Update the subscription in Firestore\n        await fetch('/api/subscriptions/use-invite', {\n          method: 'POST',\n          headers: {\n            'Content-Type': 'application/json',\n          },\n          body: JSON.stringify({\n            subscriptionId: subscriptionToUpdate.id,\n          }),\n        });\n\n        // Update local state\n        setRemainingInvites(prev => prev - 1);\n        setSubscriptions(prev =>\n          prev.map(sub =>\n            sub.id === subscriptionToUpdate.id\n              ? { ...sub, invitesRemaining: sub.invitesRemaining - 1 }\n              : sub\n          )\n        );\n      }\n\n      // Close modal and show success message\n      setShowInviteModal(false);\n      alert(\"Invitation sent successfully!\");\n    } catch (error) {\n      console.error(\"Error sending invite:\", error);\n      alert(\"Failed to send invitation. Please try again.\");\n    } finally {\n      setSending(false);\n    }\n  };\n\n  if (authLoading || loading) {\n    return (\n      <div className=\"flex justify-center items-center min-h-[60vh]\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900\"></div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"max-w-4xl mx-auto py-12\">\n        <div className=\"bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6\">\n          {error}\n        </div>\n        <Link\n          href=\"/employer/candidates\"\n          className=\"text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white\"\n        >\n          Back to Candidates\n        </Link>\n      </div>\n    );\n  }\n\n  if (!candidate) {\n    return (\n      <div className=\"max-w-4xl mx-auto py-12\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold mb-4\">Candidate Not Found</h2>\n          <p className=\"text-neutral-600 dark:text-neutral-400 mb-6\">\n            The candidate you're looking for doesn't exist or you don't have permission to view it.\n          </p>\n          <Link\n            href=\"/employer/candidates\"\n            className=\"text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white\"\n          >\n            Back to Candidates\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto py-12\">\n      {/* Invite Modal */}\n      {showInviteModal && (\n        <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n          <div className=\"bg-white dark:bg-neutral-800 rounded-lg shadow-lg max-w-md w-full p-6\">\n            <h3 className=\"text-xl font-semibold mb-4\">Invite Candidate to Apply</h3>\n\n            <div className=\"mb-4\">\n              <label className=\"block text-sm font-medium mb-1\">\n                Select Job\n              </label>\n              <select\n                value={selectedJobId}\n                onChange={(e) => setSelectedJobId(e.target.value)}\n                className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md bg-white dark:bg-neutral-800\"\n              >\n                {employerJobs.map((job) => (\n                  <option key={job.id} value={job.id}>\n                    {job.title}\n                  </option>\n                ))}\n              </select>\n            </div>\n\n            <div className=\"mb-6\">\n              <label className=\"block text-sm font-medium mb-1\">\n                Message (Optional)\n              </label>\n              <textarea\n                value={inviteMessage}\n                onChange={(e) => setInviteMessage(e.target.value)}\n                rows={4}\n                className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n                placeholder=\"Add a personal message to the candidate...\"\n              ></textarea>\n            </div>\n\n            <div className=\"flex justify-between\">\n              <button\n                onClick={() => setShowInviteModal(false)}\n                className=\"px-4 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-neutral-700 dark:text-neutral-300\"\n              >\n                Cancel\n              </button>\n              <button\n                onClick={sendInvite}\n                disabled={sending || !selectedJobId}\n                className={`px-4 py-2 rounded-md ${sending ? \"bg-neutral-700 text-white\" : \"bg-neutral-900 hover:bg-neutral-800 text-white\"}`}\n              >\n                {sending ? \"Sending...\" : \"Send Invitation\"}\n              </button>\n            </div>\n\n            <div className=\"mt-4 text-sm text-neutral-500 dark:text-neutral-400\">\n              You have {remainingInvites} invite{remainingInvites !== 1 ? \"s\" : \"\"} remaining.\n            </div>\n          </div>\n        </div>\n      )}\n      <div className=\"flex justify-between items-center mb-8\">\n        <h1 className=\"text-3xl font-bold\">Candidate Profile</h1>\n        <Link\n          href=\"/employer/candidates\"\n          className=\"text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white\"\n        >\n          Back to Candidates\n        </Link>\n      </div>\n\n      <div className=\"bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8 mb-8\">\n        <div className=\"flex items-center mb-6\">\n          {candidate.photoURL ? (\n            <div className=\"w-24 h-24 rounded-full overflow-hidden mr-6\">\n              <Image\n                src={candidate.photoURL}\n                alt=\"Profile\"\n                width={96}\n                height={96}\n                className=\"w-full h-full object-cover\"\n              />\n            </div>\n          ) : (\n            <div className=\"w-24 h-24 bg-neutral-200 dark:bg-neutral-700 rounded-full flex items-center justify-center mr-6\">\n              <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-12 w-12 text-neutral-500\" fill=\"none\" viewBox=\"0 0 24 24\" stroke=\"currentColor\">\n                <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z\" />\n              </svg>\n            </div>\n          )}\n          <div>\n            <h2 className=\"text-2xl font-bold mb-1\">\n              {candidate.profession}, {candidate.age}\n            </h2>\n            <div className=\"flex items-center\">\n              {candidate.country && (\n                <div className=\"flex items-center mr-3\">\n                  {countries.find(c => c.id === candidate.country)?.flagUrl && (\n                    <div className=\"w-4 h-3 mr-1 overflow-hidden rounded-sm\">\n                      <Image\n                        src={countries.find(c => c.id === candidate.country)?.flagUrl || \"\"}\n                        alt={countries.find(c => c.id === candidate.country)?.name || \"\"}\n                        width={16}\n                        height={12}\n                        className=\"w-full h-full object-cover\"\n                      />\n                    </div>\n                  )}\n                  <span className=\"text-neutral-600 dark:text-neutral-400\">\n                    {countries.find(c => c.id === candidate.country)?.name}\n                  </span>\n                </div>\n              )}\n              <div className=\"flex items-center\">\n                <span className=\"text-neutral-600 dark:text-neutral-400\">\n                  German: {candidate.germanLanguageLevel || \"Not specified\"},\n                  English: {candidate.englishLanguageLevel || \"Not specified\"}\n                </span>\n              </div>\n            </div>\n            <p className=\"text-sm text-neutral-500 dark:text-neutral-500 mt-1\">\n              Member since {candidate.createdAt?.toLocaleDateString() || \"N/A\"}\n            </p>\n          </div>\n        </div>\n\n        <div className=\"border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6\">\n          <h3 className=\"text-xl font-semibold mb-4\">Skills</h3>\n          {candidate.skills && candidate.skills.length > 0 ? (\n            <div className=\"flex flex-wrap gap-2\">\n              {candidate.skills.map((skill: string, index: number) => (\n                <span\n                  key={index}\n                  className=\"bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 px-3 py-1 rounded-md\"\n                >\n                  {skill}\n                </span>\n              ))}\n            </div>\n          ) : (\n            <p className=\"text-neutral-600 dark:text-neutral-400\">No skills specified</p>\n          )}\n        </div>\n\n        <div className=\"border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6\">\n          <h3 className=\"text-xl font-semibold mb-4\">Experience</h3>\n          <ExperienceDisplay\n            experiences={candidate.workExperience || []}\n            noWorkExperience={candidate.noWorkExperience}\n          />\n        </div>\n\n        <div className=\"border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6\">\n          <h3 className=\"text-xl font-semibold mb-4\">Education</h3>\n          <EducationDisplay educations={candidate.education || []} />\n        </div>\n\n        <div className=\"border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6\">\n          <h3 className=\"text-xl font-semibold mb-4\">Language Skills</h3>\n          <LanguageSkillsDisplay\n            germanLevel={candidate.germanLanguageLevel || \"A1\"}\n            englishLevel={candidate.englishLanguageLevel || \"A1\"}\n            otherLanguages={candidate.otherLanguages || []}\n          />\n        </div>\n\n        {candidate.benefits && candidate.benefits.length > 0 && (\n          <div className=\"border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6\">\n            <h3 className=\"text-xl font-semibold mb-4\">Job Preferences</h3>\n            <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n              {candidate.benefits.map((benefitId: string) => {\n                const benefit = jobBenefits.find(b => b.id === benefitId);\n                return (\n                  <div key={benefitId} className=\"flex items-center\">\n                    <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-green-500 mr-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                      <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                    </svg>\n                    <span>{benefit ? benefit.label : benefitId}</span>\n                  </div>\n                );\n              })}\n            </div>\n          </div>\n        )}\n\n        {candidate.desiredPositions && candidate.desiredPositions.length > 0 && (\n          <div className=\"border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6\">\n            <h3 className=\"text-xl font-semibold mb-4\">Desired Positions</h3>\n            <div className=\"flex flex-wrap gap-2\">\n              {candidate.desiredPositions.map((position: string, index: number) => (\n                <span\n                  key={index}\n                  className=\"bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 px-3 py-1 rounded-md\"\n                >\n                  {position}\n                </span>\n              ))}\n            </div>\n          </div>\n        )}\n      </div>\n\n      {employerJobs.length > 0 && (\n        <div className=\"bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8\">\n          <h3 className=\"text-xl font-semibold mb-6\">Match with Your Jobs</h3>\n          <div className=\"space-y-4\">\n            {employerJobs.map((job) => {\n              const matchPercentage = calculateMatchPercentage(job);\n              return (\n                <div\n                  key={job.id}\n                  className=\"border border-neutral-200 dark:border-neutral-700 rounded-lg p-4\"\n                >\n                  <div className=\"flex justify-between items-start\">\n                    <div>\n                      <h4 className=\"font-medium\">{job.title}</h4>\n                      <p className=\"text-sm text-neutral-600 dark:text-neutral-400\">\n                        {job.location} • {job.employmentType.replace(\"-\", \" \")}\n                      </p>\n                    </div>\n                    <div className=\"flex items-center\">\n                      <div className=\"w-12 h-12 rounded-full bg-neutral-100 dark:bg-neutral-800 border-4 border-neutral-200 dark:border-neutral-700 flex items-center justify-center mr-2\">\n                        <span className=\"text-sm font-medium\">{matchPercentage}%</span>\n                      </div>\n                      <div className=\"text-xs\">\n                        Match\n                      </div>\n                    </div>\n                  </div>\n                  <div className=\"mt-3 flex items-center\">\n                    <div className=\"w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2\">\n                      <div\n                        className={`h-2 rounded-full ${\n                          matchPercentage >= 80 ? \"bg-green-500\" :\n                          matchPercentage >= 50 ? \"bg-yellow-500\" :\n                          \"bg-red-500\"\n                        }`}\n                        style={{ width: `${matchPercentage}%` }}\n                      ></div>\n                    </div>\n                  </div>\n                  <div className=\"mt-4 flex justify-between items-center\">\n                    <Link\n                      href={`/employer/jobs/${job.id}`}\n                      className=\"text-sm text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white\"\n                    >\n                      View Job\n                    </Link>\n                    <button\n                      className=\"bg-neutral-900 hover:bg-neutral-800 text-white px-4 py-2 rounded-md text-sm\"\n                      onClick={() => handleInvite(job.id)}\n                    >\n                      {remainingInvites > 0 ? \"Invite to Apply\" : \"Buy Invites\"}\n                    </button>\n                  </div>\n                </div>\n              );\n            })}\n          </div>\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AACA;AAEA;AACA;AACA;AACA;;;AAdA;;;;;;;;;;;;;AAgBe,SAAS;;IACtB,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,qIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAChD,MAAM,CAAC,cAAc,gBAAgB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAS,EAAE;IAC1D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvD,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,kBAAkB,oBAAoB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAU;IACjE,MAAM,CAAC,eAAe,iBAAiB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAA0B,EAAE;IAE7E,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,iEAAiE;YACjE,IAAI,CAAC,eAAe,CAAC,CAAC,QAAQ,KAAK,IAAI,KAAK,UAAU,GAAG;gBACvD,OAAO,IAAI,CAAC;YACd;QACF;wCAAG;QAAC;QAAM;QAAa;KAAO;IAE9B,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;yCAAE;YACR,MAAM;2DAAY;oBAChB,IAAI,CAAC,QAAQ,CAAC,MAAM,OAAO,OAAO,UAAU;oBAE5C,IAAI;wBACF,uBAAuB;wBACvB,MAAM,eAAe,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,cAAc;wBAExD,IAAI,CAAC,aAAa,MAAM,IAAI;4BAC1B,SAAS;4BACT;wBACF;wBAEA,MAAM,gBAAgB,aAAa,IAAI;wBACvC,aAAa;4BACX,IAAI,aAAa,EAAE;4BACnB,GAAG,aAAa;4BAChB,WAAW,cAAc,SAAS,EAAE;4BACpC,WAAW,cAAc,SAAS,EAAE;wBACtC;wBAEA,wBAAwB;wBACxB,MAAM,iBAAiB,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE;wBACtC,MAAM,YAAY,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EACpB,gBACA,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,cAAc,MAAM,KAAK,GAAG;wBAGpC,MAAM,gBAAgB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;wBACpC,MAAM,WAAkB,EAAE;wBAE1B,cAAc,OAAO;uEAAC,CAAC;gCACrB,MAAM,OAAO,IAAI,IAAI;gCACrB,SAAS,IAAI,CAAC;oCACZ,IAAI,IAAI,EAAE;oCACV,GAAG,IAAI;oCACP,WAAW,KAAK,SAAS,CAAC,MAAM;oCAChC,WAAW,KAAK,SAAS,CAAC,MAAM;oCAChC,cAAc,KAAK,YAAY,GAAG,KAAK,YAAY,CAAC,MAAM,KAAK;gCACjE;4BACF;;wBAEA,gBAAgB;wBAEhB,wCAAwC;wBACxC,MAAM,0BAA0B,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE;wBAC/C,MAAM,qBAAqB,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAC7B,yBACA,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,cAAc,MAAM,KAAK,GAAG,GAClC,CAAA,GAAA,sKAAA,CAAA,QAAK,AAAD,EAAE,YAAY,MAAM;wBAG1B,MAAM,wBAAwB,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE;wBAC5C,MAAM,oBAA4C,EAAE;wBACpD,IAAI,eAAe;wBAEnB,sBAAsB,OAAO;uEAAC,CAAC;gCAC7B,MAAM,OAAO,IAAI,IAAI;gCACrB,MAAM,eAAe;oCACnB,IAAI,IAAI,EAAE;oCACV,GAAG,IAAI;oCACP,WAAW,KAAK,SAAS,CAAC,MAAM;oCAChC,SAAS,KAAK,OAAO,GAAG,KAAK,OAAO,CAAC,MAAM,KAAK;oCAChD,WAAW,KAAK,SAAS,CAAC,MAAM;oCAChC,WAAW,KAAK,SAAS,CAAC,MAAM;gCAClC;gCAEA,kBAAkB,IAAI,CAAC;gCAEvB,IAAI,aAAa,WAAW,KAAK,QAAQ,aAAa,WAAW,KAAK,YAAY;oCAChF,gBAAgB,aAAa,gBAAgB;gCAC/C;4BACF;;wBAEA,iBAAiB;wBACjB,oBAAoB;oBACtB,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,wBAAwB;wBACtC,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA,IAAI,QAAQ,KAAK,IAAI,KAAK,YAAY;gBACpC;YACF;QACF;wCAAG;QAAC;QAAM;QAAI;KAAO;IAErB,uDAAuD;IACvD,MAAM,2BAA2B,CAAC;QAChC,IAAI,CAAC,UAAU,QAAQ,IAAI,CAAC,IAAI,QAAQ,EAAE,OAAO;QAEjD,MAAM,oBAAoB,UAAU,QAAQ;QAC5C,MAAM,cAAc,IAAI,QAAQ;QAEhC,0BAA0B;QAC1B,MAAM,mBAAmB,kBAAkB,MAAM,CAAC,CAAC,UACjD,YAAY,QAAQ,CAAC,UACrB,MAAM;QAER,wDAAwD;QACxD,OAAO,KAAK,KAAK,CAAC,AAAC,mBAAmB,kBAAkB,MAAM,GAAI;IACpE;IAEA,MAAM,eAAe,CAAC;QACpB,IAAI,oBAAoB,GAAG;YACzB,oDAAoD;YACpD,OAAO,IAAI,CAAC;YACZ;QACF;QAEA,iBAAiB;QACjB,iBAAiB;QACjB,mBAAmB;IACrB;IAEA,MAAM,aAAa;QACjB,IAAI,CAAC,QAAQ,CAAC,aAAa,CAAC,eAAe;QAE3C,WAAW;QAEX,IAAI;YACF,uBAAuB;YACvB,MAAM,cAAc,aAAa,IAAI,CAAC,CAAA,MAAO,IAAI,EAAE,KAAK;YAExD,IAAI,CAAC,aAAa;gBAChB,MAAM,IAAI,MAAM;YAClB;YAEA,yBAAyB;YACzB,MAAM,aAAa;gBACjB,YAAY,KAAK,GAAG;gBACpB,aAAa,UAAU,EAAE;gBACzB,OAAO;gBACP,SAAS,cAAc,IAAI,MAAM;gBACjC,QAAQ;gBACR,WAAW,IAAI;gBACf,WAAW,IAAI;gBACf,WAAW,IAAI,KAAK,KAAK,GAAG,KAAK,KAAK,KAAK,KAAK,KAAK;gBACrD,UAAU,YAAY,KAAK;gBAC3B,qBAAqB,KAAK,WAAW,IAAI;gBACzC,qBAAqB,UAAU,UAAU;gBACzC,cAAc,UAAU,GAAG;YAC7B;YAEA,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,YAAY;YAExC,iDAAiD;YACjD,+EAA+E;YAC/E,MAAM,uBAAuB,cAAc,IAAI,CAAC,CAAA,MAC9C,CAAC,IAAI,WAAW,KAAK,QAAQ,IAAI,WAAW,KAAK,UAAU,KAAK,IAAI,gBAAgB,GAAG;YAGzF,IAAI,sBAAsB;gBACxB,uCAAuC;gBACvC,MAAM,MAAM,iCAAiC;oBAC3C,QAAQ;oBACR,SAAS;wBACP,gBAAgB;oBAClB;oBACA,MAAM,KAAK,SAAS,CAAC;wBACnB,gBAAgB,qBAAqB,EAAE;oBACzC;gBACF;gBAEA,qBAAqB;gBACrB,oBAAoB,CAAA,OAAQ,OAAO;gBACnC,iBAAiB,CAAA,OACf,KAAK,GAAG,CAAC,CAAA,MACP,IAAI,EAAE,KAAK,qBAAqB,EAAE,GAC9B;4BAAE,GAAG,GAAG;4BAAE,kBAAkB,IAAI,gBAAgB,GAAG;wBAAE,IACrD;YAGV;YAEA,uCAAuC;YACvC,mBAAmB;YACnB,MAAM;QACR,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,yBAAyB;YACvC,MAAM;QACR,SAAU;YACR,WAAW;QACb;IACF;IAEA,IAAI,eAAe,SAAS;QAC1B,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,OAAO;QACT,qBACE,6LAAC;YAAI,WAAU;;8BACb,6LAAC;oBAAI,WAAU;8BACZ;;;;;;8BAEH,6LAAC,+JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,IAAI,CAAC,WAAW;QACd,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,6LAAC;wBAAE,WAAU;kCAA8C;;;;;;kCAG3D,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,6LAAC;QAAI,WAAU;;YAEZ,iCACC,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;;sCACb,6LAAC;4BAAG,WAAU;sCAA6B;;;;;;sCAE3C,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAAiC;;;;;;8CAGlD,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oCAChD,WAAU;8CAET,aAAa,GAAG,CAAC,CAAC,oBACjB,6LAAC;4CAAoB,OAAO,IAAI,EAAE;sDAC/B,IAAI,KAAK;2CADC,IAAI,EAAE;;;;;;;;;;;;;;;;sCAOzB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCAAM,WAAU;8CAAiC;;;;;;8CAGlD,6LAAC;oCACC,OAAO;oCACP,UAAU,CAAC,IAAM,iBAAiB,EAAE,MAAM,CAAC,KAAK;oCAChD,MAAM;oCACN,WAAU;oCACV,aAAY;;;;;;;;;;;;sCAIhB,6LAAC;4BAAI,WAAU;;8CACb,6LAAC;oCACC,SAAS,IAAM,mBAAmB;oCAClC,WAAU;8CACX;;;;;;8CAGD,6LAAC;oCACC,SAAS;oCACT,UAAU,WAAW,CAAC;oCACtB,WAAW,CAAC,qBAAqB,EAAE,UAAU,8BAA8B,kDAAkD;8CAE5H,UAAU,eAAe;;;;;;;;;;;;sCAI9B,6LAAC;4BAAI,WAAU;;gCAAsD;gCACzD;gCAAiB;gCAAQ,qBAAqB,IAAI,MAAM;gCAAG;;;;;;;;;;;;;;;;;;0BAK7E,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,6LAAC,+JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;0BAKH,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;4BACZ,UAAU,QAAQ,iBACjB,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;oCACJ,KAAK,UAAU,QAAQ;oCACvB,KAAI;oCACJ,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;qDAId,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,OAAM;oCAA6B,WAAU;oCAA6B,MAAK;oCAAO,SAAQ;oCAAY,QAAO;8CACpH,cAAA,6LAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAI3E,6LAAC;;kDACC,6LAAC;wCAAG,WAAU;;4CACX,UAAU,UAAU;4CAAC;4CAAG,UAAU,GAAG;;;;;;;kDAExC,6LAAC;wCAAI,WAAU;;4CACZ,UAAU,OAAO,kBAChB,6LAAC;gDAAI,WAAU;;oDACZ,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,UAAU,OAAO,GAAG,yBAChD,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC,gIAAA,CAAA,UAAK;4DACJ,KAAK,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,UAAU,OAAO,GAAG,WAAW;4DACjE,KAAK,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,UAAU,OAAO,GAAG,QAAQ;4DAC9D,OAAO;4DACP,QAAQ;4DACR,WAAU;;;;;;;;;;;kEAIhB,6LAAC;wDAAK,WAAU;kEACb,2HAAA,CAAA,YAAS,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK,UAAU,OAAO,GAAG;;;;;;;;;;;;0DAIxD,6LAAC;gDAAI,WAAU;0DACb,cAAA,6LAAC;oDAAK,WAAU;;wDAAyC;wDAC9C,UAAU,mBAAmB,IAAI;wDAAgB;wDAChD,UAAU,oBAAoB,IAAI;;;;;;;;;;;;;;;;;;kDAIlD,6LAAC;wCAAE,WAAU;;4CAAsD;4CACnD,UAAU,SAAS,EAAE,wBAAwB;;;;;;;;;;;;;;;;;;;kCAKjE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;4BAC1C,UAAU,MAAM,IAAI,UAAU,MAAM,CAAC,MAAM,GAAG,kBAC7C,6LAAC;gCAAI,WAAU;0CACZ,UAAU,MAAM,CAAC,GAAG,CAAC,CAAC,OAAe,sBACpC,6LAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;;;;;qDAQX,6LAAC;gCAAE,WAAU;0CAAyC;;;;;;;;;;;;kCAI1D,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,6LAAC,uJAAA,CAAA,UAAiB;gCAChB,aAAa,UAAU,cAAc,IAAI,EAAE;gCAC3C,kBAAkB,UAAU,gBAAgB;;;;;;;;;;;;kCAIhD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,6LAAC,sJAAA,CAAA,UAAgB;gCAAC,YAAY,UAAU,SAAS,IAAI,EAAE;;;;;;;;;;;;kCAGzD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,6LAAC,2JAAA,CAAA,UAAqB;gCACpB,aAAa,UAAU,mBAAmB,IAAI;gCAC9C,cAAc,UAAU,oBAAoB,IAAI;gCAChD,gBAAgB,UAAU,cAAc,IAAI,EAAE;;;;;;;;;;;;oBAIjD,UAAU,QAAQ,IAAI,UAAU,QAAQ,CAAC,MAAM,GAAG,mBACjD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,6LAAC;gCAAI,WAAU;0CACZ,UAAU,QAAQ,CAAC,GAAG,CAAC,CAAC;oCACvB,MAAM,UAAU,6HAAA,CAAA,cAAW,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;oCAC/C,qBACE,6LAAC;wCAAoB,WAAU;;0DAC7B,6LAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAA8B,SAAQ;gDAAY,MAAK;0DACvG,cAAA,6LAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAwI,UAAS;;;;;;;;;;;0DAE9K,6LAAC;0DAAM,UAAU,QAAQ,KAAK,GAAG;;;;;;;uCAJzB;;;;;gCAOd;;;;;;;;;;;;oBAKL,UAAU,gBAAgB,IAAI,UAAU,gBAAgB,CAAC,MAAM,GAAG,mBACjE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,6LAAC;gCAAI,WAAU;0CACZ,UAAU,gBAAgB,CAAC,GAAG,CAAC,CAAC,UAAkB,sBACjD,6LAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;;;;;;;;;;;;;;;;;;YAWhB,aAAa,MAAM,GAAG,mBACrB,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAC3C,6LAAC;wBAAI,WAAU;kCACZ,aAAa,GAAG,CAAC,CAAC;4BACjB,MAAM,kBAAkB,yBAAyB;4BACjD,qBACE,6LAAC;gCAEC,WAAU;;kDAEV,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAG,WAAU;kEAAe,IAAI,KAAK;;;;;;kEACtC,6LAAC;wDAAE,WAAU;;4DACV,IAAI,QAAQ;4DAAC;4DAAI,IAAI,cAAc,CAAC,OAAO,CAAC,KAAK;;;;;;;;;;;;;0DAGtD,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDAAI,WAAU;kEACb,cAAA,6LAAC;4DAAK,WAAU;;gEAAuB;gEAAgB;;;;;;;;;;;;kEAEzD,6LAAC;wDAAI,WAAU;kEAAU;;;;;;;;;;;;;;;;;;kDAK7B,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;4CAAI,WAAU;sDACb,cAAA,6LAAC;gDACC,WAAW,CAAC,iBAAiB,EAC3B,mBAAmB,KAAK,iBACxB,mBAAmB,KAAK,kBACxB,cACA;gDACF,OAAO;oDAAE,OAAO,GAAG,gBAAgB,CAAC,CAAC;gDAAC;;;;;;;;;;;;;;;;kDAI5C,6LAAC;wCAAI,WAAU;;0DACb,6LAAC,+JAAA,CAAA,UAAI;gDACH,MAAM,CAAC,eAAe,EAAE,IAAI,EAAE,EAAE;gDAChC,WAAU;0DACX;;;;;;0DAGD,6LAAC;gDACC,WAAU;gDACV,SAAS,IAAM,aAAa,IAAI,EAAE;0DAEjC,mBAAmB,IAAI,oBAAoB;;;;;;;;;;;;;+BA1C3C,IAAI,EAAE;;;;;wBA+CjB;;;;;;;;;;;;;;;;;;AAMZ;GAngBwB;;QACP,qIAAA,CAAA,YAAS;QACT,qIAAA,CAAA,YAAS;QACe,kIAAA,CAAA,UAAO;;;KAHxB", "debugId": null}}]}