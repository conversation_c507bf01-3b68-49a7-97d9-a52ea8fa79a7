import { useState, useEffect } from "react";
import { migrateStaticDataToFirestore, checkMigrationStatus } from "@/utils/migrateData";

export default function DataMigration() {
  const [migrating, setMigrating] = useState(false);
  const [migrationResult, setMigrationResult] = useState<any>(null);
  const [migrationStatus, setMigrationStatus] = useState<any>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    checkStatus();
  }, []);

  const checkStatus = async () => {
    try {
      setLoading(true);
      const status = await checkMigrationStatus();
      setMigrationStatus(status);
    } catch (error) {
      console.error("Error checking migration status:", error);
    } finally {
      setLoading(false);
    }
  };

  const handleMigration = async () => {
    setMigrating(true);
    setMigrationResult(null);

    try {
      const result = await migrateStaticDataToFirestore();
      setMigrationResult(result);
      
      if (result.success) {
        // Refresh status after successful migration
        await checkStatus();
      }
    } catch (error) {
      setMigrationResult({
        success: false,
        message: "Migration failed with error",
        details: error,
      });
    } finally {
      setMigrating(false);
    }
  };

  if (loading) {
    return (
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6">
        <div className="animate-pulse">
          <div className="h-4 bg-neutral-200 dark:bg-neutral-700 rounded w-1/4 mb-4"></div>
          <div className="h-4 bg-neutral-200 dark:bg-neutral-700 rounded w-3/4 mb-2"></div>
          <div className="h-4 bg-neutral-200 dark:bg-neutral-700 rounded w-1/2"></div>
        </div>
      </div>
    );
  }

  const totalItems = migrationStatus ? Object.values(migrationStatus).reduce((a: any, b: any) => a + b, 0) : 0;
  const hasData = totalItems > 0;

  return (
    <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6">
      <h3 className="text-lg font-medium mb-4">Data Migration</h3>
      
      <div className="mb-6">
        <h4 className="text-md font-medium mb-2">Current Status</h4>
        <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
          <div className="bg-neutral-50 dark:bg-neutral-900 p-3 rounded">
            <div className="text-sm text-neutral-600 dark:text-neutral-400">Positions</div>
            <div className="text-lg font-semibold">{migrationStatus?.positions || 0}</div>
          </div>
          <div className="bg-neutral-50 dark:bg-neutral-900 p-3 rounded">
            <div className="text-sm text-neutral-600 dark:text-neutral-400">Study Fields</div>
            <div className="text-lg font-semibold">{migrationStatus?.studyFields || 0}</div>
          </div>
          <div className="bg-neutral-50 dark:bg-neutral-900 p-3 rounded">
            <div className="text-sm text-neutral-600 dark:text-neutral-400">Driver Licenses</div>
            <div className="text-lg font-semibold">{migrationStatus?.driverLicenses || 0}</div>
          </div>
          <div className="bg-neutral-50 dark:bg-neutral-900 p-3 rounded">
            <div className="text-sm text-neutral-600 dark:text-neutral-400">Benefits</div>
            <div className="text-lg font-semibold">{migrationStatus?.benefits || 0}</div>
          </div>
          <div className="bg-neutral-50 dark:bg-neutral-900 p-3 rounded">
            <div className="text-sm text-neutral-600 dark:text-neutral-400">Countries</div>
            <div className="text-lg font-semibold">{migrationStatus?.countries || 0}</div>
          </div>
        </div>
      </div>

      {!hasData && (
        <div className="mb-6">
          <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-4">
            <div className="flex">
              <div className="flex-shrink-0">
                <svg className="h-5 w-5 text-yellow-400" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
                </svg>
              </div>
              <div className="ml-3">
                <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                  No data found in Firestore
                </h3>
                <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                  <p>
                    Your application is currently using static data files. To enable full admin panel functionality,
                    you need to migrate this data to Firestore.
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      )}

      <div className="flex items-center justify-between">
        <div>
          <button
            onClick={handleMigration}
            disabled={migrating}
            className={`px-4 py-2 rounded-md ${
              migrating
                ? "bg-neutral-300 dark:bg-neutral-700 cursor-not-allowed"
                : "bg-blue-600 hover:bg-blue-700 text-white"
            }`}
          >
            {migrating ? "Migrating..." : hasData ? "Re-migrate Data" : "Migrate Data to Firestore"}
          </button>
          
          <button
            onClick={checkStatus}
            disabled={loading}
            className="ml-3 px-4 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md hover:bg-neutral-50 dark:hover:bg-neutral-700"
          >
            Refresh Status
          </button>
        </div>
      </div>

      {migrationResult && (
        <div className={`mt-4 p-4 rounded-md ${
          migrationResult.success
            ? "bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300"
            : "bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300"
        }`}>
          <p className="font-medium">{migrationResult.message}</p>
          {migrationResult.details && (
            <pre className="mt-2 text-xs overflow-auto">
              {JSON.stringify(migrationResult.details, null, 2)}
            </pre>
          )}
        </div>
      )}
    </div>
  );
}
