"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { doc, getDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { useAuth } from "@/contexts/AuthContext";
import Link from "next/link";
import Image from "next/image";
import { JobListing } from "@/types";
import { jobBenefits } from "@/data/jobBenefits";
import { positions } from "@/data/positions";
import { degrees } from "@/data/degrees";
import { countries } from "@/data/countries";

export default function JobDetailPage() {
  const { id } = useParams();
  const router = useRouter();
  const { user } = useAuth();
  const [job, setJob] = useState<JobListing | null>(null);
  const [loading, setLoading] = useState(true);
  const [applying, setApplying] = useState(false);

  useEffect(() => {
    const fetchJob = async () => {
      try {
        if (!id || typeof id !== "string") {
          router.push("/jobs");
          return;
        }

        const jobDoc = await getDoc(doc(db, "jobs", id));

        if (!jobDoc.exists()) {
          router.push("/jobs");
          return;
        }

        const data = jobDoc.data() as Omit<JobListing, "id">;
        setJob({
          id: jobDoc.id,
          ...data,
          createdAt: data.createdAt.toDate(),
          updatedAt: data.updatedAt.toDate(),
          premiumUntil: data.premiumUntil ? data.premiumUntil.toDate() : undefined,
        });
      } catch (error) {
        console.error("Error fetching job:", error);
        router.push("/jobs");
      } finally {
        setLoading(false);
      }
    };

    fetchJob();
  }, [id, router]);

  const handleApply = async () => {
    if (!user || user.role !== "candidate") {
      router.push("/auth");
      return;
    }

    setApplying(true);
    // Redirect to the application page
    router.push(`/jobs/${id}/apply`);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="max-w-4xl mx-auto py-12 text-center">
        <h1 className="text-3xl font-bold mb-4">Job Not Found</h1>
        <p className="text-neutral-600 dark:text-neutral-400 mb-8">
          The job you're looking for doesn't exist or has been removed.
        </p>
        <Link
          href="/jobs"
          className="bg-neutral-900 hover:bg-neutral-800 text-white px-6 py-3 rounded-md"
        >
          Browse All Jobs
        </Link>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto py-12">
      <Link
        href="/jobs"
        className="inline-flex items-center text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white mb-6"
      >
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className="h-5 w-5 mr-1"
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M9.707 14.707a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 1.414L7.414 9H15a1 1 0 110 2H7.414l2.293 2.293a1 1 0 010 1.414z"
            clipRule="evenodd"
          />
        </svg>
        Back to Jobs
      </Link>

      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h1 className="text-3xl font-bold mb-2">{job.title}</h1>
            <div className="flex flex-wrap gap-2">
              <span className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 px-3 py-1 rounded-full text-sm">
                {job.location}
              </span>
              <span className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 px-3 py-1 rounded-full text-sm">
                {job.employmentType.replace("-", " ")}
              </span>
              {job.salary && (
                <span className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 px-3 py-1 rounded-full text-sm">
                  {job.salary.min} - {job.salary.max} {job.salary.currency}
                </span>
              )}
            </div>
          </div>
          {job.isPremium && (
            <div className="bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-100 px-3 py-1 rounded-full text-sm font-medium">
              Premium
            </div>
          )}
        </div>

        <div className="border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Job Description</h2>
          <div className="prose dark:prose-invert max-w-none">
            <p className="whitespace-pre-line">{job.description}</p>
          </div>
        </div>

        <div className="border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Requirements</h2>
          <ul className="list-disc pl-5 space-y-2">
            {job.requirements.map((requirement, index) => (
              <li key={index}>{requirement}</li>
            ))}
          </ul>
        </div>

        {job.structuredRequirements && (
          <div className="border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Specific Requirements</h2>

            {/* Job Location */}
            {job.structuredRequirements.requiredCountries && job.structuredRequirements.requiredCountries.length > 0 && (
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-2">Job Location</h3>
                <div className="flex items-center">
                  {job.structuredRequirements.requiredCountries.map(countryId => {
                    const country = countries.find(c => c.id === countryId);
                    return (
                      <div key={countryId} className="flex items-center bg-blue-100 dark:bg-blue-900/30 px-4 py-2 rounded-md">
                        {country?.flagUrl && (
                          <div className="w-6 h-4 mr-3 overflow-hidden rounded-sm">
                            <Image
                              src={country.flagUrl}
                              alt={country.name}
                              width={24}
                              height={16}
                              className="w-full h-full object-cover"
                            />
                          </div>
                        )}
                        <span className="font-medium">{country ? country.name : countryId}</span>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Language Requirements */}
            {(job.structuredRequirements.germanLanguageLevel || job.structuredRequirements.englishLanguageLevel) && (
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-2">Language Skills</h3>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  {job.structuredRequirements.germanLanguageLevel && (
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mr-3">
                        <span className="font-semibold text-blue-600 dark:text-blue-400">DE</span>
                      </div>
                      <div>
                        <p className="font-medium">German</p>
                        <p className="text-sm text-neutral-600 dark:text-neutral-400">
                          Minimum level: {job.structuredRequirements.germanLanguageLevel}
                        </p>
                      </div>
                    </div>
                  )}

                  {job.structuredRequirements.englishLanguageLevel && (
                    <div className="flex items-center">
                      <div className="w-8 h-8 rounded-full bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center mr-3">
                        <span className="font-semibold text-blue-600 dark:text-blue-400">EN</span>
                      </div>
                      <div>
                        <p className="font-medium">English</p>
                        <p className="text-sm text-neutral-600 dark:text-neutral-400">
                          Minimum level: {job.structuredRequirements.englishLanguageLevel}
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            )}

            {/* Work Experience Requirements */}
            {job.structuredRequirements.requiredPositions && job.structuredRequirements.requiredPositions.length > 0 && (
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-2">Work Experience</h3>
                <div className="space-y-3">
                  {job.structuredRequirements.requiredPositions.map((posReq, index) => {
                    const position = positions.find(p => p.id === posReq.positionId);
                    return (
                      <div key={index} className="flex items-center">
                        <div className="w-8 h-8 rounded-full bg-amber-100 dark:bg-amber-900/30 flex items-center justify-center mr-3">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-amber-600 dark:text-amber-400" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
                            <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z" />
                          </svg>
                        </div>
                        <div>
                          <p className="font-medium">{position ? position.translations.en : posReq.positionId}</p>
                          <p className="text-sm text-neutral-600 dark:text-neutral-400">
                            Minimum {posReq.minExperienceMonths} months of experience
                            {position?.category && ` (${position.category})`}
                          </p>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Education Requirements */}
            {(job.structuredRequirements.minEducationLevel ||
              (job.structuredRequirements.requiredDegrees && job.structuredRequirements.requiredDegrees.length > 0)) && (
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-2">Education</h3>

                {job.structuredRequirements.minEducationLevel && (
                  <div className="flex items-center mb-3">
                    <div className="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center mr-3">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 text-green-600 dark:text-green-400" viewBox="0 0 20 20" fill="currentColor">
                        <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z" />
                      </svg>
                    </div>
                    <div>
                      <p className="font-medium">Minimum Education Level</p>
                      <p className="text-sm text-neutral-600 dark:text-neutral-400">
                        {job.structuredRequirements.minEducationLevel.charAt(0).toUpperCase() +
                          job.structuredRequirements.minEducationLevel.slice(1)}
                      </p>
                    </div>
                  </div>
                )}

                {job.structuredRequirements.requiredDegrees && job.structuredRequirements.requiredDegrees.length > 0 && (
                  <div>
                    <p className="font-medium mb-2">Specific Degrees</p>
                    <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
                      {job.structuredRequirements.requiredDegrees.map(degreeId => {
                        const degree = degrees.find(d => d.id === degreeId);
                        return (
                          <div key={degreeId} className="flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                            <span>{degree ? degree.translations.en : degreeId}</span>
                          </div>
                        );
                      })}
                    </div>
                  </div>
                )}
              </div>
            )}
          </div>
        )}

        {job.benefits && job.benefits.length > 0 && (
          <div className="border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6">
            <h2 className="text-xl font-semibold mb-4">Benefits</h2>

            {/* Accommodation Options */}
            {job.benefits.some(id => id.startsWith('accommodation_')) && (
              <div className="mb-6">
                <h3 className="text-lg font-medium mb-3">Accommodation</h3>
                <div className="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-4">
                  {job.benefits.filter(id => id.startsWith('accommodation_')).map((benefitId) => {
                    const benefit = jobBenefits.find(b => b.id === benefitId);
                    return (
                      <div key={benefitId} className="flex items-start mb-2 last:mb-0">
                        <div className="mt-0.5">
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-blue-600 dark:text-blue-400 mr-2" viewBox="0 0 20 20" fill="currentColor">
                            <path d="M10.707 2.293a1 1 0 00-1.414 0l-7 7a1 1 0 001.414 1.414L4 10.414V17a1 1 0 001 1h2a1 1 0 001-1v-2a1 1 0 011-1h2a1 1 0 011 1v2a1 1 0 001 1h2a1 1 0 001-1v-6.586l.293.293a1 1 0 001.414-1.414l-7-7z" />
                          </svg>
                        </div>
                        <div>
                          <p className="font-medium">{benefit ? benefit.label : benefitId}</p>
                          {benefit?.description && (
                            <p className="text-sm text-neutral-600 dark:text-neutral-400">{benefit.description}</p>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Other Benefits */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {job.benefits.filter(id => !id.startsWith('accommodation_')).map((benefitId) => {
                const benefit = jobBenefits.find(b => b.id === benefitId);
                return (
                  <div key={benefitId} className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>{benefit ? benefit.label : benefitId}</span>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        <div className="border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6">
          <h2 className="text-xl font-semibold mb-4">Tags</h2>
          <div className="flex flex-wrap gap-2">
            {job.tags.map((tag, index) => (
              <span
                key={index}
                className="bg-neutral-100 dark:bg-neutral-700 text-neutral-600 dark:text-neutral-400 px-3 py-1 rounded-full text-sm"
              >
                {tag}
              </span>
            ))}
          </div>
        </div>

        <div className="border-t border-neutral-200 dark:border-neutral-700 pt-6">
          <div className="flex justify-between items-center">
            <span className="text-sm text-neutral-500">
              Posted on {new Date(job.createdAt).toLocaleDateString()}
            </span>
            {user?.role === "candidate" ? (
              <button
                onClick={handleApply}
                disabled={applying}
                className={`bg-neutral-900 hover:bg-neutral-800 text-white px-6 py-3 rounded-md ${
                  applying ? "opacity-70 cursor-not-allowed" : ""
                }`}
              >
                {applying ? "Submitting..." : "Apply Now"}
              </button>
            ) : user?.role === "employer" ? (
              <div className="text-neutral-600 dark:text-neutral-400">
                You're logged in as an employer
              </div>
            ) : (
              <Link
                href="/auth"
                className="bg-neutral-900 hover:bg-neutral-800 text-white px-6 py-3 rounded-md"
              >
                Sign in to Apply
              </Link>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
