{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/data/studyFields.ts"], "sourcesContent": ["import { StudyField } from \"@/types\";\n\n// Standardized study fields in multiple languages\nexport const studyFields: StudyField[] = [\n  {\n    id: \"electrical-engineering\",\n    translations: {\n      de: \"Elektrotechnik\",\n      en: \"Electrical Engineering\",\n      bs: \"Elektrotehnika\",\n      hr: \"Elektrotehnika\",\n      sr: \"Elektrotehnika\",\n      me: \"Elektrotehn<PERSON>\",\n      mk: \"Електротехника\",\n      al: \"Inxhinieri Elektrike\"\n    },\n    category: \"engineering\"\n  },\n  {\n    id: \"mechanical-engineering\",\n    translations: {\n      de: \"Maschinenbau\",\n      en: \"Mechanical Engineering\",\n      bs: \"Mašinstvo\",\n      hr: \"St<PERSON>jarst<PERSON>\",\n      sr: \"<PERSON><PERSON>inst<PERSON>\",\n      me: \"<PERSON><PERSON>instvo\",\n      mk: \"Машинство\",\n      al: \"Inxhinieri Mekanike\"\n    },\n    category: \"engineering\"\n  },\n  {\n    id: \"automotive-mechanic\",\n    translations: {\n      de: \"Kfz-Mechaniker\",\n      en: \"Automotive Mechanic\",\n      bs: \"Automehaničar\",\n      hr: \"Autome<PERSON><PERSON>ar\",\n      sr: \"Automehaničar\",\n      me: \"Automehaničar\",\n      mk: \"Автомеханичар\",\n      al: \"Mekanik Automjetesh\"\n    },\n    category: \"vocational\"\n  },\n  {\n    id: \"automotive-painter\",\n    translations: {\n      de: \"Kfz-Lackierer\",\n      en: \"Automotive Painter\",\n      bs: \"Autolakirer\",\n      hr: \"Autolakirer\",\n      sr: \"Autolakirer\",\n      me: \"Autolakirer\",\n      mk: \"Автолакер\",\n      al: \"Bojaxhi Automjetesh\"\n    },\n    category: \"vocational\"\n  },\n  {\n    id: \"carpentry\",\n    translations: {\n      de: \"Tischlerei\",\n      en: \"Carpentry\",\n      bs: \"Stolarstvo\",\n      hr: \"Stolarstvo\",\n      sr: \"Stolarstvo\",\n      me: \"Stolarstvo\",\n      mk: \"Столарство\",\n      al: \"Marangozi\"\n    },\n    category: \"vocational\"\n  },\n  {\n    id: \"plumbing\",\n    translations: {\n      de: \"Sanitärinstallation\",\n      en: \"Plumbing\",\n      bs: \"Vodoinstalaterstvo\",\n      hr: \"Vodoinstalaterstvo\",\n      sr: \"Vodoinstalaterstvo\",\n      me: \"Vodoinstalaterstvo\",\n      mk: \"Водоинсталатерство\",\n      al: \"Hidraulikë\"\n    },\n    category: \"vocational\"\n  },\n  {\n    id: \"welding\",\n    translations: {\n      de: \"Schweißen\",\n      en: \"Welding\",\n      bs: \"Zavarivanje\",\n      hr: \"Zavarivanje\",\n      sr: \"Zavarivanje\",\n      me: \"Zavarivanje\",\n      mk: \"Заварување\",\n      al: \"Saldim\"\n    },\n    category: \"vocational\"\n  },\n  {\n    id: \"culinary-arts\",\n    translations: {\n      de: \"Kochkunst\",\n      en: \"Culinary Arts\",\n      bs: \"Kuharstvo\",\n      hr: \"Kuharstvo\",\n      sr: \"Kuvarstvo\",\n      me: \"Kuvarstvo\",\n      mk: \"Готварство\",\n      al: \"Arti Kulinar\"\n    },\n    category: \"hospitality\"\n  },\n  {\n    id: \"nursing\",\n    translations: {\n      de: \"Krankenpflege\",\n      en: \"Nursing\",\n      bs: \"Sestrinstvo\",\n      hr: \"Sestrinstvo\",\n      sr: \"Sestrinstvo\",\n      me: \"Sestrinstvo\",\n      mk: \"Медицинска нега\",\n      al: \"Infermieri\"\n    },\n    category: \"healthcare\"\n  },\n  {\n    id: \"general-high-school\",\n    translations: {\n      de: \"Allgemeine Hochschulreife\",\n      en: \"General High School\",\n      bs: \"Gimnazija\",\n      hr: \"Gimnazija\",\n      sr: \"Gimnazija\",\n      me: \"Gimnazija\",\n      mk: \"Гимназија\",\n      al: \"Gjimnaz\"\n    },\n    category: \"general-education\"\n  }\n];\n"], "names": [], "mappings": ";;;AAGO,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;Q<PERSON><PERSON>,cAAc;Y<PERSON><PERSON>,IAAI;Y<PERSON><PERSON>,IAAI;<PERSON><PERSON><PERSON>,IAAI;Y<PERSON><PERSON>,IAAI;YACJ,IAAI;<PERSON><PERSON><PERSON>,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;CACD", "debugId": null}}]}