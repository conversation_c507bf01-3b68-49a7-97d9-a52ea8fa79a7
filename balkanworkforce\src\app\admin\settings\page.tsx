"use client";

import { useState, useEffect } from "react";
import { doc, getDoc, setDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import DataMigration from "@/components/admin/DataMigration";

interface PlatformSettings {
  languages: {
    enabled: string[];
    default: string;
  };
  registration: {
    employerApprovalRequired: boolean;
    candidateEmailVerificationRequired: boolean;
  };
  notifications: {
    adminEmail: string;
    newEmployerNotification: boolean;
    newCandidateNotification: boolean;
    newJobNotification: boolean;
  };
  premium: {
    enabled: boolean;
    prices: {
      jobListing: {
        sevenDays: number;
        fourteenDays: number;
        thirtyDays: number;
      };
      cvAccess: {
        tenInvites: number;
        twentyFiveInvites: number;
        fiftyInvites: number;
      };
    };
  };
}

const defaultSettings: PlatformSettings = {
  languages: {
    enabled: ["en", "de", "bs"],
    default: "en",
  },
  registration: {
    employerApprovalRequired: false,
    candidateEmailVerificationRequired: true,
  },
  notifications: {
    adminEmail: "",
    newEmployerNotification: true,
    newCandidateNotification: false,
    newJobNotification: false,
  },
  premium: {
    enabled: true,
    prices: {
      jobListing: {
        sevenDays: 5,
        fourteenDays: 8,
        thirtyDays: 15,
      },
      cvAccess: {
        tenInvites: 5,
        twentyFiveInvites: 10,
        fiftyInvites: 20,
      },
    },
  },
};

export default function AdminSettingsPage() {
  const [settings, setSettings] = useState<PlatformSettings>(defaultSettings);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    const fetchSettings = async () => {
      try {
        const settingsDoc = await getDoc(doc(db, "settings", "platform"));
        
        if (settingsDoc.exists()) {
          setSettings(settingsDoc.data() as PlatformSettings);
        } else {
          // If no settings document exists, create one with defaults
          await setDoc(doc(db, "settings", "platform"), defaultSettings);
        }
      } catch (error) {
        console.error("Error fetching settings:", error);
        setError("Failed to load settings. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchSettings();
  }, []);

  const handleSaveSettings = async () => {
    setSaving(true);
    setError(null);
    setSuccess(null);

    try {
      await setDoc(doc(db, "settings", "platform"), settings);
      setSuccess("Settings saved successfully");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Error saving settings:", error);
      setError("Failed to save settings. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  const handleLanguageToggle = (language: string) => {
    setSettings(prev => {
      const enabled = [...prev.languages.enabled];
      const index = enabled.indexOf(language);
      
      if (index === -1) {
        enabled.push(language);
      } else {
        // Don't allow removing the default language
        if (language === prev.languages.default) {
          return prev;
        }
        enabled.splice(index, 1);
      }
      
      return {
        ...prev,
        languages: {
          ...prev.languages,
          enabled,
        },
      };
    });
  };

  const handleDefaultLanguageChange = (language: string) => {
    setSettings(prev => {
      // Make sure the default language is also enabled
      let enabled = [...prev.languages.enabled];
      if (!enabled.includes(language)) {
        enabled.push(language);
      }
      
      return {
        ...prev,
        languages: {
          enabled,
          default: language,
        },
      };
    });
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Platform Settings</h1>
        <button
          onClick={handleSaveSettings}
          disabled={saving}
          className={`px-4 py-2 rounded-md ${
            saving
              ? "bg-neutral-400 cursor-not-allowed"
              : "bg-blue-600 hover:bg-blue-700"
          } text-white`}
        >
          {saving ? "Saving..." : "Save Settings"}
        </button>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 p-4 rounded-md mb-6">
          {success}
        </div>
      )}

      {/* Data Migration */}
      <div className="mb-6">
        <DataMigration />
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Language Settings */}
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-medium mb-4">Language Settings</h2>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
              Enabled Languages
            </label>
            <div className="space-y-2">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="lang-en"
                  checked={settings.languages.enabled.includes("en")}
                  onChange={() => handleLanguageToggle("en")}
                  className="mr-2"
                  disabled={settings.languages.default === "en"}
                />
                <label htmlFor="lang-en" className="text-sm">English</label>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="lang-de"
                  checked={settings.languages.enabled.includes("de")}
                  onChange={() => handleLanguageToggle("de")}
                  className="mr-2"
                  disabled={settings.languages.default === "de"}
                />
                <label htmlFor="lang-de" className="text-sm">German</label>
              </div>
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="lang-bs"
                  checked={settings.languages.enabled.includes("bs")}
                  onChange={() => handleLanguageToggle("bs")}
                  className="mr-2"
                  disabled={settings.languages.default === "bs"}
                />
                <label htmlFor="lang-bs" className="text-sm">Bosnian</label>
              </div>
            </div>
          </div>
          
          <div>
            <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2">
              Default Language
            </label>
            <select
              value={settings.languages.default}
              onChange={(e) => handleDefaultLanguageChange(e.target.value)}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
            >
              {settings.languages.enabled.map(lang => (
                <option key={lang} value={lang}>
                  {lang === "en" ? "English" : lang === "de" ? "German" : "Bosnian"}
                </option>
              ))}
            </select>
          </div>
        </div>

        {/* Registration Settings */}
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-medium mb-4">Registration Settings</h2>
          
          <div className="space-y-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="employer-approval"
                checked={settings.registration.employerApprovalRequired}
                onChange={(e) => setSettings({
                  ...settings,
                  registration: {
                    ...settings.registration,
                    employerApprovalRequired: e.target.checked,
                  },
                })}
                className="mr-2"
              />
              <label htmlFor="employer-approval" className="text-sm">
                Require manual approval for employer accounts
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="candidate-verification"
                checked={settings.registration.candidateEmailVerificationRequired}
                onChange={(e) => setSettings({
                  ...settings,
                  registration: {
                    ...settings.registration,
                    candidateEmailVerificationRequired: e.target.checked,
                  },
                })}
                className="mr-2"
              />
              <label htmlFor="candidate-verification" className="text-sm">
                Require email verification for candidate accounts
              </label>
            </div>
          </div>
        </div>

        {/* Notification Settings */}
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-medium mb-4">Notification Settings</h2>
          
          <div className="mb-4">
            <label htmlFor="admin-email" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Admin Email for Notifications
            </label>
            <input
              type="email"
              id="admin-email"
              value={settings.notifications.adminEmail}
              onChange={(e) => setSettings({
                ...settings,
                notifications: {
                  ...settings.notifications,
                  adminEmail: e.target.value,
                },
              })}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              placeholder="<EMAIL>"
            />
          </div>
          
          <div className="space-y-3">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="new-employer"
                checked={settings.notifications.newEmployerNotification}
                onChange={(e) => setSettings({
                  ...settings,
                  notifications: {
                    ...settings.notifications,
                    newEmployerNotification: e.target.checked,
                  },
                })}
                className="mr-2"
              />
              <label htmlFor="new-employer" className="text-sm">
                Notify admin when a new employer registers
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="new-candidate"
                checked={settings.notifications.newCandidateNotification}
                onChange={(e) => setSettings({
                  ...settings,
                  notifications: {
                    ...settings.notifications,
                    newCandidateNotification: e.target.checked,
                  },
                })}
                className="mr-2"
              />
              <label htmlFor="new-candidate" className="text-sm">
                Notify admin when a new candidate registers
              </label>
            </div>
            
            <div className="flex items-center">
              <input
                type="checkbox"
                id="new-job"
                checked={settings.notifications.newJobNotification}
                onChange={(e) => setSettings({
                  ...settings,
                  notifications: {
                    ...settings.notifications,
                    newJobNotification: e.target.checked,
                  },
                })}
                className="mr-2"
              />
              <label htmlFor="new-job" className="text-sm">
                Notify admin when a new job is posted
              </label>
            </div>
          </div>
        </div>

        {/* Premium Features Settings */}
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-medium mb-4">Premium Features</h2>
          
          <div className="mb-4">
            <div className="flex items-center mb-4">
              <input
                type="checkbox"
                id="premium-enabled"
                checked={settings.premium.enabled}
                onChange={(e) => setSettings({
                  ...settings,
                  premium: {
                    ...settings.premium,
                    enabled: e.target.checked,
                  },
                })}
                className="mr-2"
              />
              <label htmlFor="premium-enabled" className="text-sm font-medium">
                Enable premium features
              </label>
            </div>
          </div>
          
          {settings.premium.enabled && (
            <>
              <h3 className="text-md font-medium mb-2">Job Listing Prices (€)</h3>
              <div className="grid grid-cols-3 gap-4 mb-6">
                <div>
                  <label htmlFor="price-7days" className="block text-sm text-neutral-700 dark:text-neutral-300 mb-1">
                    7 Days
                  </label>
                  <input
                    type="number"
                    id="price-7days"
                    value={settings.premium.prices.jobListing.sevenDays}
                    onChange={(e) => setSettings({
                      ...settings,
                      premium: {
                        ...settings.premium,
                        prices: {
                          ...settings.premium.prices,
                          jobListing: {
                            ...settings.premium.prices.jobListing,
                            sevenDays: Number(e.target.value),
                          },
                        },
                      },
                    })}
                    min="0"
                    step="1"
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                  />
                </div>
                <div>
                  <label htmlFor="price-14days" className="block text-sm text-neutral-700 dark:text-neutral-300 mb-1">
                    14 Days
                  </label>
                  <input
                    type="number"
                    id="price-14days"
                    value={settings.premium.prices.jobListing.fourteenDays}
                    onChange={(e) => setSettings({
                      ...settings,
                      premium: {
                        ...settings.premium,
                        prices: {
                          ...settings.premium.prices,
                          jobListing: {
                            ...settings.premium.prices.jobListing,
                            fourteenDays: Number(e.target.value),
                          },
                        },
                      },
                    })}
                    min="0"
                    step="1"
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                  />
                </div>
                <div>
                  <label htmlFor="price-30days" className="block text-sm text-neutral-700 dark:text-neutral-300 mb-1">
                    30 Days
                  </label>
                  <input
                    type="number"
                    id="price-30days"
                    value={settings.premium.prices.jobListing.thirtyDays}
                    onChange={(e) => setSettings({
                      ...settings,
                      premium: {
                        ...settings.premium,
                        prices: {
                          ...settings.premium.prices,
                          jobListing: {
                            ...settings.premium.prices.jobListing,
                            thirtyDays: Number(e.target.value),
                          },
                        },
                      },
                    })}
                    min="0"
                    step="1"
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                  />
                </div>
              </div>
              
              <h3 className="text-md font-medium mb-2">CV Access Prices (€)</h3>
              <div className="grid grid-cols-3 gap-4">
                <div>
                  <label htmlFor="price-10invites" className="block text-sm text-neutral-700 dark:text-neutral-300 mb-1">
                    10 Invites
                  </label>
                  <input
                    type="number"
                    id="price-10invites"
                    value={settings.premium.prices.cvAccess.tenInvites}
                    onChange={(e) => setSettings({
                      ...settings,
                      premium: {
                        ...settings.premium,
                        prices: {
                          ...settings.premium.prices,
                          cvAccess: {
                            ...settings.premium.prices.cvAccess,
                            tenInvites: Number(e.target.value),
                          },
                        },
                      },
                    })}
                    min="0"
                    step="1"
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                  />
                </div>
                <div>
                  <label htmlFor="price-25invites" className="block text-sm text-neutral-700 dark:text-neutral-300 mb-1">
                    25 Invites
                  </label>
                  <input
                    type="number"
                    id="price-25invites"
                    value={settings.premium.prices.cvAccess.twentyFiveInvites}
                    onChange={(e) => setSettings({
                      ...settings,
                      premium: {
                        ...settings.premium,
                        prices: {
                          ...settings.premium.prices,
                          cvAccess: {
                            ...settings.premium.prices.cvAccess,
                            twentyFiveInvites: Number(e.target.value),
                          },
                        },
                      },
                    })}
                    min="0"
                    step="1"
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                  />
                </div>
                <div>
                  <label htmlFor="price-50invites" className="block text-sm text-neutral-700 dark:text-neutral-300 mb-1">
                    50 Invites
                  </label>
                  <input
                    type="number"
                    id="price-50invites"
                    value={settings.premium.prices.cvAccess.fiftyInvites}
                    onChange={(e) => setSettings({
                      ...settings,
                      premium: {
                        ...settings.premium,
                        prices: {
                          ...settings.premium.prices,
                          cvAccess: {
                            ...settings.premium.prices.cvAccess,
                            fiftyInvites: Number(e.target.value),
                          },
                        },
                      },
                    })}
                    min="0"
                    step="1"
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                  />
                </div>
              </div>
            </>
          )}
        </div>
      </div>
    </div>
  );
}
