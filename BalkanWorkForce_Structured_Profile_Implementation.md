# BalkanWorkForce - Plan implementacije strukturiranih profila kandidata

## 1. Pregled

Ovaj dokument opisuje plan implementacije strukturiranih profila kandidata za BalkanWorkForce platformu, s fokusom na detaljno praćenje radnog iskustva i edukacije. Cilj je omogućiti preciznije podudaranje kandidata s poslovima i olakšati poslodavcima pronalaženje kvalificiranih kandidata.

## 2. Ključne funkcionalnosti

### 2.1. Strukturirano radno iskustvo

- Detaljno praćenje radnog iskustva s preciznim vremenskim periodima (mjesec/godina)
- Standardizacija pozicija za lakše filtriranje i pretraživanje
- Automatsko računanje ukupnog radnog iskustva po poziciji
- Podrška za različite formate radnog iskustva u različitim zemljama

### 2.2. Strukturirana edukacija

- Detaljno praćenje edukacije s preciznim vremenskim periodima (mjesec/godina)
- Standardizacija obrazovnih stupnjeva za lakše filtriranje i pretraživanje
- Podrška za različite obrazovne sisteme u različitim zemljama
- Mapiranje ekvivalentnih stupnjeva između različitih obrazovnih sistema

### 2.3. Standardizacija pozicija i zanimanja

- Kreiranje baze standardiziranih pozicija i zanimanja na više jezika
- Mapiranje ekvivalentnih pozicija između jezika (npr. "Elektriker" = "Električar" = "Electrician")
- Kategorizacija pozicija po industrijama i sektorima
- Povezivanje pozicija s relevantnim vještinama

### 2.4. Standardizacija obrazovnih stupnjeva

- Kreiranje baze standardiziranih obrazovnih stupnjeva na više jezika
- Mapiranje ekvivalentnih stupnjeva između različitih obrazovnih sistema
- Kategorizacija stupnjeva po nivoima i područjima
- Podrška za različite formate obrazovnih kvalifikacija

### 2.5. Zemlja porijekla i jezične vještine

- Dodavanje polja za zemlju porijekla kandidata
- Poboljšano praćenje jezičnih vještina s standardiziranim nivoima (A1-C2)
- Povezivanje zemlje porijekla s preferiranim jezikom
- Korištenje zemlje porijekla za bolje podudaranje s premium oglasima

## 3. Tehnička implementacija

### 3.1. Ažuriranje modela podataka

#### 3.1.1. Kandidati

```typescript
// Ažurirani Candidate interfejs
export interface Candidate extends User {
  role: "candidate";
  profession: string;
  age: number;
  gender: Gender;
  country: string; // ISO kod zemlje (npr. "BA", "RS", "HR")
  preferredLanguage: string; // Preferirani jezik korisničkog sučelja
  skills?: string[];
  
  // Strukturirano radno iskustvo
  workExperience: WorkExperience[];
  noWorkExperience?: boolean; // Za kandidate bez radnog iskustva
  
  // Strukturirana edukacija
  education: Education[];
  
  // Jezične vještine
  germanLanguageLevel: LanguageLevel;
  englishLanguageLevel: LanguageLevel;
  otherLanguages?: LanguageSkill[];
  
  desiredPositions?: string[];
  preferences?: string[];
  benefits?: string[]; // Odabrane pogodnosti/karakteristike posla za podudaranje
  profileCompleted?: boolean; // Zastavica koja označava je li profil kompletan
}

// Ažurirani WorkExperience interfejs
export interface WorkExperience {
  company: string;
  position: string;
  positionId?: string; // Referenca na standardiziranu poziciju
  startDate: Date; // Puni datum (mjesec/godina)
  endDate?: Date; // Puni datum ili undefined za trenutni posao
  current: boolean;
  description?: string;
  country?: string; // Zemlja u kojoj je radio
  industry?: string; // Industrija/sektor
}

// Ažurirani Education interfejs
export interface Education {
  institution: string;
  degree: string;
  degreeId?: string; // Referenca na standardizirani stupanj
  field: string;
  startDate: Date; // Puni datum (mjesec/godina)
  endDate?: Date; // Puni datum ili undefined za trenutno obrazovanje
  current: boolean;
  description?: string;
  country?: string; // Zemlja obrazovne institucije
}
```

#### 3.1.2. Nove kolekcije

```typescript
// Standardizirane pozicije
export interface Position {
  id: string;
  translations: {
    [languageCode: string]: string; // npr. { "de": "Elektriker", "en": "Electrician", "bs": "Električar" }
  };
  category: string; // Kategorija pozicije (npr. "construction", "healthcare")
  skills?: string[]; // Povezane vještine
  aliases?: string[]; // Alternativni nazivi
}

// Standardizirani obrazovni stupnjevi
export interface Degree {
  id: string;
  translations: {
    [languageCode: string]: string; // npr. { "de": "Bachelor", "en": "Bachelor's Degree", "bs": "Bakalaureat" }
  };
  level: string; // Nivo obrazovanja (npr. "bachelor", "master", "phd")
  equivalentTo?: string[]; // ID-ovi ekvivalentnih stupnjeva u drugim sistemima
}

// Zemlje
export interface Country {
  id: string; // ISO kod (npr. "BA", "RS", "HR")
  name: {
    [languageCode: string]: string; // npr. { "de": "Bosnien und Herzegowina", "en": "Bosnia and Herzegovina" }
  };
  flagUrl: string; // URL zastave
  languages: string[]; // Službeni jezici
  region: "balkans" | "eu" | "other"; // Regija
}
```

### 3.2. Korisničko sučelje

#### 3.2.1. Forma za radno iskustvo

- Polja za unos kompanije, pozicije, opisa
- Dropdown za odabir standardizirane pozicije
- Date pickeri za početni i završni datum (mjesec/godina)
- Checkbox za "Trenutno radim ovdje"
- Dropdown za odabir zemlje
- Dropdown za odabir industrije/sektora
- Mogućnost dodavanja više radnih iskustava

#### 3.2.2. Forma za edukaciju

- Polja za unos institucije, smjera/područja, opisa
- Dropdown za odabir standardiziranog stupnja
- Date pickeri za početni i završni datum (mjesec/godina)
- Checkbox za "Trenutno studiram"
- Dropdown za odabir zemlje
- Mogućnost dodavanja više edukacija

#### 3.2.3. Forma za osnovne informacije

- Polje za unos profesije
- Dropdown za odabir standardizirane profesije
- Polje za unos godina
- Dropdown za odabir spola
- Dropdown za odabir zemlje porijekla
- Dropdown za odabir preferiranog jezika

#### 3.2.4. Forma za jezične vještine

- Dropdowni za odabir nivoa njemačkog i engleskog jezika (A1-C2, Native)
- Mogućnost dodavanja drugih jezika s odabirom nivoa
- Vizualni prikaz nivoa jezičnih vještina

### 3.3. Backend funkcionalnosti

#### 3.3.1. Računanje ukupnog radnog iskustva

```typescript
// Funkcija za računanje ukupnog radnog iskustva po poziciji
function calculateTotalExperience(workExperience: WorkExperience[], positionId?: string): number {
  // Filtriranje po poziciji ako je specificirana
  const relevantExperience = positionId
    ? workExperience.filter(exp => exp.positionId === positionId)
    : workExperience;
  
  // Računanje ukupnog iskustva u mjesecima
  let totalMonths = 0;
  
  for (const exp of relevantExperience) {
    const startDate = new Date(exp.startDate);
    const endDate = exp.current ? new Date() : new Date(exp.endDate!);
    
    // Računanje razlike u mjesecima
    const months = (endDate.getFullYear() - startDate.getFullYear()) * 12 +
      (endDate.getMonth() - startDate.getMonth());
    
    totalMonths += months;
  }
  
  return totalMonths;
}
```

#### 3.3.2. Pretraživanje kandidata po poziciji

```typescript
// Funkcija za pretraživanje kandidata po poziciji
async function searchCandidatesByPosition(
  positionId: string,
  minExperienceMonths: number = 0,
  language: string = 'en'
): Promise<Candidate[]> {
  // Dohvaćanje standardizirane pozicije
  const positionDoc = await getDoc(doc(db, "positions", positionId));
  
  if (!positionDoc.exists()) {
    throw new Error("Position not found");
  }
  
  const position = positionDoc.data() as Position;
  
  // Dohvaćanje svih ekvivalentnih pozicija na različitim jezicima
  const positionNames = Object.values(position.translations);
  
  // Pretraživanje kandidata koji imaju iskustvo s tom pozicijom
  const candidatesQuery = query(
    collection(db, "candidates"),
    where("workExperience.position", "array-contains-any", positionNames)
  );
  
  const candidatesSnapshot = await getDocs(candidatesQuery);
  const candidates: Candidate[] = [];
  
  // Filtriranje kandidata po iskustvu
  candidatesSnapshot.forEach(doc => {
    const candidate = doc.data() as Candidate;
    
    // Računanje ukupnog iskustva za relevantnu poziciju
    const totalExperience = calculateTotalExperience(candidate.workExperience, positionId);
    
    if (totalExperience >= minExperienceMonths) {
      candidates.push({
        id: doc.id,
        ...candidate,
        totalExperience
      });
    }
  });
  
  return candidates;
}
```

## 4. Plan implementacije

### 4.1. Faza 1: Priprema podataka

1. Kreirati kolekcije za standardizirane pozicije i stupnjeve
2. Popuniti bazu s početnim setom pozicija i stupnjeva na više jezika
3. Kreirati kolekciju zemalja s prijevodima i zastavama
4. Ažurirati model podataka za kandidate

### 4.2. Faza 2: Ažuriranje formi

1. Implementirati novu formu za unos radnog iskustva
2. Implementirati novu formu za unos edukacije
3. Ažurirati formu za osnovne informacije
4. Ažurirati formu za jezične vještine

### 4.3. Faza 3: Migracija podataka

1. Kreirati skriptu za migraciju postojećih podataka o radnom iskustvu
2. Kreirati skriptu za migraciju postojećih podataka o edukaciji
3. Dodati zemlju porijekla postojećim kandidatima (na osnovu lokacije ili jezika)
4. Validirati migrirane podatke

### 4.4. Faza 4: Implementacija pretraživanja

1. Implementirati pretraživanje kandidata po standardiziranim pozicijama
2. Implementirati filtriranje po ukupnom radnom iskustvu
3. Implementirati filtriranje po obrazovnim stupnjevima
4. Implementirati filtriranje po zemlji porijekla

### 4.5. Faza 5: Prikaz profila

1. Ažurirati prikaz profila kandidata
2. Implementirati vizualizaciju radnog iskustva (vremenska linija)
3. Implementirati vizualizaciju edukacije
4. Dodati prikaz ukupnog radnog iskustva po poziciji

## 5. Testiranje

### 5.1. Testiranje unosa podataka

- Testirati unos različitih formata radnog iskustva
- Testirati unos različitih formata edukacije
- Provjeriti validaciju datuma i obaveznih polja
- Testirati dodavanje i uklanjanje stavki

### 5.2. Testiranje pretraživanja

- Testirati pretraživanje po pozicijama na različitim jezicima
- Testirati filtriranje po iskustvu
- Testirati filtriranje po obrazovanju
- Testirati kombinacije različitih filtera

### 5.3. Testiranje migracije

- Testirati migraciju postojećih podataka
- Provjeriti integritet podataka nakon migracije
- Testirati kompatibilnost sa starim formatom podataka

## 6. Zaključak

Implementacija strukturiranih profila kandidata značajno će poboljšati kvalitetu podataka i mogućnosti pretraživanja na BalkanWorkForce platformi. Standardizacija pozicija i obrazovnih stupnjeva omogućit će preciznije podudaranje kandidata s poslovima, dok će detaljno praćenje radnog iskustva i edukacije pružiti poslodavcima bolji uvid u kvalifikacije kandidata.

Ova implementacija zahtijeva značajne promjene u modelu podataka i korisničkom sučelju, ali će rezultirati platformom koja efikasnije povezuje poslodavce s kvalificiranim kandidatima.
