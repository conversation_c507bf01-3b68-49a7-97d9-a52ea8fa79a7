// Skripta za sinkronizaciju statičkih podataka s Firestore bazom
const { initializeApp } = require('firebase/app');
const { getFirestore, doc, setDoc, collection, getDocs, deleteDoc } = require('firebase/firestore');

// Firebase konfiguracija
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Inicijalizacija Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Direktno definiranje podataka
const positions = [
  {
    id: "electrician",
    translations: {
      de: "Elektriker",
      en: "Electrician",
      bs: "Električar",
      hr: "Električar",
      sr: "Električar",
      me: "Električar",
      mk: "Електричар",
      al: "Elektricist"
    },
    category: "construction",
    skills: ["electrical-wiring", "electrical-maintenance", "electrical-installation"]
  },
  {
    id: "plumber",
    translations: {
      de: "Klempner",
      en: "Plumber",
      bs: "Vodoinstalater",
      hr: "Vodoinstalater",
      sr: "Vodoinstalater",
      me: "Vodoinstalater",
      mk: "Водоводџија",
      al: "Hidraulik"
    },
    category: "construction",
    skills: ["plumbing", "pipe-fitting", "water-systems"]
  },
  {
    id: "carpenter",
    translations: {
      de: "Tischler",
      en: "Carpenter",
      bs: "Stolar",
      hr: "Stolar",
      sr: "Stolar",
      me: "Stolar",
      mk: "Столар",
      al: "Marangoz"
    },
    category: "construction",
    skills: ["woodworking", "furniture-making", "carpentry"]
  },
  {
    id: "welder",
    translations: {
      de: "Schweißer",
      en: "Welder",
      bs: "Zavarivač",
      hr: "Zavarivač",
      sr: "Zavarivač",
      me: "Zavarivač",
      mk: "Заварувач",
      al: "Saldator"
    },
    category: "manufacturing",
    skills: ["welding", "metal-fabrication", "blueprint-reading"]
  },
  {
    id: "chef",
    translations: {
      de: "Koch",
      en: "Chef",
      bs: "Kuhar",
      hr: "Kuhar",
      sr: "Kuvar",
      me: "Kuvar",
      mk: "Готвач",
      al: "Kuzhinier"
    },
    category: "hospitality",
    skills: ["cooking", "food-preparation", "menu-planning"]
  }
];

const studyFields = [
  {
    id: "electrical-engineering",
    translations: {
      de: "Elektrotechnik",
      en: "Electrical Engineering",
      bs: "Elektrotehnika",
      hr: "Elektrotehnika",
      sr: "Elektrotehnika",
      me: "Elektrotehnika",
      mk: "Електротехника",
      al: "Inxhinieri Elektrike"
    },
    category: "engineering"
  },
  {
    id: "mechanical-engineering",
    translations: {
      de: "Maschinenbau",
      en: "Mechanical Engineering",
      bs: "Mašinstvo",
      hr: "Strojarstvo",
      sr: "Mašinstvo",
      me: "Mašinstvo",
      mk: "Машинство",
      al: "Inxhinieri Mekanike"
    },
    category: "engineering"
  },
  {
    id: "automotive-mechanic",
    translations: {
      de: "Kfz-Mechaniker",
      en: "Automotive Mechanic",
      bs: "Automehaničar",
      hr: "Automehaničar",
      sr: "Automehaničar",
      me: "Automehaničar",
      mk: "Автомеханичар",
      al: "Mekanik Automjetesh"
    },
    category: "vocational"
  },
  {
    id: "culinary-arts",
    translations: {
      de: "Kochkunst",
      en: "Culinary Arts",
      bs: "Kuharstvo",
      hr: "Kuharstvo",
      sr: "Kuvarstvo",
      me: "Kuvarstvo",
      mk: "Готварство",
      al: "Arti Kulinar"
    },
    category: "hospitality"
  },
  {
    id: "nursing",
    translations: {
      de: "Krankenpflege",
      en: "Nursing",
      bs: "Sestrinstvo",
      hr: "Sestrinstvo",
      sr: "Sestrinstvo",
      me: "Sestrinstvo",
      mk: "Медицинска нега",
      al: "Infermieri"
    },
    category: "healthcare"
  }
];

const driverLicenses = [
  {
    id: "b",
    translations: {
      en: "Category B (Car)",
      de: "Kategorie B (Auto)",
      bs: "Kategorija B (Automobil)"
    },
    description: {
      en: "Vehicles up to 3.5 tonnes",
      de: "Fahrzeuge bis 3,5 Tonnen",
      bs: "Vozila do 3,5 tone"
    }
  },
  {
    id: "c",
    translations: {
      en: "Category C (Truck)",
      de: "Kategorie C (LKW)",
      bs: "Kategorija C (Kamion)"
    },
    description: {
      en: "Vehicles over 3.5 tonnes",
      de: "Fahrzeuge über 3,5 Tonnen",
      bs: "Vozila preko 3,5 tone"
    }
  },
  {
    id: "d",
    translations: {
      en: "Category D (Bus)",
      de: "Kategorie D (Bus)",
      bs: "Kategorija D (Autobus)"
    },
    description: {
      en: "Vehicles for passenger transport",
      de: "Fahrzeuge für Personenbeförderung",
      bs: "Vozila za prevoz putnika"
    }
  }
];

const benefits = [
  {
    id: "accommodation",
    translations: {
      en: "Accommodation Provided",
      de: "Unterkunft gestellt",
      bs: "Osiguran smještaj"
    },
    category: "housing"
  },
  {
    id: "meals",
    translations: {
      en: "Meals Provided",
      de: "Mahlzeiten gestellt",
      bs: "Osigurana ishrana"
    },
    category: "food"
  },
  {
    id: "transport",
    translations: {
      en: "Transport to Work",
      de: "Transport zur Arbeit",
      bs: "Prevoz do posla"
    },
    category: "transportation"
  }
];

const countries = [
  {
    id: "de",
    name: "Germany",
    translations: {
      en: "Germany",
      de: "Deutschland",
      bs: "Njemačka"
    },
    flag: "🇩🇪"
  },
  {
    id: "at",
    name: "Austria",
    translations: {
      en: "Austria",
      de: "Österreich",
      bs: "Austrija"
    },
    flag: "🇦🇹"
  },
  {
    id: "ch",
    name: "Switzerland",
    translations: {
      en: "Switzerland",
      de: "Schweiz",
      bs: "Švicarska"
    },
    flag: "🇨🇭"
  },
  {
    id: "hr",
    name: "Croatia",
    translations: {
      en: "Croatia",
      de: "Kroatien",
      bs: "Hrvatska"
    },
    flag: "🇭🇷"
  },
  {
    id: "si",
    name: "Slovenia",
    translations: {
      en: "Slovenia",
      de: "Slowenien",
      bs: "Slovenija"
    },
    flag: "🇸🇮"
  }
];

// Funkcija za sinkronizaciju podataka s Firestore kolekcijom
async function syncDataToFirestore(collectionName, data) {
  console.log(`Sinkronizacija kolekcije ${collectionName}...`);

  // Dohvaćanje postojećih dokumenata
  const snapshot = await getDocs(collection(db, collectionName));
  const existingDocs = new Map();
  snapshot.forEach(doc => {
    existingDocs.set(doc.id, doc.data());
  });

  // Dodavanje ili ažuriranje dokumenata
  for (const item of data) {
    try {
      const docRef = doc(db, collectionName, item.id);

      // Dodajemo timestamp ako ne postoji
      const docData = {
        ...item,
        updatedAt: new Date(),
      };

      if (!existingDocs.has(item.id)) {
        docData.createdAt = new Date();
      }

      await setDoc(docRef, docData);
      console.log(`Dokument ${item.id} dodan/ažuriran u kolekciji ${collectionName}`);
    } catch (error) {
      console.error(`Greška prilikom dodavanja dokumenta ${item.id} u kolekciju ${collectionName}:`, error);
    }
  }

  // Brisanje dokumenata koji više ne postoje u statičkim podacima
  const staticIds = new Set(data.map(item => item.id));
  for (const [docId] of existingDocs) {
    if (!staticIds.has(docId)) {
      try {
        await deleteDoc(doc(db, collectionName, docId));
        console.log(`Dokument ${docId} obrisan iz kolekcije ${collectionName}`);
      } catch (error) {
        console.error(`Greška prilikom brisanja dokumenta ${docId} iz kolekcije ${collectionName}:`, error);
      }
    }
  }

  console.log(`Sinkronizacija kolekcije ${collectionName} završena.`);
}

// Glavna funkcija za sinkronizaciju svih podataka
async function syncAllData() {
  try {
    // Sinkronizacija podataka s Firestore kolekcijama
    await syncDataToFirestore('positions', positions);
    await syncDataToFirestore('studyFields', studyFields);
    await syncDataToFirestore('driverLicenses', driverLicenses);
    await syncDataToFirestore('benefits', benefits);
    await syncDataToFirestore('countries', countries);

    console.log('Svi podaci uspješno sinkronizirani s Firestore bazom!');
  } catch (error) {
    console.error('Greška prilikom sinkronizacije podataka:', error);
  }
}

// Poziv glavne funkcije
syncAllData().then(() => {
  console.log('Proces sinkronizacije završen.');
  process.exit(0);
}).catch(error => {
  console.error('Greška u procesu sinkronizacije:', error);
  process.exit(1);
});
