import { useState, useEffect } from "react";
import { Education } from "@/types";
import { degrees } from "@/data/degrees";
import { studyFields } from "@/data/studyFields";
import { countries } from "@/data/countries";

interface EducationFormProps {
  value: Education[];
  onChange: (value: Education[]) => void;
}

export default function EducationForm({ value, onChange }: EducationFormProps) {
  const [educations, setEducations] = useState<Education[]>(Array.isArray(value) ? value : []);

  useEffect(() => {
    // Only update parent when educations change, but avoid deep comparison
    // to prevent infinite loops
    const handleChange = () => {
      onChange(educations);
    };
    handleChange();
    // We're intentionally not including educations in the dependency array
    // to prevent infinite loops, but still want to run this when onChange changes
  }, [onChange]);

  const addEducation = () => {
    setEducations([
      ...educations,
      {
        degree: "",
        field: "",
        startDate: new Date(),
        endDate: new Date(),
        description: "",
      },
    ]);
  };

  const updateEducation = (index: number, field: keyof Education, value: any) => {
    const updatedEducations = [...educations];
    updatedEducations[index] = {
      ...updatedEducations[index],
      [field]: value,
    };
    setEducations(updatedEducations);
  };

  const removeEducation = (index: number) => {
    const updatedEducations = [...educations];
    updatedEducations.splice(index, 1);
    setEducations(updatedEducations);
  };

  // Helper function to format date for input
  const formatDateForInput = (date: Date | undefined) => {
    if (!date) return "";
    // Check if date is valid
    const d = new Date(date);
    if (isNaN(d.getTime())) return "";
    return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, "0")}`;
  };

  // Helper function to parse date from input
  const parseDateFromInput = (dateString: string) => {
    if (!dateString) return new Date();
    try {
      const [year, month] = dateString.split("-").map(Number);
      if (isNaN(year) || isNaN(month)) return new Date();
      const date = new Date(year, month - 1);
      return date;
    } catch (error) {
      console.error("Error parsing date:", error);
      return new Date();
    }
  };

  return (
    <div className="space-y-6">
      {educations.map((education, index) => (
        <div key={index} className="border border-neutral-200 dark:border-neutral-700 rounded-lg p-4">
          <div className="flex justify-between items-center mb-4">
            <h3 className="text-lg font-medium">Education #{index + 1}</h3>
            <button
              type="button"
              onClick={() => removeEducation(index)}
              className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
            >
              Remove
            </button>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium mb-1">
                Degree <span className="text-red-500">*</span>
              </label>
              <select
                value={education.degreeId || ""}
                onChange={(e) => {
                  const degreeId = e.target.value;
                  const degree = degrees.find(d => d.id === degreeId);
                  updateEducation(index, "degreeId", degreeId || undefined);
                  updateEducation(index, "degree", degree ? degree.translations.en : "");
                }}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                required
              >
                <option value="">Select a degree</option>
                {degrees.map((degree) => (
                  <option key={degree.id} value={degree.id}>
                    {degree.translations.en}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">
                Field of Study <span className="text-red-500">*</span>
              </label>
              <select
                value={education.fieldId || ""}
                onChange={(e) => {
                  const fieldId = e.target.value;
                  const field = studyFields.find(f => f.id === fieldId);
                  updateEducation(index, "fieldId", fieldId || undefined);
                  updateEducation(index, "field", field ? field.translations.en : "");
                }}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                required
              >
                <option value="">Select a field of study</option>
                {studyFields.map((field) => (
                  <option key={field.id} value={field.id}>
                    {field.translations.en}
                  </option>
                ))}
              </select>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium mb-1">
                Start Date <span className="text-red-500">*</span>
              </label>
              <input
                type="month"
                value={formatDateForInput(education.startDate)}
                onChange={(e) => updateEducation(index, "startDate", parseDateFromInput(e.target.value))}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                required
              />
            </div>

            <div>
              <label className="block text-sm font-medium mb-1">
                End Date <span className="text-red-500">*</span>
              </label>
              <input
                type="month"
                value={formatDateForInput(education.endDate)}
                onChange={(e) => updateEducation(index, "endDate", parseDateFromInput(e.target.value))}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                required
              />
            </div>
          </div>

          <div className="mb-4">
            <label className="block text-sm font-medium mb-1">
              Country
            </label>
            <select
              value={education.country || ""}
              onChange={(e) => updateEducation(index, "country", e.target.value || undefined)}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
            >
              <option value="">Select a country</option>
              {countries.map((country) => (
                <option key={country.id} value={country.id}>
                  {country.name}
                </option>
              ))}
            </select>
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              Description
            </label>
            <textarea
              value={education.description || ""}
              onChange={(e) => updateEducation(index, "description", e.target.value)}
              rows={3}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              placeholder="Describe your studies, achievements, or thesis"
            ></textarea>
          </div>
        </div>
      ))}

      <button
        type="button"
        onClick={addEducation}
        className="flex items-center text-neutral-700 dark:text-neutral-300 hover:text-neutral-900 dark:hover:text-white"
      >
        <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
          <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
        </svg>
        Add Education
      </button>
    </div>
  );
}
