"use client";

import { useState, useEffect } from "react";
import { collection, query, getDocs, orderBy, doc, updateDoc, deleteDoc, setDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { Position } from "@/types";

export default function AdminPositionsPage() {
  const [positions, setPositions] = useState<Position[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [editingPosition, setEditingPosition] = useState<Position | null>(null);
  const [newPosition, setNewPosition] = useState<Partial<Position>>({
    id: "",
    translations: { en: "", de: "", bs: "" },
    category: "",
  });
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const supportedLanguages = [
    { code: "en", name: "English" },
    { code: "de", name: "German" },
    { code: "bs", name: "Bosnian" },
  ];

  const categories = [
    "construction",
    "healthcare",
    "hospitality",
    "manufacturing",
    "transportation",
    "agriculture",
    "it",
    "education",
    "retail",
    "other",
  ];

  useEffect(() => {
    const fetchPositions = async () => {
      try {
        const positionsQuery = query(
          collection(db, "positions"),
          orderBy("category", "asc")
        );
        const snapshot = await getDocs(positionsQuery);
        const positionsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        })) as Position[];
        setPositions(positionsData);
      } catch (error) {
        console.error("Error fetching positions:", error);
        setError("Failed to load positions. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchPositions();
  }, []);

  const filteredPositions = positions.filter(position => {
    const searchLower = searchTerm.toLowerCase();
    return (
      position.category.toLowerCase().includes(searchLower) ||
      Object.values(position.translations).some(translation => 
        translation.toLowerCase().includes(searchLower)
      )
    );
  });

  const handleEditPosition = (position: Position) => {
    setEditingPosition(position);
    setError(null);
  };

  const handleCancelEdit = () => {
    setEditingPosition(null);
    setError(null);
  };

  const handleUpdatePosition = async () => {
    if (!editingPosition) return;

    // Validate
    if (!editingPosition.category) {
      setError("Category is required");
      return;
    }

    // Check if at least one translation is provided
    const hasTranslation = Object.values(editingPosition.translations).some(t => t.trim() !== "");
    if (!hasTranslation) {
      setError("At least one translation is required");
      return;
    }

    try {
      await updateDoc(doc(db, "positions", editingPosition.id), {
        translations: editingPosition.translations,
        category: editingPosition.category,
        updatedAt: new Date(),
      });

      // Update local state
      setPositions(positions.map(p => 
        p.id === editingPosition.id ? editingPosition : p
      ));
      
      setEditingPosition(null);
      setSuccess("Position updated successfully");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Error updating position:", error);
      setError("Failed to update position. Please try again.");
    }
  };

  const handleDeletePosition = async (positionId: string) => {
    if (!confirm("Are you sure you want to delete this position? This action cannot be undone.")) {
      return;
    }

    try {
      await deleteDoc(doc(db, "positions", positionId));
      
      // Update local state
      setPositions(positions.filter(p => p.id !== positionId));
      setSuccess("Position deleted successfully");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Error deleting position:", error);
      setError("Failed to delete position. Please try again.");
    }
  };

  const handleAddNewPosition = () => {
    setIsAddingNew(true);
    setNewPosition({
      id: "",
      translations: { en: "", de: "", bs: "" },
      category: "",
    });
    setError(null);
  };

  const handleCancelAdd = () => {
    setIsAddingNew(false);
    setError(null);
  };

  const handleCreatePosition = async () => {
    // Validate
    if (!newPosition.id || !newPosition.id.trim()) {
      setError("Position ID is required");
      return;
    }

    if (!newPosition.category) {
      setError("Category is required");
      return;
    }

    // Check if at least one translation is provided
    const hasTranslation = Object.values(newPosition.translations || {}).some(t => t && t.trim() !== "");
    if (!hasTranslation) {
      setError("At least one translation is required");
      return;
    }

    // Check if ID already exists
    if (positions.some(p => p.id === newPosition.id)) {
      setError("A position with this ID already exists");
      return;
    }

    try {
      const positionData: Position = {
        id: newPosition.id,
        translations: newPosition.translations || { en: "", de: "", bs: "" },
        category: newPosition.category,
      };

      await setDoc(doc(db, "positions", newPosition.id), {
        ...positionData,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Update local state
      setPositions([...positions, positionData]);
      setIsAddingNew(false);
      setSuccess("Position created successfully");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Error creating position:", error);
      setError("Failed to create position. Please try again.");
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Position Management</h1>
        <button
          onClick={handleAddNewPosition}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
        >
          Add New Position
        </button>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 p-4 rounded-md mb-6">
          {success}
        </div>
      )}

      {/* Search */}
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <label htmlFor="search" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Search Positions
            </label>
            <input
              type="text"
              id="search"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by name or category"
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
            />
          </div>
        </div>
      </div>

      {/* Add New Position Form */}
      {isAddingNew && (
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-lg font-medium mb-4">Add New Position</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                Position ID <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={newPosition.id || ""}
                onChange={(e) => setNewPosition({...newPosition, id: e.target.value})}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                placeholder="e.g., electrician"
              />
              <p className="text-xs text-neutral-500 mt-1">
                Use lowercase letters, numbers, and hyphens only. This ID will be used in the database.
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                Category <span className="text-red-500">*</span>
              </label>
              <select
                value={newPosition.category || ""}
                onChange={(e) => setNewPosition({...newPosition, category: e.target.value})}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              >
                <option value="">Select a category</option>
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <h3 className="text-md font-medium mb-2">Translations</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {supportedLanguages.map(language => (
              <div key={language.code}>
                <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                  {language.name}
                </label>
                <input
                  type="text"
                  value={newPosition.translations?.[language.code] || ""}
                  onChange={(e) => {
                    const updatedTranslations = { 
                      ...newPosition.translations,
                      [language.code]: e.target.value 
                    };
                    setNewPosition({...newPosition, translations: updatedTranslations});
                  }}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                  placeholder={`${language.name} translation`}
                />
              </div>
            ))}
          </div>
          
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleCancelAdd}
              className="px-4 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-700"
            >
              Cancel
            </button>
            <button
              onClick={handleCreatePosition}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
            >
              Create Position
            </button>
          </div>
        </div>
      )}

      {/* Positions Table */}
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-neutral-200 dark:divide-neutral-700">
            <thead className="bg-neutral-50 dark:bg-neutral-900">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Category
                </th>
                {supportedLanguages.map(language => (
                  <th key={language.code} className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                    {language.name}
                  </th>
                ))}
                <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-neutral-800 divide-y divide-neutral-200 dark:divide-neutral-700">
              {filteredPositions.map((position) => (
                <tr key={position.id}>
                  {editingPosition && editingPosition.id === position.id ? (
                    // Edit mode
                    <>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-neutral-900 dark:text-white">
                          {position.id}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <select
                          value={editingPosition.category}
                          onChange={(e) => setEditingPosition({...editingPosition, category: e.target.value})}
                          className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-sm"
                        >
                          {categories.map(category => (
                            <option key={category} value={category}>
                              {category.charAt(0).toUpperCase() + category.slice(1)}
                            </option>
                          ))}
                        </select>
                      </td>
                      {supportedLanguages.map(language => (
                        <td key={language.code} className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="text"
                            value={editingPosition.translations[language.code] || ""}
                            onChange={(e) => {
                              const updatedTranslations = { 
                                ...editingPosition.translations,
                                [language.code]: e.target.value 
                              };
                              setEditingPosition({...editingPosition, translations: updatedTranslations});
                            }}
                            className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-sm"
                          />
                        </td>
                      ))}
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={handleCancelEdit}
                          className="text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white mr-3"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={handleUpdatePosition}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
                        >
                          Save
                        </button>
                      </td>
                    </>
                  ) : (
                    // View mode
                    <>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-neutral-900 dark:text-white">
                          {position.id}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-neutral-500 dark:text-neutral-400">
                          {position.category.charAt(0).toUpperCase() + position.category.slice(1)}
                        </div>
                      </td>
                      {supportedLanguages.map(language => (
                        <td key={language.code} className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-neutral-500 dark:text-neutral-400">
                            {position.translations[language.code] || "-"}
                          </div>
                        </td>
                      ))}
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleEditPosition(position)}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-3"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDeletePosition(position.id)}
                          className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                        >
                          Delete
                        </button>
                      </td>
                    </>
                  )}
                </tr>
              ))}
              {filteredPositions.length === 0 && (
                <tr>
                  <td colSpan={5 + supportedLanguages.length} className="px-6 py-4 text-center text-sm text-neutral-500 dark:text-neutral-400">
                    No positions found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
