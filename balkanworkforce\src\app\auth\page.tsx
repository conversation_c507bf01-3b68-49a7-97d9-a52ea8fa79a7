"use client";

import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import Link from "next/link";
import Image from "next/image";

export default function AuthPage() {
  const [role, setRole] = useState<"employer" | "candidate" | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [isLoading, setIsLoading] = useState(false);
  const { user, signInWithGoogle } = useAuth();
  const router = useRouter();

  // If user is already logged in, redirect to appropriate dashboard
  if (user) {
    if (user.role === "employer") {
      router.push("/employer/dashboard");
    } else if (user.role === "candidate") {
      router.push("/candidate/dashboard");
    }
  }

  const handleSignIn = async () => {
    if (!role) return;

    setIsLoading(true);
    setError(null);

    try {
      const result = await signInWithGoogle(role);

      if (!result.success) {
        setError(result.error || "Failed to sign in. Please try again.");
      }
      // Redirect will happen automatically in the useEffect in AuthContext
    } catch (error: any) {
      console.error("Error signing in:", error);
      setError(error.message || "An unexpected error occurred. Please try again.");
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <div className="max-w-4xl mx-auto py-12">
      <h1 className="text-3xl font-bold text-center mb-8">Join BalkanWorkForce</h1>

      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8">
        <h2 className="text-xl font-semibold mb-6 text-center">Choose your account type</h2>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          {/* Employer Option */}
          <div
            className={`p-6 rounded-lg border-2 cursor-pointer transition-all ${
              role === "employer"
                ? "border-neutral-900 dark:border-white bg-neutral-50 dark:bg-neutral-700"
                : "border-neutral-200 dark:border-neutral-700 hover:border-neutral-400 dark:hover:border-neutral-500"
            }`}
            onClick={() => setRole("employer")}
          >
            <div className="flex items-center mb-4">
              <div className="bg-neutral-100 dark:bg-neutral-700 w-12 h-12 flex items-center justify-center rounded-full mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h3 className="text-lg font-medium">I'm an Employer</h3>
            </div>
            <p className="text-neutral-600 dark:text-neutral-400 mb-4">
              I want to post jobs and find skilled workers from the Balkans for my company.
            </p>
            <ul className="space-y-2 text-sm text-neutral-600 dark:text-neutral-400">
              <li className="flex items-start">
                <span className="mr-2 text-green-500">✓</span>
                Post job listings
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-green-500">✓</span>
                Access to premium features
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-green-500">✓</span>
                Review candidate applications
              </li>
            </ul>
          </div>

          {/* Candidate Option */}
          <div
            className={`p-6 rounded-lg border-2 cursor-pointer transition-all ${
              role === "candidate"
                ? "border-neutral-900 dark:border-white bg-neutral-50 dark:bg-neutral-700"
                : "border-neutral-200 dark:border-neutral-700 hover:border-neutral-400 dark:hover:border-neutral-500"
            }`}
            onClick={() => setRole("candidate")}
          >
            <div className="flex items-center mb-4">
              <div className="bg-neutral-100 dark:bg-neutral-700 w-12 h-12 flex items-center justify-center rounded-full mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <h3 className="text-lg font-medium">I'm a Job Seeker</h3>
            </div>
            <p className="text-neutral-600 dark:text-neutral-400 mb-4">
              I want to find job opportunities with EU companies.
            </p>
            <ul className="space-y-2 text-sm text-neutral-600 dark:text-neutral-400">
              <li className="flex items-start">
                <span className="mr-2 text-green-500">✓</span>
                Anonymous profile (only shows profession and age)
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-green-500">✓</span>
                Apply to jobs across the EU
              </li>
              <li className="flex items-start">
                <span className="mr-2 text-green-500">✓</span>
                Get matched with employers
              </li>
            </ul>
          </div>
        </div>

        <div className="text-center">
          {error && (
            <div className="mb-4 p-3 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded-md">
              {error}
            </div>
          )}

          <button
            onClick={handleSignIn}
            disabled={!role || isLoading}
            className={`flex items-center justify-center mx-auto px-6 py-3 rounded-md font-medium ${
              !role
                ? "bg-neutral-200 text-neutral-500 cursor-not-allowed"
                : isLoading
                ? "bg-neutral-700 text-white cursor-wait"
                : "bg-neutral-900 hover:bg-neutral-800 text-white"
            }`}
          >
            {isLoading ? (
              <>
                <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                  <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                  <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                Signing in...
              </>
            ) : (
              <>
                <svg className="w-5 h-5 mr-2" viewBox="0 0 24 24">
                  <path
                    fill="currentColor"
                    d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                  />
                  <path
                    fill="currentColor"
                    d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                  />
                  <path
                    fill="currentColor"
                    d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                  />
                  <path
                    fill="currentColor"
                    d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                  />
                  <path fill="none" d="M1 1h22v22H1z" />
                </svg>
                Sign in with Google
              </>
            )}
          </button>

          <p className="mt-6 text-sm text-neutral-600 dark:text-neutral-400">
            By signing up, you agree to our{" "}
            <Link href="/terms" className="text-neutral-900 dark:text-white underline">
              Terms of Service
            </Link>{" "}
            and{" "}
            <Link href="/privacy" className="text-neutral-900 dark:text-white underline">
              Privacy Policy
            </Link>
          </p>
        </div>
      </div>
    </div>
  );
}
