import { NextRequest, NextResponse } from 'next/server';
import { doc, getDoc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';

export async function POST(request: NextRequest) {
  try {
    // Check authentication
    const session = await getServerSession(authOptions);
    
    if (!session || !session.user) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
    }
    
    // Parse request body
    const body = await request.json();
    const { subscriptionId } = body;
    
    if (!subscriptionId) {
      return NextResponse.json({ error: 'Subscription ID is required' }, { status: 400 });
    }
    
    // Get subscription document
    const subscriptionRef = doc(db, 'subscriptions', subscriptionId);
    const subscriptionSnap = await getDoc(subscriptionRef);
    
    if (!subscriptionSnap.exists()) {
      return NextResponse.json({ error: 'Subscription not found' }, { status: 404 });
    }
    
    const subscriptionData = subscriptionSnap.data();
    
    // Check if subscription belongs to the current user
    if (subscriptionData.employerId !== session.user.id) {
      return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }
    
    // Check if subscription has invites remaining
    if (subscriptionData.invitesRemaining <= 0) {
      return NextResponse.json({ error: 'No invites remaining' }, { status: 400 });
    }
    
    // Update subscription
    await updateDoc(subscriptionRef, {
      invitesRemaining: subscriptionData.invitesRemaining - 1,
      updatedAt: new Date()
    });
    
    return NextResponse.json({ success: true });
  } catch (error) {
    console.error('Error using invite:', error);
    return NextResponse.json({ error: 'Internal server error' }, { status: 500 });
  }
}
