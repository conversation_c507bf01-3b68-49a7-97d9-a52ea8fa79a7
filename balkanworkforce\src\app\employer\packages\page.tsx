"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { collection, query, where, getDocs, orderBy } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { cvPackages } from "@/data/cvPackages";
import { EmployerSubscription } from "@/types";

export default function EmployerPackagesPage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [activeSubscriptions, setActiveSubscriptions] = useState<EmployerSubscription[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedTab, setSelectedTab] = useState<'cv' | 'combined'>('cv');

  useEffect(() => {
    // If not authenticated or not an employer, redirect to auth page
    if (!authLoading && (!user || user.role !== "employer")) {
      router.push("/auth");
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    const fetchSubscriptions = async () => {
      if (!user) return;
      
      try {
        const subscriptionsCollection = collection(db, "subscriptions");
        const subscriptionsQuery = query(
          subscriptionsCollection,
          where("employerId", "==", user.uid),
          where("isActive", "==", true),
          orderBy("createdAt", "desc")
        );
        
        const querySnapshot = await getDocs(subscriptionsQuery);
        const subscriptionsList: EmployerSubscription[] = [];
        
        querySnapshot.forEach((doc) => {
          const data = doc.data();
          subscriptionsList.push({
            id: doc.id,
            ...data,
            startDate: data.startDate.toDate(),
            endDate: data.endDate ? data.endDate.toDate() : undefined,
            createdAt: data.createdAt.toDate(),
            updatedAt: data.updatedAt.toDate(),
          } as EmployerSubscription);
        });
        
        setActiveSubscriptions(subscriptionsList);
      } catch (error) {
        console.error("Error fetching subscriptions:", error);
      } finally {
        setLoading(false);
      }
    };

    if (user && user.role === "employer") {
      fetchSubscriptions();
    }
  }, [user]);

  // Filter packages based on selected tab
  const filteredPackages = cvPackages.filter(pkg => pkg.type === selectedTab);

  // Calculate total remaining invites
  const totalRemainingInvites = activeSubscriptions.reduce((total, sub) => {
    if (sub.packageType === 'cv' || sub.packageType === 'combined') {
      return total + sub.invitesRemaining;
    }
    return total;
  }, 0);

  const handlePurchase = (packageId: string) => {
    // For now, just show a message since payment integration is not implemented yet
    alert("Payment integration will be implemented later. This package would be purchased: " + packageId);
    
    // Later, this will redirect to a payment page
    // router.push(`/employer/packages/purchase/${packageId}`);
  };

  if (authLoading || loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto py-12">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">CV Packages</h1>
        <Link
          href="/employer/dashboard"
          className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
        >
          Back to Dashboard
        </Link>
      </div>
      
      {/* Current Subscription Status */}
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4">Your Subscription</h2>
        <div className="flex flex-wrap gap-6">
          <div>
            <div className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">
              Remaining Invites
            </div>
            <div className="text-3xl font-bold">
              {totalRemainingInvites}
            </div>
          </div>
          
          <div>
            <div className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">
              Active Packages
            </div>
            <div className="text-3xl font-bold">
              {activeSubscriptions.length}
            </div>
          </div>
        </div>
        
        {activeSubscriptions.length > 0 && (
          <div className="mt-6">
            <h3 className="text-lg font-medium mb-3">Active Packages</h3>
            <div className="space-y-3">
              {activeSubscriptions.map((sub) => {
                const packageDetails = cvPackages.find(pkg => pkg.id === sub.packageId);
                return (
                  <div 
                    key={sub.id}
                    className="border border-neutral-200 dark:border-neutral-700 rounded-lg p-4"
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h4 className="font-medium">{packageDetails?.name || "Package"}</h4>
                        <p className="text-sm text-neutral-600 dark:text-neutral-400">
                          {sub.packageType === 'job' 
                            ? `Expires on ${sub.endDate?.toLocaleDateString()}` 
                            : `${sub.invitesRemaining} of ${sub.invitesTotal} invites remaining`}
                        </p>
                      </div>
                      {(sub.packageType === 'cv' || sub.packageType === 'combined') && (
                        <div className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 px-3 py-1 rounded-full text-sm">
                          {sub.invitesRemaining} invites
                        </div>
                      )}
                    </div>
                  </div>
                );
              })}
            </div>
          </div>
        )}
      </div>
      
      {/* Package Selection Tabs */}
      <div className="flex border-b border-neutral-200 dark:border-neutral-700 mb-6">
        <button
          className={`py-3 px-6 font-medium ${
            selectedTab === 'cv'
              ? 'border-b-2 border-neutral-900 dark:border-white text-neutral-900 dark:text-white'
              : 'text-neutral-500 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white'
          }`}
          onClick={() => setSelectedTab('cv')}
        >
          CV Packages
        </button>
        <button
          className={`py-3 px-6 font-medium ${
            selectedTab === 'combined'
              ? 'border-b-2 border-neutral-900 dark:border-white text-neutral-900 dark:text-white'
              : 'text-neutral-500 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white'
          }`}
          onClick={() => setSelectedTab('combined')}
        >
          Combined Packages
        </button>
      </div>
      
      {/* Packages Grid */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
        {filteredPackages.map((pkg) => (
          <div
            key={pkg.id}
            className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 overflow-hidden"
          >
            <div className="p-6 border-b border-neutral-200 dark:border-neutral-700">
              <h3 className="text-xl font-semibold mb-1">{pkg.name}</h3>
              <div className="text-2xl font-bold mb-2">
                {pkg.price} {pkg.currency}
              </div>
              <p className="text-neutral-600 dark:text-neutral-400 text-sm">
                {pkg.description}
              </p>
            </div>
            <div className="p-6">
              <ul className="space-y-3 mb-6">
                <li className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>{pkg.inviteCount} candidate invites</span>
                </li>
                {pkg.type === 'combined' && (
                  <>
                    <li className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span>{pkg.jobDuration}-day job listing</span>
                    </li>
                    <li className="flex items-center">
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                      </svg>
                      <span>Premium job visibility</span>
                    </li>
                  </>
                )}
                <li className="flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                  </svg>
                  <span>No expiration on invites</span>
                </li>
              </ul>
              <button
                onClick={() => handlePurchase(pkg.id)}
                className="w-full bg-neutral-900 hover:bg-neutral-800 text-white py-3 rounded-md font-medium"
              >
                Purchase
              </button>
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}
