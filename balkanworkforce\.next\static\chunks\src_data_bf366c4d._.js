(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/data/positions.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_data_positions_ts_55af3cd5._.js",
  "static/chunks/src_data_positions_ts_760a8ac7._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/data/positions.ts [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/data/studyFields.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_data_studyFields_ts_0806f7a4._.js",
  "static/chunks/src_data_studyFields_ts_760a8ac7._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/data/studyFields.ts [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/data/driverLicenses.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/data/driverLicenses.ts [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/data/jobBenefits.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/src/data/jobBenefits.ts [app-client] (ecmascript)");
    });
});
}}),
"[project]/src/data/countries.ts [app-client] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "static/chunks/src_data_countries_ts_1ad27834._.js",
  "static/chunks/src_data_countries_ts_760a8ac7._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/src/data/countries.ts [app-client] (ecmascript)");
    });
});
}}),
}]);