import { Education } from "@/types";
import { degrees } from "@/data/degrees";
import { studyFields } from "@/data/studyFields";
import { countries } from "@/data/countries";

interface EducationDisplayProps {
  educations: Education[];
}

export default function EducationDisplay({ educations }: EducationDisplayProps) {
  if (!educations || !Array.isArray(educations) || educations.length === 0) {
    return (
      <div className="text-neutral-600 dark:text-neutral-400 italic">
        No education provided
      </div>
    );
  }

  // Sort educations by date (most recent first)
  const sortedEducations = Array.isArray(educations) ? [...educations].sort((a, b) => {
    const dateA = new Date(a.endDate!);
    const dateB = new Date(b.endDate!);
    return dateB.getTime() - dateA.getTime();
  }) : [];

  return (
    <div className="space-y-6">
      {sortedEducations.map((education, index) => {
        const startDate = new Date(education.startDate);
        const endDate = new Date(education.endDate!);

        const formattedStartDate = `${startDate.toLocaleString('default', { month: 'short' })} ${startDate.getFullYear()}`;
        const formattedEndDate = `${endDate.toLocaleString('default', { month: 'short' })} ${endDate.getFullYear()}`;

        const degree = education.degreeId
          ? degrees.find(d => d.id === education.degreeId)
          : null;

        const field = education.fieldId
          ? studyFields.find(f => f.id === education.fieldId)
          : null;

        const country = education.country
          ? countries.find(c => c.id === education.country)
          : null;

        return (
          <div key={index} className="border-l-2 border-neutral-300 dark:border-neutral-700 pl-4 pb-6 relative">
            <div className="absolute w-3 h-3 bg-neutral-300 dark:bg-neutral-700 rounded-full -left-[7px] top-1"></div>

            <div className="flex flex-col md:flex-row md:justify-between md:items-start">
              <div>
                <h4 className="font-medium">{education.degree}</h4>
                <div className="text-neutral-600 dark:text-neutral-400">{education.field}</div>
              </div>
              <div className="text-sm text-neutral-500 dark:text-neutral-400 mt-1 md:mt-0 md:text-right">
                {formattedStartDate} - {formattedEndDate}
              </div>
            </div>

            {(degree || field || country) && (
              <div className="flex flex-wrap gap-2 mt-2">
                {degree && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300">
                    {degree.translations.en}
                  </span>
                )}

                {field && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300">
                    {field.translations.en}
                  </span>
                )}

                {country && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300">
                    {country.name}
                  </span>
                )}
              </div>
            )}

            {education.description && (
              <div className="mt-2 text-sm text-neutral-600 dark:text-neutral-400">
                {education.description}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}
