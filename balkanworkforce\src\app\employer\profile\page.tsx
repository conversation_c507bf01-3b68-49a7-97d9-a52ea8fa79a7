"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { doc, getDoc, setDoc } from "firebase/firestore";
import { db, storage } from "@/lib/firebase";
import { ref, uploadBytes, getDownloadURL } from "firebase/storage";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import Image from "next/image";
import Link from "next/link";

// Form validation schema
const employerProfileSchema = z.object({
  companyName: z.string().min(2, "Company name is required"),
  companyDescription: z.string().min(10, "Company description is required"),
  industry: z.string().min(2, "Industry is required"),
  location: z.string().min(2, "Location is required"),
  website: z.string().url("Please enter a valid URL").or(z.string().length(0)),
  contactPerson: z.string().min(2, "Contact person name is required"),
  contactEmail: z.string().email("Please enter a valid email"),
  contactPhone: z.string().optional(),
  preferences: z.string().optional(),
});

type EmployerProfileFormValues = z.infer<typeof employerProfileSchema>;

export default function EmployerProfilePage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profileData, setProfileData] = useState<any>(null);
  const [companyLogo, setCompanyLogo] = useState<File | null>(null);
  const [companyLogoUrl, setCompanyLogoUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<EmployerProfileFormValues>({
    resolver: zodResolver(employerProfileSchema),
    defaultValues: {
      companyName: "",
      companyDescription: "",
      industry: "",
      location: "",
      website: "",
      contactPerson: "",
      contactEmail: "",
      contactPhone: "",
      preferences: "",
    },
  });

  useEffect(() => {
    // If not authenticated or not an employer, redirect to auth page
    if (!authLoading && (!user || user.role !== "employer")) {
      router.push("/auth");
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    const fetchEmployerProfile = async () => {
      if (!user) return;

      try {
        const employerDoc = await getDoc(doc(db, "employers", user.uid));
        
        if (employerDoc.exists()) {
          const data = employerDoc.data();
          setProfileData(data);
          
          // Set form values
          setValue("companyName", data.companyName || "");
          setValue("companyDescription", data.companyDescription || "");
          setValue("industry", data.industry || "");
          setValue("location", data.location || "");
          setValue("website", data.website || "");
          setValue("contactPerson", data.contactPerson || "");
          setValue("contactEmail", data.contactEmail || "");
          setValue("contactPhone", data.contactPhone || "");
          setValue("preferences", data.preferences?.join(", ") || "");
          
          // Set company logo URL if exists
          if (data.logoURL) {
            setCompanyLogoUrl(data.logoURL);
          }
        }
      } catch (error) {
        console.error("Error fetching employer profile:", error);
      } finally {
        setLoading(false);
      }
    };

    if (user && user.role === "employer") {
      fetchEmployerProfile();
    }
  }, [user, setValue]);

  const handleCompanyLogoChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    if (e.target.files && e.target.files[0]) {
      const file = e.target.files[0];
      setCompanyLogo(file);
      
      // Create a preview URL
      const reader = new FileReader();
      reader.onload = () => {
        setCompanyLogoUrl(reader.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const uploadCompanyLogo = async (): Promise<string | null> => {
    if (!companyLogo || !user) return null;
    
    try {
      const storageRef = ref(storage, `company-logos/${user.uid}`);
      await uploadBytes(storageRef, companyLogo);
      const downloadURL = await getDownloadURL(storageRef);
      return downloadURL;
    } catch (error) {
      console.error("Error uploading company logo:", error);
      throw error;
    }
  };

  const onSubmit = async (data: EmployerProfileFormValues) => {
    if (!user) return;
    
    setSaving(true);
    setError(null);
    setSuccess(false);
    
    try {
      // Process form data
      const employerData = {
        uid: user.uid,
        email: user.email,
        companyName: data.companyName,
        companyDescription: data.companyDescription,
        industry: data.industry,
        location: data.location,
        website: data.website || null,
        contactPerson: data.contactPerson,
        contactEmail: data.contactEmail,
        contactPhone: data.contactPhone || null,
        preferences: data.preferences ? data.preferences.split(",").map(pref => pref.trim()).filter(Boolean) : [],
        updatedAt: new Date(),
      };
      
      // Upload company logo if changed
      let logoURL = profileData?.logoURL || null;
      if (companyLogo) {
        logoURL = await uploadCompanyLogo();
      }
      
      // Save to Firestore
      await setDoc(
        doc(db, "employers", user.uid),
        {
          ...employerData,
          logoURL,
          ...(profileData ? {} : { createdAt: new Date() }),
        },
        { merge: true }
      );
      
      setSuccess(true);
      setProfileData({
        ...employerData,
        logoURL,
        createdAt: profileData?.createdAt || new Date(),
      });
      
      // Clear company logo state
      setCompanyLogo(null);
      
      // Scroll to top to show success message
      window.scrollTo(0, 0);
    } catch (error: any) {
      console.error("Error saving employer profile:", error);
      setError(error.message || "Failed to save profile. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto py-12">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Employer Profile</h1>
        <Link
          href="/employer/dashboard"
          className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
        >
          Back to Dashboard
        </Link>
      </div>
      
      {error && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded-md">
          {error}
        </div>
      )}
      
      {success && (
        <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 rounded-md">
          Profile saved successfully!
        </div>
      )}
      
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8">
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-2">Company Profile</h2>
          <p className="text-neutral-600 dark:text-neutral-400 mb-4">
            This information will be visible to candidates and used to match you with suitable applicants.
          </p>
          
          <div className="bg-neutral-50 dark:bg-neutral-900 p-4 rounded-md flex items-center">
            {companyLogoUrl ? (
              <div className="w-16 h-16 rounded-md overflow-hidden mr-4">
                <Image
                  src={companyLogoUrl}
                  alt="Company Logo"
                  width={64}
                  height={64}
                  className="w-full h-full object-contain"
                />
              </div>
            ) : (
              <div className="w-16 h-16 bg-neutral-200 dark:bg-neutral-700 rounded-md flex items-center justify-center mr-4">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8 text-neutral-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
            )}
            <div>
              <div className="text-xl font-medium">
                {profileData?.companyName || "Your Company Name"}
              </div>
              <div className="text-sm text-neutral-500 dark:text-neutral-400">
                {profileData?.industry || "Industry"} • {profileData?.location || "Location"}
              </div>
            </div>
          </div>
        </div>
        
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="mb-6">
            <h2 className="text-xl font-semibold mb-4">Company Logo</h2>
            <div className="flex items-center">
              {companyLogoUrl ? (
                <div className="w-24 h-24 rounded-md overflow-hidden mr-4 bg-white flex items-center justify-center">
                  <Image
                    src={companyLogoUrl}
                    alt="Company Logo"
                    width={96}
                    height={96}
                    className="max-w-full max-h-full object-contain"
                  />
                </div>
              ) : (
                <div className="w-24 h-24 bg-neutral-200 dark:bg-neutral-700 rounded-md flex items-center justify-center mr-4">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-neutral-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                  </svg>
                </div>
              )}
              <div>
                <label className="block mb-2 text-sm font-medium">
                  Upload a company logo (optional)
                </label>
                <input
                  type="file"
                  accept="image/*"
                  onChange={handleCompanyLogoChange}
                  className="block w-full text-sm text-neutral-900 dark:text-white
                    file:mr-4 file:py-2 file:px-4
                    file:rounded-md file:border-0
                    file:text-sm file:font-medium
                    file:bg-neutral-100 file:text-neutral-700
                    dark:file:bg-neutral-700 dark:file:text-neutral-100
                    hover:file:bg-neutral-200 dark:hover:file:bg-neutral-600"
                />
              </div>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label htmlFor="companyName" className="block text-sm font-medium mb-1">
                Company Name <span className="text-red-500">*</span>
              </label>
              <input
                id="companyName"
                {...register("companyName")}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                placeholder="Your company name"
              />
              {errors.companyName && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.companyName.message}</p>
              )}
            </div>
            
            <div>
              <label htmlFor="industry" className="block text-sm font-medium mb-1">
                Industry <span className="text-red-500">*</span>
              </label>
              <input
                id="industry"
                {...register("industry")}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                placeholder="e.g., Construction, IT, Manufacturing"
              />
              {errors.industry && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.industry.message}</p>
              )}
            </div>
          </div>
          
          <div className="mb-6">
            <label htmlFor="companyDescription" className="block text-sm font-medium mb-1">
              Company Description <span className="text-red-500">*</span>
            </label>
            <textarea
              id="companyDescription"
              {...register("companyDescription")}
              rows={4}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              placeholder="Describe your company, its mission, and what you do"
            ></textarea>
            {errors.companyDescription && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.companyDescription.message}</p>
            )}
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label htmlFor="location" className="block text-sm font-medium mb-1">
                Location <span className="text-red-500">*</span>
              </label>
              <input
                id="location"
                {...register("location")}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                placeholder="e.g., Berlin, Germany"
              />
              {errors.location && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.location.message}</p>
              )}
            </div>
            
            <div>
              <label htmlFor="website" className="block text-sm font-medium mb-1">
                Website
              </label>
              <input
                id="website"
                {...register("website")}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                placeholder="https://www.yourcompany.com"
              />
              {errors.website && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.website.message}</p>
              )}
            </div>
          </div>
          
          <div className="mb-8">
            <h2 className="text-xl font-semibold mb-4">Contact Information</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              <div>
                <label htmlFor="contactPerson" className="block text-sm font-medium mb-1">
                  Contact Person <span className="text-red-500">*</span>
                </label>
                <input
                  id="contactPerson"
                  {...register("contactPerson")}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                  placeholder="Full name of contact person"
                />
                {errors.contactPerson && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.contactPerson.message}</p>
                )}
              </div>
              
              <div>
                <label htmlFor="contactEmail" className="block text-sm font-medium mb-1">
                  Contact Email <span className="text-red-500">*</span>
                </label>
                <input
                  id="contactEmail"
                  type="email"
                  {...register("contactEmail")}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                  placeholder="<EMAIL>"
                />
                {errors.contactEmail && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.contactEmail.message}</p>
                )}
              </div>
            </div>
            
            <div>
              <label htmlFor="contactPhone" className="block text-sm font-medium mb-1">
                Contact Phone
              </label>
              <input
                id="contactPhone"
                {...register("contactPhone")}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                placeholder="+49 123 456 789"
              />
            </div>
          </div>
          
          <div className="mb-8">
            <label htmlFor="preferences" className="block text-sm font-medium mb-1">
              Candidate Preferences
            </label>
            <input
              id="preferences"
              {...register("preferences")}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              placeholder="e.g., German Language, 3+ Years Experience, Team Player"
            />
            <p className="mt-1 text-xs text-neutral-500 dark:text-neutral-400">
              Separate preferences with commas. These will be used to match you with suitable candidates.
            </p>
          </div>
          
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={saving}
              className={`px-6 py-3 rounded-md font-medium ${
                saving
                  ? "bg-neutral-700 text-white cursor-wait"
                  : "bg-neutral-900 hover:bg-neutral-800 text-white"
              }`}
            >
              {saving ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : (
                "Save Profile"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
