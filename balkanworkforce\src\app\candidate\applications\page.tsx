"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { collection, query, where, getDocs, orderBy } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { JobApplication } from "@/types";

export default function CandidateApplicationsPage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [applications, setApplications] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // If not authenticated or not a candidate, redirect to auth page
    if (!authLoading && (!user || user.role !== "candidate")) {
      router.push("/auth");
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    const fetchApplications = async () => {
      if (!user) return;
      
      try {
        const applicationsCollection = collection(db, "applications");
        const applicationsQuery = query(
          applicationsCollection,
          where("candidateId", "==", user.uid),
          orderBy("createdAt", "desc")
        );
        
        const querySnapshot = await getDocs(applicationsQuery);
        const applicationsList: any[] = [];
        
        querySnapshot.forEach((doc) => {
          const data = doc.data();
          applicationsList.push({
            id: doc.id,
            ...data,
            createdAt: data.createdAt.toDate(),
            updatedAt: data.updatedAt.toDate(),
            lastContactDate: data.lastContactDate ? data.lastContactDate.toDate() : null,
          });
        });
        
        setApplications(applicationsList);
      } catch (error) {
        console.error("Error fetching applications:", error);
      } finally {
        setLoading(false);
      }
    };

    if (user && user.role === "candidate") {
      fetchApplications();
    }
  }, [user]);

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <span className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 text-xs px-2 py-1 rounded-full">
            Pending
          </span>
        );
      case "reviewed":
        return (
          <span className="bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300 text-xs px-2 py-1 rounded-full">
            Reviewed
          </span>
        );
      case "contacted":
        return (
          <span className="bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300 text-xs px-2 py-1 rounded-full">
            Contacted
          </span>
        );
      case "rejected":
        return (
          <span className="bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300 text-xs px-2 py-1 rounded-full">
            Rejected
          </span>
        );
      case "hired":
        return (
          <span className="bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 text-xs px-2 py-1 rounded-full">
            Hired
          </span>
        );
      default:
        return (
          <span className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 text-xs px-2 py-1 rounded-full">
            {status}
          </span>
        );
    }
  };

  if (authLoading || loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto py-12">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">My Applications</h1>
        <Link
          href="/jobs"
          className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
        >
          Browse Jobs
        </Link>
      </div>
      
      {applications.length === 0 ? (
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8 text-center">
          <h2 className="text-xl font-medium mb-2">No Applications Yet</h2>
          <p className="text-neutral-600 dark:text-neutral-400 mb-6">
            You haven't applied to any jobs yet. Browse available jobs and submit your first application.
          </p>
          <Link
            href="/jobs"
            className="bg-neutral-900 hover:bg-neutral-800 text-white px-4 py-2 rounded-md"
          >
            Find Jobs
          </Link>
        </div>
      ) : (
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-neutral-50 dark:bg-neutral-900">
                <tr>
                  <th className="text-left py-3 px-4">Job</th>
                  <th className="text-left py-3 px-4">Status</th>
                  <th className="text-left py-3 px-4">Applied On</th>
                  <th className="text-left py-3 px-4">Last Update</th>
                  <th className="text-left py-3 px-4">Match</th>
                  <th className="text-left py-3 px-4">Actions</th>
                </tr>
              </thead>
              <tbody>
                {applications.map((application) => (
                  <tr
                    key={application.id}
                    className="border-b border-neutral-200 dark:border-neutral-700"
                  >
                    <td className="py-3 px-4">
                      <div>
                        <div className="font-medium">{application.jobTitle}</div>
                        <div className="text-sm text-neutral-500 dark:text-neutral-400">
                          {application.employerCompanyName || "Company"}
                        </div>
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      {getStatusBadge(application.status)}
                    </td>
                    <td className="py-3 px-4">
                      {application.createdAt.toLocaleDateString()}
                    </td>
                    <td className="py-3 px-4">
                      {application.updatedAt.toLocaleDateString()}
                    </td>
                    <td className="py-3 px-4">
                      {application.matchPercentage ? (
                        <div className="flex items-center">
                          <div className="w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2 mr-2 w-16">
                            <div
                              className={`h-2 rounded-full ${
                                application.matchPercentage >= 80 ? "bg-green-500" :
                                application.matchPercentage >= 50 ? "bg-yellow-500" :
                                "bg-red-500"
                              }`}
                              style={{ width: `${application.matchPercentage}%` }}
                            ></div>
                          </div>
                          <span>{application.matchPercentage}%</span>
                        </div>
                      ) : (
                        "N/A"
                      )}
                    </td>
                    <td className="py-3 px-4">
                      <Link
                        href={`/jobs/${application.jobId}`}
                        className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mr-4"
                      >
                        View Job
                      </Link>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
