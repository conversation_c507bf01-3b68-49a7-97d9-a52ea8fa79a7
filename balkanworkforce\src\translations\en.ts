export const en = {
  // Common
  "app.name": "BalkanWorkForce",
  "app.tagline": "Connecting EU employers with Balkan talent",

  // Navigation
  "nav.title": "Quick Links",
  "nav.home": "Home",
  "nav.jobs": "Jobs",
  "nav.candidates": "Candidates",
  "nav.about": "About",
  "nav.contact": "Contact",
  "nav.login": "Login",
  "nav.register": "Register",
  "nav.dashboard": "Dashboard",
  "nav.profile": "Profile",
  "nav.applications": "Applications",
  "nav.invitations": "Invitations",
  "nav.messages": "Messages",
  "nav.settings": "Settings",
  "nav.logout": "Logout",

  // Auth
  "auth.login": "Login",
  "auth.register": "Register",
  "auth.email": "Email",
  "auth.password": "Password",
  "auth.forgotPassword": "Forgot password?",
  "auth.noAccount": "Don't have an account?",
  "auth.haveAccount": "Already have an account?",
  "auth.signUp": "Sign up",
  "auth.signIn": "Sign in",
  "auth.continueWithGoogle": "Continue with Google",
  "auth.continueWithEmail": "Continue with Email",
  "auth.selectRole": "Select your role",
  "auth.employer": "Employer",
  "auth.candidate": "Candidate",
  "auth.employerDescription": "I want to hire candidates",
  "auth.candidateDescription": "I'm looking for a job",

  // Home
  "home.hero.title": "Find the right talent from the Balkans",
  "home.hero.subtitle": "Connect with skilled professionals from Balkan countries",
  "home.hero.cta": "Get Started",
  "home.forEmployers": "For Employers",
  "home.forCandidates": "For Candidates",
  "home.howItWorks": "How It Works",
  "home.featuredJobs": "Featured Jobs",
  "home.viewAllJobs": "View All Jobs",

  // Jobs
  "jobs.title": "Jobs",
  "jobs.search": "Search jobs",
  "jobs.filter": "Filter",
  "jobs.location": "Location",
  "jobs.employmentType": "Employment Type",
  "jobs.salary": "Salary",
  "jobs.posted": "Posted",
  "jobs.applications": "Applications",
  "jobs.apply": "Apply",
  "jobs.applied": "Applied",
  "jobs.description": "Description",
  "jobs.requirements": "Requirements",
  "jobs.benefits": "Benefits",
  "jobs.similarJobs": "Similar Jobs",
  "jobs.noJobs": "No jobs found",

  // Job Types
  "jobType.fullTime": "Full-time",
  "jobType.partTime": "Part-time",
  "jobType.contract": "Contract",
  "jobType.temporary": "Temporary",
  "jobType.internship": "Internship",

  // Candidates
  "candidates.title": "Candidates",
  "candidates.search": "Search candidates",
  "candidates.filter": "Filter",
  "candidates.skills": "Skills",
  "candidates.experience": "Experience",
  "candidates.education": "Education",
  "candidates.languages": "Languages",
  "candidates.noCandidates": "No candidates found",
  "candidates.invite": "Invite",
  "candidates.view": "View Profile",

  // Profile
  "profile.title": "Profile",
  "profile.edit": "Edit Profile",
  "profile.save": "Save Changes",
  "profile.personalInfo": "Personal Information",
  "profile.profession": "Profession",
  "profile.age": "Age",
  "profile.gender": "Gender",
  "profile.country": "Country",
  "profile.preferredLanguage": "Preferred Language",
  "profile.skills": "Skills",
  "profile.workExperience": "Work Experience",
  "profile.education": "Education",
  "profile.languageSkills": "Language Skills",
  "profile.desiredPositions": "Desired Positions",
  "profile.preferences": "Preferences",
  "profile.benefits": "Job Preferences",
  "profile.noWorkExperience": "I have no work experience",

  // Work Experience
  "workExperience.add": "Add Work Experience",
  "workExperience.company": "Company",
  "workExperience.position": "Position",
  "workExperience.standardizedPosition": "Standardized Position",
  "workExperience.startDate": "Start Date",
  "workExperience.endDate": "End Date",
  "workExperience.current": "I currently work here",
  "workExperience.country": "Country",
  "workExperience.industry": "Industry",
  "workExperience.description": "Description",
  "workExperience.remove": "Remove",

  // Education
  "education.add": "Add Education",
  "education.institution": "Institution",
  "education.degree": "Degree",
  "education.standardizedDegree": "Standardized Degree",
  "education.field": "Field of Study",
  "education.startDate": "Start Date",
  "education.endDate": "End Date",
  "education.current": "I am currently studying here",
  "education.country": "Country",
  "education.description": "Description",
  "education.remove": "Remove",

  // Language Skills
  "languageSkills.german": "German Language Level",
  "languageSkills.english": "English Language Level",
  "languageSkills.other": "Other Languages",
  "languageSkills.add": "Add Another Language",
  "languageSkills.language": "Language",
  "languageSkills.level": "Level",
  "languageSkills.remove": "Remove",

  // Applications
  "applications.title": "My Applications",
  "applications.status": "Status",
  "applications.date": "Date Applied",
  "applications.noApplications": "No applications found",

  // Application Status
  "applicationStatus.pending": "Pending",
  "applicationStatus.reviewed": "Reviewed",
  "applicationStatus.contacted": "Contacted",
  "applicationStatus.rejected": "Rejected",
  "applicationStatus.hired": "Hired",

  // Invitations
  "invitations.title": "Job Invitations",
  "invitations.status": "Status",
  "invitations.date": "Date Received",
  "invitations.expires": "Expires",
  "invitations.noInvitations": "No invitations found",
  "invitations.accept": "Accept & Apply",
  "invitations.decline": "Decline",

  // Invitation Status
  "invitationStatus.pending": "Pending",
  "invitationStatus.accepted": "Accepted",
  "invitationStatus.declined": "Declined",
  "invitationStatus.expired": "Expired",

  // Messages
  "messages.title": "Messages",
  "messages.noMessages": "No messages found",
  "messages.send": "Send",
  "messages.reply": "Reply",

  // Employer Dashboard
  "employer.dashboard.title": "Employer Dashboard",
  "employer.dashboard.activeJobs": "Active Jobs",
  "employer.dashboard.applications": "Applications",
  "employer.dashboard.premiumJobs": "Premium Jobs",
  "employer.dashboard.candidateInvites": "Candidate Invites",
  "employer.dashboard.recentApplications": "Recent Applications",
  "employer.dashboard.viewAll": "View All",

  // Candidate Dashboard
  "candidate.dashboard.title": "Candidate Dashboard",
  "candidate.dashboard.applications": "Applications",
  "candidate.dashboard.invitations": "Job Invitations",
  "candidate.dashboard.profileViews": "Profile Views",
  "candidate.dashboard.matches": "Matches",
  "candidate.dashboard.recommendedJobs": "Recommended Jobs",
  "candidate.dashboard.viewAll": "View All",

  // Packages
  "packages.title": "CV Packages",
  "packages.subscription": "Your Subscription",
  "packages.remainingInvites": "Remaining Invites",
  "packages.activePackages": "Active Packages",
  "packages.cvPackages": "CV Packages",
  "packages.combinedPackages": "Combined Packages",
  "packages.purchase": "Purchase",

  // Premium
  "premium.title": "Promote Job",
  "premium.selectCountries": "Select Countries",
  "premium.selectDuration": "Select Duration",
  "premium.summary": "Summary",
  "premium.totalPrice": "Total Price",
  "premium.promoteJob": "Promote Job",

  // Settings
  "settings.language": "Language Settings",
  "settings.selectLanguage": "Select your preferred language",
  "settings.languageUpdated": "Language preference updated successfully!",
  "settings.languageDescription": "This will change the language of the interface across the entire platform.",

  // Footer
  "footer.rights": "All rights reserved",
  "footer.terms": "Terms of Service",
  "footer.privacy": "Privacy Policy",
  "footer.contact": "Contact Us",
};
