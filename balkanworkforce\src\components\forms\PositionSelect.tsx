import { useFirestoreData } from "@/hooks/useFirestoreData";
import { Position } from "@/types";
import { useLanguage } from "@/contexts/LanguageContext";

interface PositionSelectProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  required?: boolean;
}

export default function PositionSelect({
  value,
  onChange,
  placeholder = "Select position",
  className = "",
  required = false,
}: PositionSelectProps) {
  const { language } = useLanguage();
  const { data: positions, loading, error } = useFirestoreData<Position>("positions");

  if (loading) {
    return (
      <select className={`${className} opacity-50`} disabled>
        <option>Loading positions...</option>
      </select>
    );
  }

  if (error) {
    return (
      <select className={`${className} opacity-50`} disabled>
        <option>Error loading positions</option>
      </select>
    );
  }

  return (
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className={className}
      required={required}
    >
      <option value="">{placeholder}</option>
      {positions.map((position) => (
        <option key={position.id} value={position.id}>
          {position.translations[language] || position.translations.en || position.id}
        </option>
      ))}
    </select>
  );
}
