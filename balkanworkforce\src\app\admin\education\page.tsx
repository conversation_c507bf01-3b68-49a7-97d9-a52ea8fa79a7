"use client";

import { useState, useEffect } from "react";
import { collection, query, getDocs, orderBy, doc, updateDoc, deleteDoc, setDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { StudyField } from "@/types";

export default function AdminEducationPage() {
  const [studyFields, setStudyFields] = useState<StudyField[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [editingField, setEditingField] = useState<StudyField | null>(null);
  const [newField, setNewField] = useState<Partial<StudyField>>({
    id: "",
    translations: { en: "", de: "", bs: "" },
    category: "",
  });
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const supportedLanguages = [
    { code: "en", name: "English" },
    { code: "de", name: "German" },
    { code: "bs", name: "Bosnian" },
  ];

  const categories = [
    "engineering",
    "healthcare",
    "business",
    "education",
    "arts",
    "science",
    "vocational",
    "it",
    "law",
    "agriculture",
    "other",
  ];

  useEffect(() => {
    const fetchStudyFields = async () => {
      try {
        const fieldsQuery = query(
          collection(db, "studyFields"),
          orderBy("category", "asc")
        );
        const snapshot = await getDocs(fieldsQuery);
        const fieldsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        })) as StudyField[];
        setStudyFields(fieldsData);
      } catch (error) {
        console.error("Error fetching study fields:", error);
        setError("Failed to load study fields. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchStudyFields();
  }, []);

  const filteredFields = studyFields.filter(field => {
    const searchLower = searchTerm.toLowerCase();
    return (
      field.category.toLowerCase().includes(searchLower) ||
      Object.values(field.translations).some(translation => 
        translation.toLowerCase().includes(searchLower)
      )
    );
  });

  const handleEditField = (field: StudyField) => {
    setEditingField(field);
    setError(null);
  };

  const handleCancelEdit = () => {
    setEditingField(null);
    setError(null);
  };

  const handleUpdateField = async () => {
    if (!editingField) return;

    // Validate
    if (!editingField.category) {
      setError("Category is required");
      return;
    }

    // Check if at least one translation is provided
    const hasTranslation = Object.values(editingField.translations).some(t => t.trim() !== "");
    if (!hasTranslation) {
      setError("At least one translation is required");
      return;
    }

    try {
      await updateDoc(doc(db, "studyFields", editingField.id), {
        translations: editingField.translations,
        category: editingField.category,
        updatedAt: new Date(),
      });

      // Update local state
      setStudyFields(studyFields.map(f => 
        f.id === editingField.id ? editingField : f
      ));
      
      setEditingField(null);
      setSuccess("Study field updated successfully");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Error updating study field:", error);
      setError("Failed to update study field. Please try again.");
    }
  };

  const handleDeleteField = async (fieldId: string) => {
    if (!confirm("Are you sure you want to delete this study field? This action cannot be undone.")) {
      return;
    }

    try {
      await deleteDoc(doc(db, "studyFields", fieldId));
      
      // Update local state
      setStudyFields(studyFields.filter(f => f.id !== fieldId));
      setSuccess("Study field deleted successfully");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Error deleting study field:", error);
      setError("Failed to delete study field. Please try again.");
    }
  };

  const handleAddNewField = () => {
    setIsAddingNew(true);
    setNewField({
      id: "",
      translations: { en: "", de: "", bs: "" },
      category: "",
    });
    setError(null);
  };

  const handleCancelAdd = () => {
    setIsAddingNew(false);
    setError(null);
  };

  const handleCreateField = async () => {
    // Validate
    if (!newField.id || !newField.id.trim()) {
      setError("Field ID is required");
      return;
    }

    if (!newField.category) {
      setError("Category is required");
      return;
    }

    // Check if at least one translation is provided
    const hasTranslation = Object.values(newField.translations || {}).some(t => t && t.trim() !== "");
    if (!hasTranslation) {
      setError("At least one translation is required");
      return;
    }

    // Check if ID already exists
    if (studyFields.some(f => f.id === newField.id)) {
      setError("A study field with this ID already exists");
      return;
    }

    try {
      const fieldData: StudyField = {
        id: newField.id,
        translations: newField.translations || { en: "", de: "", bs: "" },
        category: newField.category,
      };

      await setDoc(doc(db, "studyFields", newField.id), {
        ...fieldData,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Update local state
      setStudyFields([...studyFields, fieldData]);
      setIsAddingNew(false);
      setSuccess("Study field created successfully");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Error creating study field:", error);
      setError("Failed to create study field. Please try again.");
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Education Fields Management</h1>
        <button
          onClick={handleAddNewField}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
        >
          Add New Field
        </button>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 p-4 rounded-md mb-6">
          {success}
        </div>
      )}

      {/* Search */}
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <label htmlFor="search" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Search Education Fields
            </label>
            <input
              type="text"
              id="search"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by name or category"
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
            />
          </div>
        </div>
      </div>

      {/* Add New Field Form */}
      {isAddingNew && (
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-lg font-medium mb-4">Add New Education Field</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                Field ID <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={newField.id || ""}
                onChange={(e) => setNewField({...newField, id: e.target.value})}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                placeholder="e.g., computer-science"
              />
              <p className="text-xs text-neutral-500 mt-1">
                Use lowercase letters, numbers, and hyphens only. This ID will be used in the database.
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                Category <span className="text-red-500">*</span>
              </label>
              <select
                value={newField.category || ""}
                onChange={(e) => setNewField({...newField, category: e.target.value})}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              >
                <option value="">Select a category</option>
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <h3 className="text-md font-medium mb-2">Translations</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {supportedLanguages.map(language => (
              <div key={language.code}>
                <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                  {language.name}
                </label>
                <input
                  type="text"
                  value={newField.translations?.[language.code] || ""}
                  onChange={(e) => {
                    const updatedTranslations = { 
                      ...newField.translations,
                      [language.code]: e.target.value 
                    };
                    setNewField({...newField, translations: updatedTranslations});
                  }}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                  placeholder={`${language.name} translation`}
                />
              </div>
            ))}
          </div>
          
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleCancelAdd}
              className="px-4 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-700"
            >
              Cancel
            </button>
            <button
              onClick={handleCreateField}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
            >
              Create Field
            </button>
          </div>
        </div>
      )}

      {/* Study Fields Table */}
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-neutral-200 dark:divide-neutral-700">
            <thead className="bg-neutral-50 dark:bg-neutral-900">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Category
                </th>
                {supportedLanguages.map(language => (
                  <th key={language.code} className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                    {language.name}
                  </th>
                ))}
                <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-neutral-800 divide-y divide-neutral-200 dark:divide-neutral-700">
              {filteredFields.map((field) => (
                <tr key={field.id}>
                  {editingField && editingField.id === field.id ? (
                    // Edit mode
                    <>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-neutral-900 dark:text-white">
                          {field.id}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <select
                          value={editingField.category}
                          onChange={(e) => setEditingField({...editingField, category: e.target.value})}
                          className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-sm"
                        >
                          {categories.map(category => (
                            <option key={category} value={category}>
                              {category.charAt(0).toUpperCase() + category.slice(1)}
                            </option>
                          ))}
                        </select>
                      </td>
                      {supportedLanguages.map(language => (
                        <td key={language.code} className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="text"
                            value={editingField.translations[language.code] || ""}
                            onChange={(e) => {
                              const updatedTranslations = { 
                                ...editingField.translations,
                                [language.code]: e.target.value 
                              };
                              setEditingField({...editingField, translations: updatedTranslations});
                            }}
                            className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-sm"
                          />
                        </td>
                      ))}
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={handleCancelEdit}
                          className="text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white mr-3"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={handleUpdateField}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
                        >
                          Save
                        </button>
                      </td>
                    </>
                  ) : (
                    // View mode
                    <>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-neutral-900 dark:text-white">
                          {field.id}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-neutral-500 dark:text-neutral-400">
                          {field.category.charAt(0).toUpperCase() + field.category.slice(1)}
                        </div>
                      </td>
                      {supportedLanguages.map(language => (
                        <td key={language.code} className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-neutral-500 dark:text-neutral-400">
                            {field.translations[language.code] || "-"}
                          </div>
                        </td>
                      ))}
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleEditField(field)}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-3"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDeleteField(field.id)}
                          className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                        >
                          Delete
                        </button>
                      </td>
                    </>
                  )}
                </tr>
              ))}
              {filteredFields.length === 0 && (
                <tr>
                  <td colSpan={5 + supportedLanguages.length} className="px-6 py-4 text-center text-sm text-neutral-500 dark:text-neutral-400">
                    No education fields found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
