import { useState } from "react";
import { doc, updateDoc, deleteDoc, writeBatch } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { useAuth } from "@/contexts/AuthContext";
import { logAdminActivity } from "./AdminActivityLog";

interface BulkOperationsProps {
  entityType: "jobs" | "applications" | "employers" | "candidates";
  selectedItems: string[];
  onComplete: () => void;
  availableActions: {
    id: string;
    label: string;
    action: "update" | "delete";
    field?: string;
    value?: any;
    confirmationMessage?: string;
    dangerLevel?: "low" | "medium" | "high";
  }[];
}

export default function BulkOperations({
  entityType,
  selectedItems,
  onComplete,
  availableActions,
}: BulkOperationsProps) {
  const { user } = useAuth();
  const [selectedAction, setSelectedAction] = useState<string>("");
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const handleActionChange = (actionId: string) => {
    setSelectedAction(actionId);
    setError(null);
  };

  const handleExecute = async () => {
    if (!selectedAction) {
      setError("Please select an action to perform");
      return;
    }

    if (selectedItems.length === 0) {
      setError("No items selected");
      return;
    }

    const action = availableActions.find(a => a.id === selectedAction);
    if (!action) {
      setError("Invalid action selected");
      return;
    }

    // Confirm dangerous actions
    if (action.dangerLevel === "high") {
      const confirmMessage = action.confirmationMessage || 
        `Are you sure you want to ${action.action} ${selectedItems.length} ${entityType}? This action cannot be undone.`;
      
      if (!confirm(confirmMessage)) {
        return;
      }
    }

    try {
      setLoading(true);
      setError(null);

      // Use batch for multiple operations
      const batch = writeBatch(db);
      
      if (action.action === "update") {
        // Update operation
        for (const itemId of selectedItems) {
          const itemRef = doc(db, entityType, itemId);
          if (action.field) {
            batch.update(itemRef, {
              [action.field]: action.value,
              updatedAt: new Date(),
            });
          }
        }
      } else if (action.action === "delete") {
        // Delete operation
        for (const itemId of selectedItems) {
          const itemRef = doc(db, entityType, itemId);
          batch.delete(itemRef);
        }
      }

      // Commit the batch
      await batch.commit();

      // Log the activity
      if (user) {
        await logAdminActivity(
          user.uid,
          user.email || "unknown",
          action.action === "update" ? "bulk-update" : "bulk-delete",
          entityType,
          undefined,
          `${action.action === "update" ? "Updated" : "Deleted"} ${selectedItems.length} ${entityType} with action: ${action.label}`
        );
      }

      setSuccess(`Successfully performed ${action.label} on ${selectedItems.length} ${entityType}`);
      setTimeout(() => {
        setSuccess(null);
        onComplete();
      }, 2000);
    } catch (error) {
      console.error(`Error performing bulk operation:`, error);
      setError(`Failed to perform operation. Please try again.`);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-4 mb-6">
      <div className="flex flex-col md:flex-row items-start md:items-center justify-between gap-4 mb-4">
        <div>
          <h3 className="text-md font-medium">Bulk Operations</h3>
          <p className="text-sm text-neutral-600 dark:text-neutral-400">
            {selectedItems.length} {entityType} selected
          </p>
        </div>
        
        <div className="flex flex-wrap gap-2">
          <select
            value={selectedAction}
            onChange={(e) => handleActionChange(e.target.value)}
            className="px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md bg-white dark:bg-neutral-800"
            disabled={loading || selectedItems.length === 0}
          >
            <option value="">Select an action</option>
            {availableActions.map(action => (
              <option key={action.id} value={action.id}>
                {action.label}
              </option>
            ))}
          </select>
          
          <button
            onClick={handleExecute}
            disabled={loading || !selectedAction || selectedItems.length === 0}
            className={`px-4 py-2 rounded-md ${
              loading
                ? "bg-neutral-300 dark:bg-neutral-700 cursor-not-allowed"
                : "bg-blue-600 hover:bg-blue-700 text-white"
            }`}
          >
            {loading ? "Processing..." : "Execute"}
          </button>
        </div>
      </div>
      
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-3 rounded-md text-sm">
          {error}
        </div>
      )}
      
      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 p-3 rounded-md text-sm">
          {success}
        </div>
      )}
    </div>
  );
}
