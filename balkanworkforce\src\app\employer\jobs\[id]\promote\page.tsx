"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { doc, getDoc, collection, addDoc, updateDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import Link from "next/link";
import Image from "next/image";
import { countries } from "@/data/countries";
import { premiumPackage } from "@/data/premiumPackages";
import { JobListing, JobPremiumSubscription } from "@/types";

export default function PromoteJobPage() {
  const { id } = useParams();
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [job, setJob] = useState<JobListing | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [selectedCountries, setSelectedCountries] = useState<string[]>([]);
  const [duration, setDuration] = useState<7 | 14 | 30>(7);
  const [submitting, setSubmitting] = useState(false);
  const [success, setSuccess] = useState(false);

  useEffect(() => {
    // If not authenticated or not an employer, redirect to auth page
    if (!authLoading && (!user || user.role !== "employer")) {
      router.push("/auth");
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    const fetchJob = async () => {
      if (!user || !id || typeof id !== "string") return;
      
      try {
        const jobDoc = await getDoc(doc(db, "jobs", id));
        
        if (!jobDoc.exists()) {
          setError("Job not found");
          return;
        }
        
        const jobData = jobDoc.data();
        
        // Check if this job belongs to the current employer
        if (jobData.employerId !== user.uid) {
          setError("You don't have permission to promote this job");
          return;
        }
        
        setJob({
          id: jobDoc.id,
          ...jobData,
          createdAt: jobData.createdAt.toDate(),
          updatedAt: jobData.updatedAt.toDate(),
          premiumUntil: jobData.premiumUntil ? jobData.premiumUntil.toDate() : undefined,
        } as JobListing);
      } catch (error) {
        console.error("Error fetching job:", error);
        setError("Failed to load job details. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    if (user && user.role === "employer") {
      fetchJob();
    }
  }, [user, id, router]);

  const toggleCountry = (countryId: string) => {
    setSelectedCountries(prev => {
      if (prev.includes(countryId)) {
        return prev.filter(id => id !== countryId);
      } else {
        return [...prev, countryId];
      }
    });
  };

  const calculateTotalPrice = () => {
    return selectedCountries.length * premiumPackage.pricePerCountry[duration];
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    
    if (!user || !job) return;
    
    if (selectedCountries.length === 0) {
      alert("Please select at least one country");
      return;
    }
    
    setSubmitting(true);
    
    try {
      // Calculate total price
      const totalPrice = calculateTotalPrice();
      
      // Calculate end date
      const startDate = new Date();
      const endDate = new Date();
      endDate.setDate(endDate.getDate() + duration);
      
      // Create premium subscription
      const subscriptionData: Omit<JobPremiumSubscription, "id"> = {
        jobId: job.id,
        employerId: user.uid,
        duration,
        countries: selectedCountries,
        startDate,
        endDate,
        totalPrice,
        currency: premiumPackage.currency,
        isActive: true,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      
      // Add to Firestore
      const subscriptionRef = await addDoc(collection(db, "jobPremiumSubscriptions"), subscriptionData);
      
      // Update job with premium status
      await updateDoc(doc(db, "jobs", job.id), {
        isPremium: true,
        premiumUntil: endDate,
        premiumCountries: selectedCountries,
        updatedAt: new Date()
      });
      
      setSuccess(true);
      
      // Redirect after a short delay
      setTimeout(() => {
        router.push(`/employer/jobs/${job.id}`);
      }, 2000);
    } catch (error) {
      console.error("Error promoting job:", error);
      setError("Failed to promote job. Please try again.");
    } finally {
      setSubmitting(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto py-12">
        <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6">
          {error}
        </div>
        <Link
          href={`/employer/jobs/${id}`}
          className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
        >
          Back to Job Details
        </Link>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="max-w-4xl mx-auto py-12">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Job Not Found</h2>
          <p className="text-neutral-600 dark:text-neutral-400 mb-6">
            The job you're looking for doesn't exist or has been removed.
          </p>
          <Link
            href="/employer/dashboard"
            className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
          >
            Back to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  if (success) {
    return (
      <div className="max-w-4xl mx-auto py-12">
        <div className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 p-6 rounded-md mb-6 text-center">
          <h2 className="text-2xl font-bold mb-2">Job Promoted Successfully!</h2>
          <p className="mb-4">
            Your job listing has been successfully promoted and will appear at the top of search results in the selected countries.
          </p>
          <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-green-600 dark:border-green-400 mx-auto"></div>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto py-12">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Promote Job</h1>
        <Link
          href={`/employer/jobs/${id}`}
          className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
        >
          Back to Job Details
        </Link>
      </div>
      
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6 mb-8">
        <h2 className="text-xl font-semibold mb-2">{job.title}</h2>
        <div className="flex flex-wrap gap-2 mb-4">
          <span className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 text-xs px-2 py-1 rounded">
            {job.location}
          </span>
          <span className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 text-xs px-2 py-1 rounded">
            {job.employmentType.replace("-", " ")}
          </span>
          {job.salary && (
            <span className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 text-xs px-2 py-1 rounded">
              {job.salary.min} - {job.salary.max} {job.salary.currency}
            </span>
          )}
        </div>
        
        {job.isPremium && job.premiumUntil && new Date() < job.premiumUntil && (
          <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4 mb-6">
            <h3 className="text-lg font-medium text-amber-800 dark:text-amber-400 mb-2">
              This Job Is Already Premium
            </h3>
            <p className="text-amber-700 dark:text-amber-500 mb-2">
              This job is already promoted until {job.premiumUntil.toLocaleDateString()}.
            </p>
            <p className="text-amber-700 dark:text-amber-500">
              You can extend the promotion period or add more countries.
            </p>
          </div>
        )}
      </div>
      
      <form onSubmit={handleSubmit}>
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6 mb-8">
          <h2 className="text-xl font-semibold mb-6">Select Countries</h2>
          <p className="text-neutral-600 dark:text-neutral-400 mb-6">
            Choose the countries where you want your job listing to be promoted. Your job will appear at the top of search results in these countries.
          </p>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">
            {countries.map((country) => (
              <div
                key={country.id}
                className={`p-4 rounded-lg border cursor-pointer transition-all ${
                  selectedCountries.includes(country.id)
                    ? "border-neutral-900 dark:border-white bg-neutral-50 dark:bg-neutral-700"
                    : "border-neutral-200 dark:border-neutral-700 hover:border-neutral-400 dark:hover:border-neutral-500"
                }`}
                onClick={() => toggleCountry(country.id)}
              >
                <div className="flex items-center">
                  <div className={`w-5 h-5 rounded-sm border flex items-center justify-center mr-3 ${
                    selectedCountries.includes(country.id)
                      ? "bg-neutral-900 dark:bg-white border-neutral-900 dark:border-white"
                      : "border-neutral-400 dark:border-neutral-500"
                  }`}>
                    {selectedCountries.includes(country.id) && (
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-white dark:text-neutral-900" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                    )}
                  </div>
                  <div className="flex items-center">
                    {country.flagUrl && (
                      <div className="w-6 h-4 mr-2 overflow-hidden rounded-sm">
                        <Image
                          src={country.flagUrl}
                          alt={country.name}
                          width={24}
                          height={16}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                    <span>{country.name}</span>
                  </div>
                </div>
              </div>
            ))}
          </div>
          
          {selectedCountries.length === 0 && (
            <p className="text-red-600 dark:text-red-400 mb-6">
              Please select at least one country.
            </p>
          )}
        </div>
        
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6 mb-8">
          <h2 className="text-xl font-semibold mb-6">Select Duration</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            <div
              className={`p-4 rounded-lg border cursor-pointer transition-all ${
                duration === 7
                  ? "border-neutral-900 dark:border-white bg-neutral-50 dark:bg-neutral-700"
                  : "border-neutral-200 dark:border-neutral-700 hover:border-neutral-400 dark:hover:border-neutral-500"
              }`}
              onClick={() => setDuration(7)}
            >
              <div className="flex items-center mb-2">
                <div className={`w-5 h-5 rounded-full border flex items-center justify-center mr-3 ${
                  duration === 7
                    ? "border-neutral-900 dark:border-white"
                    : "border-neutral-400 dark:border-neutral-500"
                }`}>
                  {duration === 7 && (
                    <div className="w-3 h-3 rounded-full bg-neutral-900 dark:bg-white"></div>
                  )}
                </div>
                <span className="font-medium">7 Days</span>
              </div>
              <div className="ml-8 text-sm text-neutral-600 dark:text-neutral-400">
                {premiumPackage.pricePerCountry[7]} {premiumPackage.currency} per country
              </div>
            </div>
            
            <div
              className={`p-4 rounded-lg border cursor-pointer transition-all ${
                duration === 14
                  ? "border-neutral-900 dark:border-white bg-neutral-50 dark:bg-neutral-700"
                  : "border-neutral-200 dark:border-neutral-700 hover:border-neutral-400 dark:hover:border-neutral-500"
              }`}
              onClick={() => setDuration(14)}
            >
              <div className="flex items-center mb-2">
                <div className={`w-5 h-5 rounded-full border flex items-center justify-center mr-3 ${
                  duration === 14
                    ? "border-neutral-900 dark:border-white"
                    : "border-neutral-400 dark:border-neutral-500"
                }`}>
                  {duration === 14 && (
                    <div className="w-3 h-3 rounded-full bg-neutral-900 dark:bg-white"></div>
                  )}
                </div>
                <span className="font-medium">14 Days</span>
              </div>
              <div className="ml-8 text-sm text-neutral-600 dark:text-neutral-400">
                {premiumPackage.pricePerCountry[14]} {premiumPackage.currency} per country
              </div>
            </div>
            
            <div
              className={`p-4 rounded-lg border cursor-pointer transition-all ${
                duration === 30
                  ? "border-neutral-900 dark:border-white bg-neutral-50 dark:bg-neutral-700"
                  : "border-neutral-200 dark:border-neutral-700 hover:border-neutral-400 dark:hover:border-neutral-500"
              }`}
              onClick={() => setDuration(30)}
            >
              <div className="flex items-center mb-2">
                <div className={`w-5 h-5 rounded-full border flex items-center justify-center mr-3 ${
                  duration === 30
                    ? "border-neutral-900 dark:border-white"
                    : "border-neutral-400 dark:border-neutral-500"
                }`}>
                  {duration === 30 && (
                    <div className="w-3 h-3 rounded-full bg-neutral-900 dark:bg-white"></div>
                  )}
                </div>
                <span className="font-medium">30 Days</span>
              </div>
              <div className="ml-8 text-sm text-neutral-600 dark:text-neutral-400">
                {premiumPackage.pricePerCountry[30]} {premiumPackage.currency} per country
              </div>
            </div>
          </div>
        </div>
        
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6 mb-8">
          <h2 className="text-xl font-semibold mb-6">Summary</h2>
          
          <div className="space-y-4 mb-6">
            <div className="flex justify-between">
              <span>Selected Countries:</span>
              <span>{selectedCountries.length}</span>
            </div>
            <div className="flex justify-between">
              <span>Duration:</span>
              <span>{duration} days</span>
            </div>
            <div className="flex justify-between">
              <span>Price per Country:</span>
              <span>{premiumPackage.pricePerCountry[duration]} {premiumPackage.currency}</span>
            </div>
            <div className="border-t border-neutral-200 dark:border-neutral-700 pt-4 flex justify-between font-bold">
              <span>Total Price:</span>
              <span>{calculateTotalPrice()} {premiumPackage.currency}</span>
            </div>
          </div>
          
          <div className="flex justify-end">
            <button
              type="submit"
              disabled={submitting || selectedCountries.length === 0}
              className={`px-6 py-3 rounded-md font-medium ${
                submitting || selectedCountries.length === 0
                  ? "bg-neutral-700 text-white cursor-not-allowed"
                  : "bg-neutral-900 hover:bg-neutral-800 text-white"
              }`}
            >
              {submitting ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Processing...
                </>
              ) : (
                "Promote Job"
              )}
            </button>
          </div>
        </div>
      </form>
    </div>
  );
}
