import { useState, useEffect } from "react";
import { LanguageLevel } from "@/types";
import { countries } from "@/data/countries";
import { positions } from "@/data/positions";
import { jobBenefits } from "@/data/jobBenefits";
import { degrees } from "@/data/degrees";
import { studyFields } from "@/data/studyFields";
import { driverLicenses, driverLicensesByCategory } from "@/data/driverLicenses";
import Image from "next/image";

export interface CandidateFilters {
  profession: string;
  countries: string[];
  germanLanguageLevel?: LanguageLevel;
  englishLanguageLevel?: LanguageLevel;
  positions: string[];
  benefits: string[];
  educationLevels: string[];
  degrees: string[];
  studyFields: string[];
  driverLicense?: string;
}

interface CandidateFilterSidebarProps {
  filters: CandidateFilters;
  onChange: (filters: CandidateFilters) => void;
  isOpen: boolean;
  onClose: () => void;
}

export default function CandidateFilterSidebar({
  filters,
  onChange,
  isOpen,
  onClose,
}: CandidateFilterSidebarProps) {
  const [localFilters, setLocalFilters] = useState<CandidateFilters>(filters);

  useEffect(() => {
    setLocalFilters(filters);
  }, [filters]);

  const handleChange = (
    field: keyof CandidateFilters,
    value: any
  ) => {
    setLocalFilters((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleCountryToggle = (countryId: string) => {
    setLocalFilters((prev) => {
      const countries = [...prev.countries];
      const index = countries.indexOf(countryId);

      if (index === -1) {
        countries.push(countryId);
      } else {
        countries.splice(index, 1);
      }

      return {
        ...prev,
        countries,
      };
    });
  };

  const handlePositionToggle = (positionId: string) => {
    setLocalFilters((prev) => {
      const positions = [...prev.positions];
      const index = positions.indexOf(positionId);

      if (index === -1) {
        positions.push(positionId);
      } else {
        positions.splice(index, 1);
      }

      return {
        ...prev,
        positions,
      };
    });
  };

  const handleBenefitToggle = (benefitId: string) => {
    setLocalFilters((prev) => {
      const benefits = [...prev.benefits];
      const index = benefits.indexOf(benefitId);

      if (index === -1) {
        benefits.push(benefitId);
      } else {
        benefits.splice(index, 1);
      }

      return {
        ...prev,
        benefits,
      };
    });
  };

  const handleEducationLevelToggle = (level: string) => {
    setLocalFilters((prev) => {
      const educationLevels = [...prev.educationLevels];
      const index = educationLevels.indexOf(level);

      if (index === -1) {
        educationLevels.push(level);
      } else {
        educationLevels.splice(index, 1);
      }

      return {
        ...prev,
        educationLevels,
      };
    });
  };

  const handleDegreeToggle = (degreeId: string) => {
    setLocalFilters((prev) => {
      const degreesList = [...prev.degrees];
      const index = degreesList.indexOf(degreeId);

      if (index === -1) {
        degreesList.push(degreeId);
      } else {
        degreesList.splice(index, 1);
      }

      return {
        ...prev,
        degrees: degreesList,
      };
    });
  };

  const handleStudyFieldToggle = (fieldId: string) => {
    setLocalFilters((prev) => {
      const fieldsList = [...prev.studyFields];
      const index = fieldsList.indexOf(fieldId);

      if (index === -1) {
        fieldsList.push(fieldId);
      } else {
        fieldsList.splice(index, 1);
      }

      return {
        ...prev,
        studyFields: fieldsList,
      };
    });
  };

  const applyFilters = () => {
    onChange(localFilters);
    if (window.innerWidth < 768) {
      onClose();
    }
  };

  const handleDriverLicenseChange = (licenseId: string | undefined) => {
    setLocalFilters((prev) => ({
      ...prev,
      driverLicense: licenseId,
    }));
  };

  const resetFilters = () => {
    const emptyFilters: CandidateFilters = {
      profession: "",
      countries: [],
      germanLanguageLevel: undefined,
      englishLanguageLevel: undefined,
      positions: [],
      benefits: [],
      educationLevels: [],
      degrees: [],
      studyFields: [],
      driverLicense: undefined,
    };
    setLocalFilters(emptyFilters);
    onChange(emptyFilters);
  };

  // Filter to only show the 4 countries we're interested in
  const allowedCountries = countries.filter(country =>
    ['de', 'at', 'hr', 'si'].includes(country.id)
  );

  // Group positions by category
  const positionsByCategory: Record<string, typeof positions> = {};
  positions.forEach(position => {
    if (!positionsByCategory[position.category]) {
      positionsByCategory[position.category] = [];
    }
    positionsByCategory[position.category].push(position);
  });

  // Group benefits by category (assuming benefits have categories)
  const accommodationBenefits = jobBenefits.filter(benefit =>
    benefit.id.startsWith('accommodation_')
  );

  const otherBenefits = jobBenefits.filter(benefit =>
    !benefit.id.startsWith('accommodation_')
  );

  // Group degrees by education level
  const educationLevels = [
    { id: 'secondary', label: 'Secondary Education' },
    { id: 'vocational', label: 'Vocational Training' },
    { id: 'tertiary', label: 'Tertiary Education' },
    { id: 'bachelor', label: 'Bachelor Degree' },
    { id: 'master', label: 'Master Degree' },
    { id: 'doctorate', label: 'Doctorate' },
    { id: 'professional', label: 'Professional Degree' },
  ];

  // Group degrees by level
  const degreesByLevel: Record<string, typeof degrees> = {};
  degrees.forEach(degree => {
    if (!degreesByLevel[degree.level]) {
      degreesByLevel[degree.level] = [];
    }
    degreesByLevel[degree.level].push(degree);
  });

  // Group study fields by category
  const studyFieldsByCategory: Record<string, typeof studyFields> = {};
  studyFields.forEach(field => {
    if (!studyFieldsByCategory[field.category]) {
      studyFieldsByCategory[field.category] = [];
    }
    studyFieldsByCategory[field.category].push(field);
  });

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div
          className="md:hidden fixed inset-0 bg-black bg-opacity-50 z-40"
          onClick={onClose}
        ></div>
      )}

      {/* Sidebar */}
      <div
        className={`fixed md:sticky top-0 h-screen md:h-auto overflow-y-auto bg-white dark:bg-neutral-800 w-80 md:w-full border-r border-neutral-200 dark:border-neutral-700 p-4 transition-transform duration-300 ease-in-out z-50 md:z-auto ${
          isOpen ? "translate-x-0" : "-translate-x-full md:translate-x-0"
        }`}
      >
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-xl font-semibold">Filter Candidates</h2>
          <button
            onClick={onClose}
            className="md:hidden text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <div className="space-y-6">
          {/* Profession Filter */}
          <div>
            <label htmlFor="profession" className="block text-sm font-medium mb-1">
              Profession
            </label>
            <input
              type="text"
              id="profession"
              value={localFilters.profession}
              onChange={(e) => handleChange("profession", e.target.value)}
              placeholder="e.g., Electrician, Developer"
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
            />
          </div>

          {/* Country Filter */}
          <div>
            <h3 className="text-sm font-medium mb-2">Desired Countries</h3>
            <div className="grid grid-cols-2 gap-2">
              {allowedCountries.map((country) => (
                <div
                  key={country.id}
                  className={`flex items-center p-2 border rounded-md cursor-pointer ${
                    localFilters.countries.includes(country.id)
                      ? "border-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-700"
                      : "border-neutral-200 dark:border-neutral-700"
                  }`}
                  onClick={() => handleCountryToggle(country.id)}
                >
                  {country.flagUrl && (
                    <div className="w-5 h-3 mr-2 overflow-hidden rounded-sm">
                      <Image
                        src={country.flagUrl}
                        alt={country.name}
                        width={20}
                        height={12}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  <span className="text-sm">{country.name}</span>
                </div>
              ))}
            </div>
          </div>

          {/* Language Filters */}
          <div>
            <h3 className="text-sm font-medium mb-2">Language Skills</h3>
            <div className="space-y-3">
              <div>
                <label htmlFor="germanLanguageLevel" className="block text-xs text-neutral-500 dark:text-neutral-400 mb-1">
                  German Language Level (minimum)
                </label>
                <select
                  id="germanLanguageLevel"
                  value={localFilters.germanLanguageLevel || ""}
                  onChange={(e) => handleChange("germanLanguageLevel", e.target.value || undefined)}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-sm"
                >
                  <option value="">Any level</option>
                  <option value="A1">A1 (Beginner)</option>
                  <option value="A2">A2 (Elementary)</option>
                  <option value="B1">B1 (Intermediate)</option>
                  <option value="B2">B2 (Upper Intermediate)</option>
                  <option value="C1">C1 (Advanced)</option>
                  <option value="C2">C2 (Proficient)</option>
                  <option value="Native">Native</option>
                </select>
              </div>
              <div>
                <label htmlFor="englishLanguageLevel" className="block text-xs text-neutral-500 dark:text-neutral-400 mb-1">
                  English Language Level (minimum)
                </label>
                <select
                  id="englishLanguageLevel"
                  value={localFilters.englishLanguageLevel || ""}
                  onChange={(e) => handleChange("englishLanguageLevel", e.target.value || undefined)}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-sm"
                >
                  <option value="">Any level</option>
                  <option value="A1">A1 (Beginner)</option>
                  <option value="A2">A2 (Elementary)</option>
                  <option value="B1">B1 (Intermediate)</option>
                  <option value="B2">B2 (Upper Intermediate)</option>
                  <option value="C1">C1 (Advanced)</option>
                  <option value="C2">C2 (Proficient)</option>
                  <option value="Native">Native</option>
                </select>
              </div>
            </div>
          </div>

          {/* Position Filters */}
          <div>
            <h3 className="text-sm font-medium mb-2">Work Experience</h3>
            <div className="space-y-3 max-h-60 overflow-y-auto pr-2">
              {Object.entries(positionsByCategory).map(([category, categoryPositions]) => (
                <div key={category} className="border border-neutral-200 dark:border-neutral-700 rounded-md p-3">
                  <h4 className="text-xs font-medium text-neutral-500 dark:text-neutral-400 mb-2 uppercase">{category}</h4>
                  <div className="space-y-1">
                    {categoryPositions.map((position) => (
                      <div key={position.id} className="flex items-center">
                        <input
                          type="checkbox"
                          id={`position-${position.id}`}
                          checked={localFilters.positions.includes(position.id)}
                          onChange={() => handlePositionToggle(position.id)}
                          className="mr-2"
                        />
                        <label htmlFor={`position-${position.id}`} className="text-sm">
                          {position.translations.en}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Education Filters */}
          <div>
            <h3 className="text-sm font-medium mb-2">Education</h3>

            {/* Education Levels */}
            <div className="mb-3 border border-neutral-200 dark:border-neutral-700 rounded-md p-3">
              <h4 className="text-xs font-medium text-neutral-500 dark:text-neutral-400 mb-2 uppercase">Education Level</h4>
              <div className="space-y-1">
                {educationLevels.map((level) => (
                  <div key={level.id} className="flex items-center">
                    <input
                      type="checkbox"
                      id={`level-${level.id}`}
                      checked={localFilters.educationLevels.includes(level.id)}
                      onChange={() => handleEducationLevelToggle(level.id)}
                      className="mr-2"
                    />
                    <label htmlFor={`level-${level.id}`} className="text-sm">
                      {level.label}
                    </label>
                  </div>
                ))}
              </div>
            </div>

            {/* Specific Degrees */}
            <div className="max-h-60 overflow-y-auto pr-2 mb-6">
              <h4 className="text-xs font-medium text-neutral-500 dark:text-neutral-400 mb-2 uppercase">Specific Degrees</h4>
              {Object.entries(degreesByLevel).map(([level, levelDegrees]) => (
                <div key={level} className="border border-neutral-200 dark:border-neutral-700 rounded-md p-3 mb-3">
                  <h5 className="text-xs font-medium text-neutral-500 dark:text-neutral-400 mb-2 capitalize">
                    {educationLevels.find(l => l.id === level)?.label || level}
                  </h5>
                  <div className="space-y-1">
                    {levelDegrees.map((degree) => (
                      <div key={degree.id} className="flex items-center">
                        <input
                          type="checkbox"
                          id={`degree-${degree.id}`}
                          checked={localFilters.degrees.includes(degree.id)}
                          onChange={() => handleDegreeToggle(degree.id)}
                          className="mr-2"
                        />
                        <label htmlFor={`degree-${degree.id}`} className="text-sm">
                          {degree.translations.en}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>

            {/* Study Fields */}
            <div className="max-h-60 overflow-y-auto pr-2 mb-6">
              <h4 className="text-xs font-medium text-neutral-500 dark:text-neutral-400 mb-2 uppercase">Fields of Study</h4>
              {Object.entries(studyFieldsByCategory).map(([category, categoryFields]) => (
                <div key={category} className="border border-neutral-200 dark:border-neutral-700 rounded-md p-3 mb-3">
                  <h5 className="text-xs font-medium text-neutral-500 dark:text-neutral-400 mb-2 capitalize">
                    {category}
                  </h5>
                  <div className="space-y-1">
                    {categoryFields.map((field) => (
                      <div key={field.id} className="flex items-center">
                        <input
                          type="checkbox"
                          id={`field-${field.id}`}
                          checked={localFilters.studyFields.includes(field.id)}
                          onChange={() => handleStudyFieldToggle(field.id)}
                          className="mr-2"
                        />
                        <label htmlFor={`field-${field.id}`} className="text-sm">
                          {field.translations.en}
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </div>

          {/* Driver's License Filter */}
          <div>
            <h3 className="text-sm font-medium mb-2">Driver's License</h3>
            <div className="mb-3">
              <select
                id="driverLicense"
                value={localFilters.driverLicense || ""}
                onChange={(e) => handleDriverLicenseChange(e.target.value || undefined)}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-sm"
              >
                <option value="">Any license (or none)</option>
                <optgroup label="Car Licenses">
                  {driverLicensesByCategory.car.map((license) => (
                    <option key={license.id} value={license.id}>
                      {license.name} - {license.description}
                    </option>
                  ))}
                </optgroup>
                <optgroup label="Motorcycle Licenses">
                  {driverLicensesByCategory.motorcycle.map((license) => (
                    <option key={license.id} value={license.id}>
                      {license.name} - {license.description}
                    </option>
                  ))}
                </optgroup>
                <optgroup label="Truck Licenses">
                  {driverLicensesByCategory.truck.map((license) => (
                    <option key={license.id} value={license.id}>
                      {license.name} - {license.description}
                    </option>
                  ))}
                </optgroup>
                <optgroup label="Bus Licenses">
                  {driverLicensesByCategory.bus.map((license) => (
                    <option key={license.id} value={license.id}>
                      {license.name} - {license.description}
                    </option>
                  ))}
                </optgroup>
              </select>
            </div>
          </div>

          {/* Benefits Filters */}
          <div>
            <h3 className="text-sm font-medium mb-2">Job Preferences</h3>

            {/* Accommodation Benefits */}
            {accommodationBenefits.length > 0 && (
              <div className="mb-3 border border-neutral-200 dark:border-neutral-700 rounded-md p-3">
                <h4 className="text-xs font-medium text-neutral-500 dark:text-neutral-400 mb-2 uppercase">Accommodation</h4>
                <div className="space-y-1">
                  {accommodationBenefits.map((benefit) => (
                    <div key={benefit.id} className="flex items-center">
                      <input
                        type="checkbox"
                        id={`benefit-${benefit.id}`}
                        checked={localFilters.benefits.includes(benefit.id)}
                        onChange={() => handleBenefitToggle(benefit.id)}
                        className="mr-2"
                      />
                      <label htmlFor={`benefit-${benefit.id}`} className="text-sm">
                        {benefit.label}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Other Benefits */}
            <div className="max-h-60 overflow-y-auto pr-2">
              <div className="border border-neutral-200 dark:border-neutral-700 rounded-md p-3">
                <h4 className="text-xs font-medium text-neutral-500 dark:text-neutral-400 mb-2 uppercase">Other Benefits</h4>
                <div className="space-y-1">
                  {otherBenefits.map((benefit) => (
                    <div key={benefit.id} className="flex items-center">
                      <input
                        type="checkbox"
                        id={`benefit-${benefit.id}`}
                        checked={localFilters.benefits.includes(benefit.id)}
                        onChange={() => handleBenefitToggle(benefit.id)}
                        className="mr-2"
                      />
                      <label htmlFor={`benefit-${benefit.id}`} className="text-sm">
                        {benefit.label}
                      </label>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex space-x-3 pt-4 border-t border-neutral-200 dark:border-neutral-700">
            <button
              onClick={applyFilters}
              className="flex-1 bg-blue-600 hover:bg-blue-700 text-white py-2 px-4 rounded-md"
            >
              Apply Filters
            </button>
            <button
              onClick={resetFilters}
              className="flex-1 bg-neutral-200 dark:bg-neutral-700 hover:bg-neutral-300 dark:hover:bg-neutral-600 text-neutral-800 dark:text-neutral-200 py-2 px-4 rounded-md"
            >
              Reset
            </button>
          </div>
        </div>
      </div>
    </>
  );
}
