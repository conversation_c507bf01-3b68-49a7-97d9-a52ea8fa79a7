import { WorkExperience } from "@/types";

/**
 * Calculate total work experience in months
 * @param experiences Array of work experiences
 * @param positionId Optional position ID to filter by
 * @returns Total experience in months
 */
export function calculateTotalExperience(
  experiences: WorkExperience[],
  positionId?: string
): number {
  // Filter by position if specified
  const relevantExperiences = positionId
    ? experiences.filter(exp => exp.positionId === positionId)
    : experiences;
  
  // Calculate total months
  let totalMonths = 0;
  
  for (const exp of relevantExperiences) {
    const startDate = new Date(exp.startDate);
    const endDate = exp.current ? new Date() : new Date(exp.endDate!);
    
    // Calculate difference in months
    const months = (endDate.getFullYear() - startDate.getFullYear()) * 12 +
      (endDate.getMonth() - startDate.getMonth());
    
    totalMonths += Math.max(0, months); // Ensure we don't add negative months
  }
  
  return totalMonths;
}

/**
 * Format experience duration in a human-readable format
 * @param months Total months of experience
 * @returns Formatted string (e.g., "2 years, 3 months")
 */
export function formatExperienceDuration(months: number): string {
  if (months <= 0) {
    return "No experience";
  }
  
  const years = Math.floor(months / 12);
  const remainingMonths = months % 12;
  
  if (years === 0) {
    return `${remainingMonths} month${remainingMonths !== 1 ? 's' : ''}`;
  } else if (remainingMonths === 0) {
    return `${years} year${years !== 1 ? 's' : ''}`;
  } else {
    return `${years} year${years !== 1 ? 's' : ''}, ${remainingMonths} month${remainingMonths !== 1 ? 's' : ''}`;
  }
}

/**
 * Calculate experience by position and return formatted results
 * @param experiences Array of work experiences
 * @returns Object with position IDs as keys and formatted experience as values
 */
export function calculateExperienceByPosition(
  experiences: WorkExperience[]
): Record<string, string> {
  const result: Record<string, string> = {};
  
  // Get unique position IDs
  const positionIds = [...new Set(experiences
    .filter(exp => exp.positionId)
    .map(exp => exp.positionId as string))];
  
  // Calculate experience for each position
  for (const positionId of positionIds) {
    const months = calculateTotalExperience(experiences, positionId);
    result[positionId] = formatExperienceDuration(months);
  }
  
  // Add total experience
  const totalMonths = calculateTotalExperience(experiences);
  result.total = formatExperienceDuration(totalMonths);
  
  return result;
}
