"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { collection, query, where, getDocs, orderBy, doc, updateDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { JobApplication } from "@/types";

export default function EmployerApplicationsPage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [applications, setApplications] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState<string | null>(null);
  const [filter, setFilter] = useState({
    status: "all",
    jobId: "all",
  });
  const [jobs, setJobs] = useState<any[]>([]);

  useEffect(() => {
    // If not authenticated or not an employer, redirect to auth page
    if (!authLoading && (!user || user.role !== "employer")) {
      router.push("/auth");
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    const fetchData = async () => {
      if (!user) return;

      try {
        // Fetch employer's jobs first
        const jobsCollection = collection(db, "jobs");
        const jobsQuery = query(
          jobsCollection,
          where("employerId", "==", user.uid)
        );

        const jobsSnapshot = await getDocs(jobsQuery);
        const jobsList: any[] = [];

        jobsSnapshot.forEach((doc) => {
          const data = doc.data();
          jobsList.push({
            id: doc.id,
            ...data,
            createdAt: data.createdAt.toDate(),
            updatedAt: data.updatedAt.toDate(),
          });
        });

        setJobs(jobsList);

        // Fetch applications for all employer's jobs
        const applicationsCollection = collection(db, "applications");
        const applicationsQuery = query(
          applicationsCollection,
          where("employerId", "==", user.uid)
          // Removed orderBy to avoid requiring a composite index
        );

        const applicationsSnapshot = await getDocs(applicationsQuery);
        const applicationsList: any[] = [];

        applicationsSnapshot.forEach((doc) => {
          const data = doc.data();
          applicationsList.push({
            id: doc.id,
            ...data,
            createdAt: data.createdAt.toDate(),
            updatedAt: data.updatedAt.toDate(),
            lastContactDate: data.lastContactDate ? data.lastContactDate.toDate() : null,
          });
        });

        // Sort applications by createdAt date in descending order (newest first)
        applicationsList.sort((a, b) => b.createdAt.getTime() - a.createdAt.getTime());

        setApplications(applicationsList);
      } catch (error) {
        console.error("Error fetching data:", error);
      } finally {
        setLoading(false);
      }
    };

    if (user && user.role === "employer") {
      fetchData();
    }
  }, [user]);

  const handleStatusChange = async (applicationId: string, newStatus: string) => {
    if (!user) return;

    setUpdating(applicationId);

    try {
      const applicationRef = doc(db, "applications", applicationId);

      await updateDoc(applicationRef, {
        status: newStatus,
        updatedAt: new Date(),
        ...(newStatus === "contacted" ? { lastContactDate: new Date() } : {})
      });

      // Update local state
      setApplications(prev =>
        prev.map(app =>
          app.id === applicationId
            ? {
                ...app,
                status: newStatus,
                updatedAt: new Date(),
                ...(newStatus === "contacted" ? { lastContactDate: new Date() } : {})
              }
            : app
        )
      );
    } catch (error) {
      console.error("Error updating application status:", error);
      alert("Failed to update application status. Please try again.");
    } finally {
      setUpdating(null);
    }
  };

  const handleFilterChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilter(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const filteredApplications = applications.filter(app => {
    // Filter by status
    if (filter.status !== "all" && app.status !== filter.status) {
      return false;
    }

    // Filter by job
    if (filter.jobId !== "all" && app.jobId !== filter.jobId) {
      return false;
    }

    return true;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <span className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 text-xs px-2 py-1 rounded-full">
            Pending
          </span>
        );
      case "reviewed":
        return (
          <span className="bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300 text-xs px-2 py-1 rounded-full">
            Reviewed
          </span>
        );
      case "contacted":
        return (
          <span className="bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300 text-xs px-2 py-1 rounded-full">
            Contacted
          </span>
        );
      case "rejected":
        return (
          <span className="bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300 text-xs px-2 py-1 rounded-full">
            Rejected
          </span>
        );
      case "hired":
        return (
          <span className="bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 text-xs px-2 py-1 rounded-full">
            Hired
          </span>
        );
      default:
        return (
          <span className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 text-xs px-2 py-1 rounded-full">
            {status}
          </span>
        );
    }
  };

  if (authLoading || loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto py-12">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Applications</h1>
        <Link
          href="/employer/dashboard"
          className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
        >
          Back to Dashboard
        </Link>
      </div>

      {/* Filters */}
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-4 mb-6">
        <div className="flex flex-wrap gap-4">
          <div>
            <label htmlFor="status" className="block text-sm font-medium mb-1">
              Status
            </label>
            <select
              id="status"
              name="status"
              value={filter.status}
              onChange={handleFilterChange}
              className="px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md bg-white dark:bg-neutral-800"
            >
              <option value="all">All Statuses</option>
              <option value="pending">Pending</option>
              <option value="reviewed">Reviewed</option>
              <option value="contacted">Contacted</option>
              <option value="rejected">Rejected</option>
              <option value="hired">Hired</option>
            </select>
          </div>

          <div>
            <label htmlFor="jobId" className="block text-sm font-medium mb-1">
              Job
            </label>
            <select
              id="jobId"
              name="jobId"
              value={filter.jobId}
              onChange={handleFilterChange}
              className="px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md bg-white dark:bg-neutral-800"
            >
              <option value="all">All Jobs</option>
              {jobs.map(job => (
                <option key={job.id} value={job.id}>
                  {job.title}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {filteredApplications.length === 0 ? (
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8 text-center">
          <h2 className="text-xl font-medium mb-2">No Applications Found</h2>
          <p className="text-neutral-600 dark:text-neutral-400 mb-6">
            {applications.length === 0
              ? "You haven't received any applications yet."
              : "No applications match your current filters."}
          </p>
          {applications.length > 0 && (
            <button
              onClick={() => setFilter({ status: "all", jobId: "all" })}
              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
            >
              Clear Filters
            </button>
          )}
        </div>
      ) : (
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 overflow-hidden">
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead className="bg-neutral-50 dark:bg-neutral-900">
                <tr>
                  <th className="text-left py-3 px-4">Candidate</th>
                  <th className="text-left py-3 px-4">Job</th>
                  <th className="text-left py-3 px-4">Status</th>
                  <th className="text-left py-3 px-4">Applied On</th>
                  <th className="text-left py-3 px-4">Match</th>
                  <th className="text-left py-3 px-4">Actions</th>
                </tr>
              </thead>
              <tbody>
                {filteredApplications.map((application) => (
                  <tr
                    key={application.id}
                    className="border-b border-neutral-200 dark:border-neutral-700"
                  >
                    <td className="py-3 px-4">
                      <div className="font-medium">
                        {application.candidateProfession}, {application.candidateAge}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <div className="font-medium">{application.jobTitle}</div>
                    </td>
                    <td className="py-3 px-4">
                      {getStatusBadge(application.status)}
                    </td>
                    <td className="py-3 px-4">
                      {application.createdAt.toLocaleDateString()}
                    </td>
                    <td className="py-3 px-4">
                      {application.matchPercentage ? (
                        <div className="flex items-center">
                          <div className="w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2 mr-2 w-16">
                            <div
                              className={`h-2 rounded-full ${
                                application.matchPercentage >= 80 ? "bg-green-500" :
                                application.matchPercentage >= 50 ? "bg-yellow-500" :
                                "bg-red-500"
                              }`}
                              style={{ width: `${application.matchPercentage}%` }}
                            ></div>
                          </div>
                          <span>{application.matchPercentage}%</span>
                        </div>
                      ) : (
                        "N/A"
                      )}
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex items-center">
                        <Link
                          href={`/employer/applications/${application.id}`}
                          className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 mr-4"
                        >
                          View
                        </Link>

                        <select
                          value={application.status}
                          onChange={(e) => handleStatusChange(application.id, e.target.value)}
                          disabled={updating === application.id}
                          className="px-2 py-1 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md bg-white dark:bg-neutral-800"
                        >
                          <option value="pending">Pending</option>
                          <option value="reviewed">Reviewed</option>
                          <option value="contacted">Contacted</option>
                          <option value="rejected">Rejected</option>
                          <option value="hired">Hired</option>
                        </select>

                        {updating === application.id && (
                          <div className="ml-2 animate-spin rounded-full h-4 w-4 border-t-2 border-b-2 border-neutral-900"></div>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}
    </div>
  );
}
