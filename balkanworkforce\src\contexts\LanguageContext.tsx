import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { translations, Language, defaultLanguage } from '@/translations';
import { useAuth } from './AuthContext';
import { doc, updateDoc } from 'firebase/firestore';
import { db } from '@/lib/firebase';

type LanguageContextType = {
  language: Language;
  setLanguage: (language: Language) => void;
  t: (key: string) => string;
};

const LanguageContext = createContext<LanguageContextType | undefined>(undefined);

export const useLanguage = () => {
  const context = useContext(LanguageContext);
  if (context === undefined) {
    throw new Error('useLanguage must be used within a LanguageProvider');
  }
  return context;
};

type LanguageProviderProps = {
  children: ReactNode;
};

export const LanguageProvider: React.FC<LanguageProviderProps> = ({ children }) => {
  const { user } = useAuth();
  const [language, setLanguageState] = useState<Language>(defaultLanguage);

  // Initialize language from localStorage and browser preferences
  useEffect(() => {
    // Only run in browser environment
    if (typeof window !== 'undefined') {
      // Try to get language from localStorage
      const savedLanguage = localStorage.getItem('language') as Language;
      if (savedLanguage && Object.keys(translations).includes(savedLanguage)) {
        setLanguageState(savedLanguage);
        return;
      }

      // Try to get language from browser
      const browserLanguage = navigator.language.split('-')[0] as Language;
      if (browserLanguage && Object.keys(translations).includes(browserLanguage)) {
        setLanguageState(browserLanguage);
        return;
      }
    }
  }, []);

  // Update language in localStorage and user profile
  const setLanguage = async (newLanguage: Language) => {
    setLanguageState(newLanguage);

    // Only access localStorage in browser environment
    if (typeof window !== 'undefined') {
      localStorage.setItem('language', newLanguage);
    }

    // Update user's preferred language in Firestore if logged in
    if (user) {
      try {
        const userRef = doc(db, user.role === 'employer' ? 'employers' : 'candidates', user.uid);
        await updateDoc(userRef, {
          preferredLanguage: newLanguage,
          updatedAt: new Date(),
        });
      } catch (error) {
        console.error('Error updating preferred language:', error);
      }
    }
  };

  // Update language when user changes
  useEffect(() => {
    if (user && user.preferredLanguage && Object.keys(translations).includes(user.preferredLanguage as Language)) {
      setLanguageState(user.preferredLanguage as Language);

      // Only access localStorage in browser environment
      if (typeof window !== 'undefined') {
        localStorage.setItem('language', user.preferredLanguage as Language);
      }
    }
  }, [user]);

  // Translation function
  const t = (key: string): string => {
    const keys = key.split('.');
    let value = translations[language];

    for (const k of keys) {
      if (value && typeof value === 'object' && k in value) {
        value = value[k as keyof typeof value];
      } else {
        // Fallback to default language
        let fallbackValue = translations[defaultLanguage];
        for (const fk of keys) {
          if (fallbackValue && typeof fallbackValue === 'object' && fk in fallbackValue) {
            fallbackValue = fallbackValue[fk as keyof typeof fallbackValue];
          } else {
            return key; // Return key if translation not found
          }
        }
        return fallbackValue as string;
      }
    }

    return value as string;
  };

  return (
    <LanguageContext.Provider value={{ language, setLanguage, t }}>
      {children}
    </LanguageContext.Provider>
  );
};
