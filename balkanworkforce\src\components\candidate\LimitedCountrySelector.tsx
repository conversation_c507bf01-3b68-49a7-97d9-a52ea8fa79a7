import { useState, useEffect, useRef } from "react";
import Image from "next/image";
import { countries } from "@/data/countries";

interface LimitedCountrySelectorProps {
  value: string[];
  onChange: (value: string[]) => void;
  required?: boolean;
  label?: string;
}

export default function LimitedCountrySelector({
  value,
  onChange,
  required = false,
  label = "Desired Countries"
}: LimitedCountrySelectorProps) {
  const [selectedCountries, setSelectedCountries] = useState<string[]>(value || []);
  const [isOpen, setIsOpen] = useState(false);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Limit to only these four countries
  const allowedCountryIds = ['de', 'at', 'hr', 'si'];

  useEffect(() => {
    // Filter out any countries that are not in the allowed list
    const filteredValue = value ? value.filter(id => allowedCountryIds.includes(id)) : [];
    // Only update if the values are different to prevent infinite loops
    if (JSON.stringify(filteredValue) !== JSON.stringify(selectedCountries)) {
      setSelectedCountries(filteredValue);
    }
  }, [value, selectedCountries, allowedCountryIds]);

  // We'll call onChange directly in the toggleCountry function instead of using useEffect

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleCountry = (countryId: string) => {
    const newSelectedCountries = selectedCountries.includes(countryId)
      ? selectedCountries.filter(id => id !== countryId)
      : [...selectedCountries, countryId];

    // Update local state
    setSelectedCountries(newSelectedCountries);

    // Call onChange directly with the new value
    onChange(newSelectedCountries);
  };

  // Get only the allowed countries
  const allowedCountries = countries.filter(country => allowedCountryIds.includes(country.id));

  // Get selected country details
  const selectedCountryObjects = countries.filter(country => selectedCountries.includes(country.id));

  return (
    <div className="relative" ref={dropdownRef}>
      <label className="block text-sm font-medium mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>

      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md bg-white dark:bg-neutral-800"
      >
        <div className="flex items-center flex-wrap gap-1">
          {selectedCountryObjects.length > 0 ? (
            selectedCountryObjects.map(country => (
              <div
                key={country.id}
                className="flex items-center bg-neutral-100 dark:bg-neutral-700 px-2 py-1 rounded-md mr-1 mb-1"
              >
                {country.flagUrl && (
                  <div className="w-4 h-3 mr-1 overflow-hidden rounded-sm">
                    <Image
                      src={country.flagUrl}
                      alt={country.name}
                      width={16}
                      height={12}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                <span className="text-sm">{country.name}</span>
                <span
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleCountry(country.id);
                  }}
                  className="ml-1 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200 cursor-pointer"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </span>
              </div>
            ))
          ) : (
            <span>Select countries where you want to work</span>
          )}
        </div>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`h-5 w-5 transition-transform ${isOpen ? "transform rotate-180" : ""}`}
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute z-10 mt-1 w-full bg-white dark:bg-neutral-800 border border-neutral-300 dark:border-neutral-700 rounded-md shadow-lg max-h-60 overflow-auto">
          <div className="grid grid-cols-2 gap-0">
            {allowedCountries.map((country) => (
              <button
                key={country.id}
                type="button"
                onClick={(e) => {
                  e.preventDefault();
                  toggleCountry(country.id);
                }}
                className={`text-left px-3 py-3 flex items-center hover:bg-neutral-100 dark:hover:bg-neutral-700 ${
                  selectedCountries.includes(country.id) ? "bg-neutral-100 dark:bg-neutral-700" : ""
                }`}
              >
                <div className="flex items-center flex-1">
                  {country.flagUrl && (
                    <div className="w-6 h-4 mr-2 overflow-hidden rounded-sm">
                      <Image
                        src={country.flagUrl}
                        alt={country.name}
                        width={24}
                        height={16}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  {country.name}
                </div>
                {selectedCountries.includes(country.id) && (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                  </svg>
                )}
              </button>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}
