{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/data/countries.ts"], "sourcesContent": ["import { Country } from \"@/types\";\n\nexport const countries: Country[] = [\n  {\n    id: \"de\",\n    name: \"Germany\",\n    code: \"DE\",\n    flagUrl: \"/flags/de.svg\",\n    region: \"eu\"\n  },\n  {\n    id: \"at\",\n    name: \"Austria\",\n    code: \"AT\",\n    flagUrl: \"/flags/at.svg\",\n    region: \"eu\"\n  },\n  {\n    id: \"hr\",\n    name: \"Croatia\",\n    code: \"HR\",\n    flagUrl: \"/flags/hr.svg\",\n    region: \"eu\"\n  },\n  {\n    id: \"si\",\n    name: \"Slovenia\",\n    code: \"SI\",\n    flagUrl: \"/flags/si.svg\",\n    region: \"eu\"\n  },\n  {\n    id: \"ba\",\n    name: \"Bosnia and Herzegovina\",\n    code: \"BA\",\n    flagUrl: \"/flags/ba.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"rs\",\n    name: \"Serbia\",\n    code: \"RS\",\n    flagUrl: \"/flags/rs.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"me\",\n    name: \"Montenegro\",\n    code: \"ME\",\n    flagUrl: \"/flags/me.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"mk\",\n    name: \"North Macedonia\",\n    code: \"MK\",\n    flagUrl: \"/flags/mk.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"al\",\n    name: \"Albania\",\n    code: \"AL\",\n    flagUrl: \"/flags/al.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"bg\",\n    name: \"Bulgaria\",\n    code: \"BG\",\n    flagUrl: \"/flags/bg.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"ro\",\n    name: \"Romania\",\n    code: \"RO\",\n    flagUrl: \"/flags/ro.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"gr\",\n    name: \"Greece\",\n    code: \"GR\",\n    flagUrl: \"/flags/gr.svg\",\n    region: \"balkans\"\n  }\n];\n"], "names": [], "mappings": ";;;AAEO,MAAM,YAAuB;IAClC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;CACD", "debugId": null}}]}