# Plan za automatsko prevođenje oglasa za posao

## Pregled

Ovaj dokument opisuje plan implementacije automatskog prevođenja oglasa za posao na BalkanWorkForce platformi. Cilj je omogućiti poslodavcima da postavljaju oglase na svom jeziku, a da se ti oglasi automatski prevode na sve podržane jezike i prikazuju korisnicima na njihovom preferiranom jeziku.

## Zahtjevi

1. Poslodavci postavljaju oglase na svom jeziku bez potrebe za ručnim prevođenjem
2. Oglasi se automatski prevode na sve podržane jezike u pozadini
3. Korisnici vide oglase na svom preferiranom jeziku
4. Statički elementi (dropdown opcije, kategorije, itd.) se ne prevode ponovo, već se koriste postojeći prijevodi
5. Prevođenje je dio platforme i ne naplaćuje se dodatno

## Tehnička implementacija

### 1. Struktura podataka

Oglasi će imati sljedeću strukturu za podršku višejezičnosti:

```typescript
interface Job {
  id: string;
  employerId: string;
  // Osnovni podaci
  title: string;
  description: string;
  requirements: string;
  benefits: string;
  // Prevedeni sadržaj
  translations: {
    [language: string]: {
      title: string;
      description: string;
      requirements: string;
      benefits: string;
      isTranslated: boolean; // Označava je li prijevod dovršen
    }
  };
  // Metapodaci
  sourceLanguage: string; // Izvorni jezik oglasa
  // Ostali podaci...
}
```

### 2. Proces prevođenja

#### 2.1. Kreiranje/ažuriranje oglasa

1. Poslodavac kreira ili ažurira oglas na svom jeziku
2. Sustav detektira izvorni jezik ili koristi preferirani jezik poslodavca
3. Oglas se sprema u bazu podataka s izvornim jezikom
4. Pokreće se pozadinski proces za prevođenje

#### 2.2. Pozadinski proces za prevođenje

1. Cloud Function se aktivira kada se kreira ili ažurira oglas
2. Funkcija dohvaća oglas i provjerava je li potrebno prevođenje
3. Za svaki podržani jezik:
   - Provjera je li jezik različit od izvornog jezika
   - Provjera je li sadržaj već preveden ili je ažuriran
   - Ako je potrebno prevođenje, šalje zahtjev API-ju za prevođenje
   - Sprema prevedeni sadržaj u `translations` objekt
   - Postavlja `isTranslated` na `true` za taj jezik

#### 2.3. API za prevođenje

Za prevođenje ćemo koristiti OpenAI API:

1. Kreirati prompt koji jasno definira zadatak prevođenja
2. Poslati zahtjev OpenAI API-ju s izvornim tekstom i ciljnim jezikom
3. Obraditi odgovor i formatirati prevedeni tekst
4. Implementirati sustav za ponovno pokušavanje u slučaju grešaka

### 3. Prikaz prevedenih oglasa

1. Kada korisnik pregledava oglase, sustav dohvaća oglase iz baze podataka
2. Sustav provjerava preferirani jezik korisnika
3. Ako postoji prijevod na preferirani jezik korisnika i `isTranslated` je `true`, prikazuje se prevedeni sadržaj
4. Ako prijevod nije dostupan, prikazuje se sadržaj na izvornom jeziku
5. Statički elementi (kategorije, lokacije, tipovi zaposlenja) prikazuju se iz postojećih prijevoda

### 4. Optimizacija i keširanje

1. Implementirati keširanje prevedenih oglasa za brži pristup
2. Koristiti batch processing za prevođenje više oglasa odjednom
3. Implementirati sustav prioriteta za prevođenje (npr. novi oglasi imaju veći prioritet)
4. Pratiti korištenje API-ja za prevođenje i optimizirati troškove

## Implementacija

### Faza 1: Osnovna infrastruktura

1. Ažurirati model podataka za oglase da podržava višejezičnost
2. Implementirati detekciju jezika i spremanje izvornog jezika
3. Kreirati Cloud Function za pokretanje procesa prevođenja

### Faza 2: Integracija s API-jem za prevođenje

1. Implementirati servis za komunikaciju s OpenAI API-jem
2. Kreirati promptove za prevođenje različitih dijelova oglasa
3. Implementirati obradu odgovora i spremanje prevedenog sadržaja

### Faza 3: Prikaz i korisničko iskustvo

1. Ažurirati komponente za prikaz oglasa da koriste prevedeni sadržaj
2. Implementirati fallback na izvorni jezik ako prijevod nije dostupan
3. Dodati indikator izvornog jezika i opciju za prikaz originalnog teksta

### Faza 4: Optimizacija i monitoring

1. Implementirati keširanje i batch processing
2. Dodati monitoring za praćenje procesa prevođenja
3. Optimizirati troškove i performanse

## Tehnologije

- Firebase Cloud Functions za pozadinske procese
- OpenAI API za prevođenje
- Firestore za pohranu podataka
- React za frontend komponente

## Ograničenja i izazovi

1. Kvaliteta automatskog prevođenja može varirati
2. Troškovi API-ja za prevođenje mogu biti značajni s rastom platforme
3. Latencija pri prevođenju dugih tekstova
4. Ograničenja API-ja (rate limiting, maksimalna duljina teksta)

## Budući razvoj

1. Implementacija ljudske revizije prijevoda za premium oglase
2. Dodavanje više jezika
3. Implementacija specijaliziranih modela za prevođenje stručne terminologije
4. Analiza kvalitete prijevoda i kontinuirano poboljšanje
