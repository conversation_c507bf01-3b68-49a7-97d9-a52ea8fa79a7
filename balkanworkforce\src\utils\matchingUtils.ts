import { Candidate, JobListing, WorkExperience, Education } from "@/types";

// Helper function to compare language levels
export const compareLanguageLevels = (candidateLevel: string, requiredLevel: string): boolean => {
  const levels = ["A1", "A2", "B1", "B2", "C1", "C2", "Native"];
  const candidateIndex = levels.indexOf(candidateLevel);
  const requiredIndex = levels.indexOf(requiredLevel);

  if (candidateIndex === -1 || requiredIndex === -1) return false;
  return candidateIndex >= requiredIndex;
};

// Helper function to calculate experience duration for a specific position
const calculatePositionExperience = (
  workExperience: WorkExperience[],
  positionId: string
): number => {
  let totalMonths = 0;

  workExperience.forEach(experience => {
    if (experience.positionId === positionId) {
      const startDate = new Date(experience.startDate);
      const endDate = experience.current ? new Date() : new Date(experience.endDate!);

      // Calculate months between dates
      const months = (endDate.getFullYear() - startDate.getFullYear()) * 12 +
                     (endDate.getMonth() - startDate.getMonth());

      totalMonths += months;
    }
  });

  return totalMonths;
};

// Helper function to check if candidate has required education level
const hasRequiredEducationLevel = (
  education: Education[],
  minLevel?: string,
  requiredDegrees?: string[]
): boolean => {
  if (!minLevel && (!requiredDegrees || requiredDegrees.length === 0)) {
    return true; // No education requirements
  }

  // Education level hierarchy
  const levelHierarchy = {
    "secondary": 1,
    "vocational": 2,
    "tertiary": 3,
    "bachelor": 4,
    "master": 5,
    "doctorate": 6
  };

  // Check for minimum education level
  if (minLevel) {
    const requiredLevelValue = levelHierarchy[minLevel as keyof typeof levelHierarchy];

    // Check if any education meets the minimum level
    const hasMinLevel = education.some(edu => {
      if (!edu.level) return false;
      const eduLevelValue = levelHierarchy[edu.level as keyof typeof levelHierarchy];
      return eduLevelValue >= requiredLevelValue;
    });

    if (!hasMinLevel) return false;
  }

  // Check for specific required degrees
  if (requiredDegrees && requiredDegrees.length > 0) {
    const hasDegree = education.some(edu =>
      edu.degreeId && requiredDegrees.includes(edu.degreeId)
    );

    if (!hasDegree) return false;
  }

  return true;
};

// Helper function to check if candidate is willing to work in required countries
const isWillingToWorkInRequiredCountries = (
  desiredCountries: string[] | undefined,
  requiredCountries: string[] | undefined
): boolean => {
  if (!requiredCountries || requiredCountries.length === 0) {
    return true; // No country requirements
  }

  if (!desiredCountries || desiredCountries.length === 0) {
    return false; // Candidate hasn't specified desired countries
  }

  // Check if the job's country is in the candidate's desired countries
  // Since job can only have one country now, we check if that country is in the candidate's desired countries
  return requiredCountries.some(country => desiredCountries.includes(country));
};

// Main function to calculate match score between candidate and job
export const calculateMatchScore = (candidate: Candidate, job: JobListing): {
  score: number;
  details: {
    languageMatch: boolean;
    experienceMatch: boolean;
    educationMatch: boolean;
    locationMatch: boolean;
    benefitsMatch: number;
  };
} => {
  // Default result
  const result = {
    score: 0,
    details: {
      languageMatch: true,
      experienceMatch: true,
      educationMatch: true,
      locationMatch: true,
      benefitsMatch: 0
    }
  };

  // If no structured requirements, only calculate benefits match
  if (!job.structuredRequirements) {
    // Calculate benefits match (percentage of matching benefits)
    const candidateBenefits = candidate.benefits || [];
    const jobBenefits = job.benefits || [];

    if (jobBenefits.length > 0 && candidateBenefits.length > 0) {
      const matchingBenefits = jobBenefits.filter(benefit =>
        candidateBenefits.includes(benefit)
      ).length;

      result.details.benefitsMatch = Math.round((matchingBenefits / jobBenefits.length) * 100);
    }

    result.score = result.details.benefitsMatch;
    return result;
  }

  const requirements = job.structuredRequirements;

  // Check language requirements
  if (requirements.germanLanguageLevel) {
    result.details.languageMatch = result.details.languageMatch &&
      compareLanguageLevels(candidate.germanLanguageLevel, requirements.germanLanguageLevel);
  }

  if (requirements.englishLanguageLevel) {
    result.details.languageMatch = result.details.languageMatch &&
      compareLanguageLevels(candidate.englishLanguageLevel, requirements.englishLanguageLevel);
  }

  // Check work experience requirements
  if (requirements.requiredPositions && requirements.requiredPositions.length > 0) {
    const workExperience = candidate.workExperience || [];

    result.details.experienceMatch = requirements.requiredPositions.every(req => {
      const experienceMonths = calculatePositionExperience(workExperience, req.positionId);
      return experienceMonths >= req.minExperienceMonths;
    });
  }

  // Check education requirements
  result.details.educationMatch = hasRequiredEducationLevel(
    candidate.education || [],
    requirements.minEducationLevel,
    requirements.requiredDegrees
  );

  // Check location requirements
  result.details.locationMatch = isWillingToWorkInRequiredCountries(
    candidate.desiredCountries,
    requirements.requiredCountries
  );

  // Calculate benefits match (percentage of matching benefits)
  const candidateBenefits = candidate.benefits || [];
  const jobBenefits = job.benefits || [];

  if (jobBenefits.length > 0 && candidateBenefits.length > 0) {
    const matchingBenefits = jobBenefits.filter(benefit =>
      candidateBenefits.includes(benefit)
    ).length;

    result.details.benefitsMatch = Math.round((matchingBenefits / jobBenefits.length) * 100);
  }

  // Calculate overall score
  // Hard requirements (must match): language, experience, education, location
  if (!result.details.languageMatch ||
      !result.details.experienceMatch ||
      !result.details.educationMatch ||
      !result.details.locationMatch) {
    result.score = 0;
  } else {
    // If all hard requirements match, score is based on benefits match
    result.score = result.details.benefitsMatch;
  }

  return result;
};

// Function to get matching candidates for a job
export const getMatchingCandidates = (
  candidates: Candidate[],
  job: JobListing
): { candidate: Candidate; score: number; details: any }[] => {
  return candidates
    .map(candidate => {
      const { score, details } = calculateMatchScore(candidate, job);
      return { candidate, score, details };
    })
    .filter(match => match.score > 0) // Filter out non-matching candidates
    .sort((a, b) => b.score - a.score); // Sort by score (highest first)
};

// Function to get matching jobs for a candidate
export const getMatchingJobs = (
  jobs: JobListing[],
  candidate: Candidate
): { job: JobListing; score: number; details: any }[] => {
  return jobs
    .map(job => {
      const { score, details } = calculateMatchScore(candidate, job);
      return { job, score, details };
    })
    .filter(match => match.score > 0) // Filter out non-matching jobs
    .sort((a, b) => b.score - a.score); // Sort by score (highest first)
};
