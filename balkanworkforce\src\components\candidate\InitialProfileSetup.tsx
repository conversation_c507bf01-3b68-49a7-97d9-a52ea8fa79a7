import { useState } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { doc, setDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";

// Form validation schema
const initialProfileSchema = z.object({
  profession: z.string().min(2, "Profession is required"),
  age: z.coerce.number().min(18, "Age must be at least 18").max(100, "Age must be less than 100"),
});

type InitialProfileFormValues = z.infer<typeof initialProfileSchema>;

interface InitialProfileSetupProps {
  onComplete: () => void;
}

export default function InitialProfileSetup({ onComplete }: InitialProfileSetupProps) {
  const { user } = useAuth();
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<InitialProfileFormValues>({
    resolver: zodResolver(initialProfileSchema),
    defaultValues: {
      profession: "",
      age: undefined,
    },
  });

  const onSubmit = async (data: InitialProfileFormValues) => {
    if (!user) return;

    setSaving(true);
    setError(null);

    try {
      // Save basic profile data
      const candidateData = {
        uid: user.uid,
        email: user.email,
        profession: data.profession,
        age: data.age,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Save to Firestore
      await setDoc(
        doc(db, "candidates", user.uid),
        candidateData,
        { merge: true }
      );

      // Call the onComplete callback to proceed
      onComplete();
    } catch (error: any) {
      console.error("Error saving initial profile:", error);
      setError(error.message || "Failed to save profile. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  return (
    <div className="max-w-md mx-auto bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8">
      <h2 className="text-xl font-semibold mb-6">Complete Your Profile</h2>
      
      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6">
          {error}
        </div>
      )}
      
      <p className="text-neutral-600 dark:text-neutral-400 mb-6">
        Please provide your profession and age to create your anonymous profile. 
        Your name will be hidden, and you will be shown as:
      </p>
      
      <form onSubmit={handleSubmit(onSubmit)}>
        <div className="space-y-4 mb-6">
          <div>
            <label htmlFor="profession" className="block text-sm font-medium mb-1">
              Profession <span className="text-red-500">*</span>
            </label>
            <input
              id="profession"
              {...register("profession")}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              placeholder="e.g., Electrician, Software Developer"
            />
            {errors.profession && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.profession.message}</p>
            )}
          </div>

          <div>
            <label htmlFor="age" className="block text-sm font-medium mb-1">
              Age <span className="text-red-500">*</span>
            </label>
            <input
              id="age"
              type="number"
              {...register("age")}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              placeholder="Your age"
            />
            {errors.age && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.age.message}</p>
            )}
          </div>
        </div>
        
        <div className="bg-neutral-50 dark:bg-neutral-900 p-4 rounded-md mb-6">
          <div className="text-xl font-medium">
            <span className="text-neutral-500 dark:text-neutral-400">You will appear as: </span>
            <span id="profile-preview">
              {register("profession").value || "Your Profession"}, {register("age").value || "XX"}
            </span>
          </div>
        </div>

        <button
          type="submit"
          disabled={saving}
          className={`w-full py-2 px-4 rounded-md font-medium ${
            saving
              ? "bg-neutral-400 dark:bg-neutral-600 cursor-wait"
              : "bg-neutral-900 hover:bg-neutral-800 dark:bg-neutral-700 dark:hover:bg-neutral-600 text-white"
          }`}
        >
          {saving ? "Saving..." : "Save and Continue"}
        </button>
      </form>
    </div>
  );
}
