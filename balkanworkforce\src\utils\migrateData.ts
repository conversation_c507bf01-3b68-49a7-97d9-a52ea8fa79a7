import { collection, doc, setDoc, getDocs } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { positions } from "@/data/positions";
import { studyFields } from "@/data/studyFields";
import { driverLicenses } from "@/data/driverLicenses";
import { jobBenefits } from "@/data/jobBenefits";
import { countries } from "@/data/countries";

interface MigrationResult {
  success: boolean;
  message: string;
  details?: any;
}

export async function migrateStaticDataToFirestore(): Promise<MigrationResult> {
  try {
    console.log("Starting data migration to Firestore...");

    // Migrate positions
    await migrateCollection("positions", positions);
    console.log("Positions migrated successfully");

    // Migrate study fields
    await migrateCollection("studyFields", studyFields);
    console.log("Study fields migrated successfully");

    // Migrate driver licenses
    await migrateCollection("driverLicenses", driverLicenses);
    console.log("Driver licenses migrated successfully");

    // Migrate job benefits
    await migrateCollection("benefits", jobBenefits);
    console.log("Job benefits migrated successfully");

    // Migrate countries
    await migrateCollection("countries", countries);
    console.log("Countries migrated successfully");

    return {
      success: true,
      message: "All data migrated successfully to Firestore",
    };
  } catch (error) {
    console.error("Error during migration:", error);
    return {
      success: false,
      message: "Migration failed",
      details: error,
    };
  }
}

async function migrateCollection(collectionName: string, data: any[]) {
  // Check if collection already has data
  const snapshot = await getDocs(collection(db, collectionName));
  
  if (!snapshot.empty) {
    console.log(`Collection ${collectionName} already has data, skipping...`);
    return;
  }

  // Migrate data
  for (const item of data) {
    const docRef = doc(db, collectionName, item.id);
    await setDoc(docRef, {
      ...item,
      createdAt: new Date(),
      updatedAt: new Date(),
    });
  }
}

export async function checkMigrationStatus(): Promise<{
  positions: number;
  studyFields: number;
  driverLicenses: number;
  benefits: number;
  countries: number;
}> {
  const collections = ["positions", "studyFields", "driverLicenses", "benefits", "countries"];
  const status: any = {};

  for (const collectionName of collections) {
    const snapshot = await getDocs(collection(db, collectionName));
    status[collectionName] = snapshot.size;
  }

  return status;
}
