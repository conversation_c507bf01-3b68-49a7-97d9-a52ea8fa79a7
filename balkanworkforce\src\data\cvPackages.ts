import { CVPackage } from "@/types";

export const cvPackages: CVPackage[] = [
  {
    id: "cv-basic",
    name: "Basic CV Package",
    type: "cv",
    inviteCount: 10,
    price: 5,
    currency: "EUR",
    description: "Access to 10 candidate invitations. Invite candidates to apply for your jobs."
  },
  {
    id: "cv-standard",
    name: "Standard CV Package",
    type: "cv",
    inviteCount: 25,
    price: 10,
    currency: "EUR",
    description: "Access to 25 candidate invitations. Best value for active recruiters."
  },
  {
    id: "cv-premium",
    name: "Premium CV Package",
    type: "cv",
    inviteCount: 50,
    price: 20,
    currency: "EUR",
    description: "Access to 50 candidate invitations. Ideal for high-volume recruiting."
  },
  {
    id: "combined-basic",
    name: "Basic Combined Package",
    type: "combined",
    inviteCount: 5,
    jobDuration: 7,
    isPremiumJob: true,
    price: 15,
    currency: "EUR",
    description: "7-day premium job listing plus 5 candidate invitations."
  },
  {
    id: "combined-standard",
    name: "Standard Combined Package",
    type: "combined",
    inviteCount: 10,
    jobDuration: 14,
    isPremiumJob: true,
    price: 25,
    currency: "EUR",
    description: "14-day premium job listing plus 10 candidate invitations."
  },
  {
    id: "combined-premium",
    name: "Premium Combined Package",
    type: "combined",
    inviteCount: 20,
    jobDuration: 30,
    isPremiumJob: true,
    price: 40,
    currency: "EUR",
    description: "30-day premium job listing plus 20 candidate invitations."
  }
];
