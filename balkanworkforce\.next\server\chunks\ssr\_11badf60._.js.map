{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/data/jobBenefits.ts"], "sourcesContent": ["// Job benefits/features that employers can select when creating a job\n// and candidates can select as preferences\n\nexport const jobBenefits = [\n  {\n    id: \"flexible_hours\",\n    label: \"Flexible Working Hours\",\n    description: \"Flexible start and end times or the ability to work remotely\"\n  },\n  {\n    id: \"remote_work\",\n    label: \"Remote Work Options\",\n    description: \"Possibility to work remotely part-time or full-time\"\n  },\n  {\n    id: \"health_insurance\",\n    label: \"Health Insurance\",\n    description: \"Comprehensive health insurance coverage\"\n  },\n  {\n    id: \"paid_vacation\",\n    label: \"Paid Vacation\",\n    description: \"Generous paid time off and vacation days\"\n  },\n  {\n    id: \"professional_development\",\n    label: \"Professional Development\",\n    description: \"Training, courses, and career advancement opportunities\"\n  },\n  {\n    id: \"retirement_plan\",\n    label: \"Retirement Plan\",\n    description: \"Company pension or retirement savings plan\"\n  },\n  {\n    id: \"relocation_assistance\",\n    label: \"Relocation Assistance\",\n    description: \"Help with moving expenses and finding accommodation\"\n  },\n  {\n    id: \"accommodation_provided_free\",\n    label: \"Free Accommodation Provided\",\n    description: \"Accommodation is provided and fully paid by the employer\"\n  },\n  {\n    id: \"accommodation_provided_paid\",\n    label: \"Accommodation Provided (Paid)\",\n    description: \"Accommodation is arranged by the employer but paid by the employee\"\n  },\n  {\n    id: \"accommodation_not_provided\",\n    label: \"Accommodation Not Provided\",\n    description: \"Employee must arrange their own accommodation\"\n  },\n  {\n    id: \"accommodation_assistance\",\n    label: \"Accommodation Finding Assistance\",\n    description: \"Employer assists in finding accommodation but does not provide it directly\"\n  },\n  {\n    id: \"language_courses\",\n    label: \"Language Courses\",\n    description: \"Free or subsidized language training\"\n  },\n  {\n    id: \"childcare\",\n    label: \"Childcare Support\",\n    description: \"On-site childcare or childcare subsidies\"\n  },\n  {\n    id: \"gym_membership\",\n    label: \"Gym Membership\",\n    description: \"Free or discounted gym or wellness program\"\n  },\n  {\n    id: \"company_car\",\n    label: \"Company Car\",\n    description: \"Company vehicle or car allowance\"\n  },\n  {\n    id: \"meal_allowance\",\n    label: \"Meal Allowance\",\n    description: \"Free or subsidized meals or meal vouchers\"\n  },\n  {\n    id: \"performance_bonus\",\n    label: \"Performance Bonuses\",\n    description: \"Financial bonuses based on performance\"\n  },\n  {\n    id: \"housing_allowance\",\n    label: \"Housing Allowance\",\n    description: \"Assistance with housing costs\"\n  },\n  {\n    id: \"public_transport\",\n    label: \"Public Transport Subsidy\",\n    description: \"Free or subsidized public transportation\"\n  }\n];\n\n// Minimum number of benefits that must be selected when creating a job\nexport const MIN_BENEFITS_REQUIRED = 3;\n"], "names": [], "mappings": "AAAA,sEAAsE;AACtE,2CAA2C;;;;;AAEpC,MAAM,cAAc;IACzB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;CACD;AAGM,MAAM,wBAAwB", "debugId": null}}, {"offset": {"line": 117, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/app/employer/jobs/%5Bid%5D/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { useParams, useRouter } from \"next/navigation\";\nimport { useAuth } from \"@/contexts/AuthContext\";\nimport { doc, getDoc, updateDoc, deleteDoc } from \"firebase/firestore\";\nimport { db } from \"@/lib/firebase\";\nimport Link from \"next/link\";\nimport { jobBenefits } from \"@/data/jobBenefits\";\nimport Image from \"next/image\";\n\nexport default function EmployerJobDetailPage() {\n  const { id } = useParams();\n  const router = useRouter();\n  const { user, loading: authLoading } = useAuth();\n  const [job, setJob] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n  const [error, setError] = useState<string | null>(null);\n  const [deleting, setDeleting] = useState(false);\n  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);\n\n  useEffect(() => {\n    // If not authenticated or not an employer, redirect to auth page\n    if (!authLoading && (!user || user.role !== \"employer\")) {\n      router.push(\"/auth\");\n    }\n  }, [user, authLoading, router]);\n\n  useEffect(() => {\n    const fetchJob = async () => {\n      if (!user || !id || typeof id !== \"string\") return;\n\n      try {\n        const jobDoc = await getDoc(doc(db, \"jobs\", id));\n\n        if (!jobDoc.exists()) {\n          router.push(\"/employer/dashboard\");\n          return;\n        }\n\n        const jobData = jobDoc.data();\n\n        // Check if this job belongs to the current employer\n        if (jobData.employerId !== user.uid) {\n          router.push(\"/employer/dashboard\");\n          return;\n        }\n\n        setJob({\n          id: jobDoc.id,\n          ...jobData,\n          createdAt: jobData.createdAt.toDate(),\n          updatedAt: jobData.updatedAt.toDate(),\n          premiumUntil: jobData.premiumUntil ? jobData.premiumUntil.toDate() : undefined,\n        });\n      } catch (error) {\n        console.error(\"Error fetching job:\", error);\n        setError(\"Failed to load job details. Please try again.\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    if (user && user.role === \"employer\") {\n      fetchJob();\n    }\n  }, [user, id, router]);\n\n  const handleDeleteJob = async () => {\n    if (!user || !id || typeof id !== \"string\") return;\n\n    setDeleting(true);\n\n    try {\n      await deleteDoc(doc(db, \"jobs\", id));\n      router.push(\"/employer/dashboard\");\n    } catch (error) {\n      console.error(\"Error deleting job:\", error);\n      setError(\"Failed to delete job. Please try again.\");\n      setDeleting(false);\n    }\n  };\n\n  const getBenefitLabel = (benefitId: string) => {\n    const benefit = jobBenefits.find(b => b.id === benefitId);\n    return benefit ? benefit.label : benefitId;\n  };\n\n  if (authLoading || loading) {\n    return (\n      <div className=\"flex justify-center items-center min-h-[60vh]\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900\"></div>\n      </div>\n    );\n  }\n\n  if (error) {\n    return (\n      <div className=\"max-w-4xl mx-auto py-12\">\n        <div className=\"bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6\">\n          {error}\n        </div>\n        <Link\n          href=\"/employer/dashboard\"\n          className=\"text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white\"\n        >\n          Back to Dashboard\n        </Link>\n      </div>\n    );\n  }\n\n  if (!job) {\n    return (\n      <div className=\"max-w-4xl mx-auto py-12\">\n        <div className=\"text-center\">\n          <h2 className=\"text-2xl font-bold mb-4\">Job Not Found</h2>\n          <p className=\"text-neutral-600 dark:text-neutral-400 mb-6\">\n            The job you're looking for doesn't exist or you don't have permission to view it.\n          </p>\n          <Link\n            href=\"/employer/dashboard\"\n            className=\"text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white\"\n          >\n            Back to Dashboard\n          </Link>\n        </div>\n      </div>\n    );\n  }\n\n  return (\n    <div className=\"max-w-4xl mx-auto py-12\">\n      <div className=\"flex justify-between items-center mb-8\">\n        <h1 className=\"text-3xl font-bold\">Job Details</h1>\n        <div className=\"flex gap-4\">\n          <Link\n            href={`/employer/jobs/${id}/edit`}\n            className=\"bg-neutral-100 hover:bg-neutral-200 dark:bg-neutral-700 dark:hover:bg-neutral-600 text-neutral-900 dark:text-white px-4 py-2 rounded-md\"\n          >\n            Edit Job\n          </Link>\n          <Link\n            href=\"/employer/dashboard\"\n            className=\"text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white px-4 py-2\"\n          >\n            Back to Dashboard\n          </Link>\n        </div>\n      </div>\n\n      <div className=\"bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8 mb-8\">\n        <div className=\"flex justify-between items-start mb-6\">\n          <div>\n            <h2 className=\"text-2xl font-bold mb-2\">{job.title}</h2>\n            <div className=\"flex flex-wrap gap-2\">\n              <span className=\"bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 px-3 py-1 rounded-full text-sm\">\n                {job.location}\n              </span>\n              <span className=\"bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 px-3 py-1 rounded-full text-sm\">\n                {job.employmentType.replace(\"-\", \" \")}\n              </span>\n              {job.salary && (\n                <span className=\"bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 px-3 py-1 rounded-full text-sm\">\n                  {job.salary.min} - {job.salary.max} {job.salary.currency}\n                </span>\n              )}\n            </div>\n          </div>\n          <div className=\"flex items-center\">\n            {job.isPremium && (\n              <span className=\"bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-100 px-3 py-1 rounded-full text-sm font-medium mr-4\">\n                Premium\n              </span>\n            )}\n            <span className=\"text-sm text-neutral-500 dark:text-neutral-400\">\n              Posted on {job.createdAt.toLocaleDateString()}\n            </span>\n          </div>\n        </div>\n\n        <div className=\"border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6\">\n          <h3 className=\"text-xl font-semibold mb-4\">Job Description</h3>\n          <div className=\"prose dark:prose-invert max-w-none\">\n            <p className=\"whitespace-pre-line\">{job.description}</p>\n          </div>\n        </div>\n\n        <div className=\"border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6\">\n          <h3 className=\"text-xl font-semibold mb-4\">Requirements</h3>\n          <ul className=\"list-disc pl-5 space-y-2\">\n            {job.requirements.map((requirement: string, index: number) => (\n              <li key={index}>{requirement}</li>\n            ))}\n          </ul>\n        </div>\n\n        <div className=\"border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6\">\n          <h3 className=\"text-xl font-semibold mb-4\">Benefits</h3>\n          <div className=\"grid grid-cols-1 md:grid-cols-2 gap-3\">\n            {job.benefits.map((benefitId: string) => (\n              <div key={benefitId} className=\"flex items-center\">\n                <svg xmlns=\"http://www.w3.org/2000/svg\" className=\"h-5 w-5 text-green-500 mr-2\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z\" clipRule=\"evenodd\" />\n                </svg>\n                <span>{getBenefitLabel(benefitId)}</span>\n              </div>\n            ))}\n          </div>\n        </div>\n\n        {job.tags && job.tags.length > 0 && (\n          <div className=\"border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6\">\n            <h3 className=\"text-xl font-semibold mb-4\">Tags</h3>\n            <div className=\"flex flex-wrap gap-2\">\n              {job.tags.map((tag: string, index: number) => (\n                <span\n                  key={index}\n                  className=\"bg-neutral-100 dark:bg-neutral-700 text-neutral-600 dark:text-neutral-400 px-3 py-1 rounded-full text-sm\"\n                >\n                  {tag}\n                </span>\n              ))}\n            </div>\n          </div>\n        )}\n\n        <div className=\"border-t border-neutral-200 dark:border-neutral-700 pt-6\">\n          <div className=\"flex justify-between items-center\">\n            <div>\n              <h3 className=\"text-xl font-semibold mb-2\">Applications</h3>\n              <p className=\"text-neutral-600 dark:text-neutral-400\">\n                {job.applications.length} application{job.applications.length !== 1 ? 's' : ''}\n              </p>\n            </div>\n            <Link\n              href={`/employer/applications?jobId=${id}`}\n              className=\"bg-neutral-900 hover:bg-neutral-800 text-white px-4 py-2 rounded-md\"\n            >\n              View Applications\n            </Link>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8\">\n        <h3 className=\"text-xl font-semibold mb-6\">Job Actions</h3>\n\n        <div className=\"flex flex-wrap gap-4\">\n          {job.isPremium && job.premiumUntil && new Date() < job.premiumUntil ? (\n            <div className=\"mb-4 w-full\">\n              <div className=\"bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4\">\n                <h4 className=\"text-lg font-medium text-amber-800 dark:text-amber-400 mb-2\">\n                  Premium Job\n                </h4>\n                <p className=\"text-amber-700 dark:text-amber-500 mb-2\">\n                  This job is promoted until {job.premiumUntil.toLocaleDateString()}\n                </p>\n                <Link\n                  href={`/employer/jobs/${id}/promote`}\n                  className=\"text-amber-800 dark:text-amber-400 underline hover:no-underline\"\n                >\n                  Extend Premium or Add More Countries\n                </Link>\n              </div>\n            </div>\n          ) : (\n            <Link\n              href={`/employer/jobs/${id}/promote`}\n              className=\"bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-md\"\n            >\n              Promote to Premium\n            </Link>\n          )}\n\n          <Link\n            href={`/jobs/${id}`}\n            className=\"bg-neutral-100 hover:bg-neutral-200 dark:bg-neutral-700 dark:hover:bg-neutral-600 text-neutral-900 dark:text-white px-4 py-2 rounded-md\"\n          >\n            View Public Listing\n          </Link>\n\n          <button\n            onClick={() => setShowDeleteConfirm(true)}\n            className=\"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md\"\n          >\n            Delete Job\n          </button>\n        </div>\n\n        {showDeleteConfirm && (\n          <div className=\"fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50\">\n            <div className=\"bg-white dark:bg-neutral-800 rounded-lg p-6 max-w-md w-full\">\n              <h3 className=\"text-xl font-bold mb-4\">Confirm Deletion</h3>\n              <p className=\"text-neutral-600 dark:text-neutral-400 mb-6\">\n                Are you sure you want to delete this job? This action cannot be undone.\n              </p>\n              <div className=\"flex justify-end gap-4\">\n                <button\n                  onClick={() => setShowDeleteConfirm(false)}\n                  className=\"px-4 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n                >\n                  Cancel\n                </button>\n                <button\n                  onClick={handleDeleteJob}\n                  disabled={deleting}\n                  className=\"bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md\"\n                >\n                  {deleting ? \"Deleting...\" : \"Delete Job\"}\n                </button>\n              </div>\n            </div>\n          </div>\n        )}\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AACA;AACA;AAAA;AACA;AACA;AACA;AARA;;;;;;;;;AAWe,SAAS;IACtB,MAAM,EAAE,EAAE,EAAE,GAAG,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,SAAS,CAAA,GAAA,kIAAA,CAAA,YAAS,AAAD;IACvB,MAAM,EAAE,IAAI,EAAE,SAAS,WAAW,EAAE,GAAG,CAAA,GAAA,+HAAA,CAAA,UAAO,AAAD;IAC7C,MAAM,CAAC,KAAK,OAAO,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAO;IACpC,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IACzC,MAAM,CAAC,mBAAmB,qBAAqB,GAAG,CAAA,GAAA,qMAAA,CAAA,WAAQ,AAAD,EAAE;IAE3D,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,iEAAiE;QACjE,IAAI,CAAC,eAAe,CAAC,CAAC,QAAQ,KAAK,IAAI,KAAK,UAAU,GAAG;YACvD,OAAO,IAAI,CAAC;QACd;IACF,GAAG;QAAC;QAAM;QAAa;KAAO;IAE9B,CAAA,GAAA,qMAAA,CAAA,YAAS,AAAD,EAAE;QACR,MAAM,WAAW;YACf,IAAI,CAAC,QAAQ,CAAC,MAAM,OAAO,OAAO,UAAU;YAE5C,IAAI;gBACF,MAAM,SAAS,MAAM,CAAA,GAAA,iKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,QAAQ;gBAE5C,IAAI,CAAC,OAAO,MAAM,IAAI;oBACpB,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,MAAM,UAAU,OAAO,IAAI;gBAE3B,oDAAoD;gBACpD,IAAI,QAAQ,UAAU,KAAK,KAAK,GAAG,EAAE;oBACnC,OAAO,IAAI,CAAC;oBACZ;gBACF;gBAEA,OAAO;oBACL,IAAI,OAAO,EAAE;oBACb,GAAG,OAAO;oBACV,WAAW,QAAQ,SAAS,CAAC,MAAM;oBACnC,WAAW,QAAQ,SAAS,CAAC,MAAM;oBACnC,cAAc,QAAQ,YAAY,GAAG,QAAQ,YAAY,CAAC,MAAM,KAAK;gBACvE;YACF,EAAE,OAAO,OAAO;gBACd,QAAQ,KAAK,CAAC,uBAAuB;gBACrC,SAAS;YACX,SAAU;gBACR,WAAW;YACb;QACF;QAEA,IAAI,QAAQ,KAAK,IAAI,KAAK,YAAY;YACpC;QACF;IACF,GAAG;QAAC;QAAM;QAAI;KAAO;IAErB,MAAM,kBAAkB;QACtB,IAAI,CAAC,QAAQ,CAAC,MAAM,OAAO,OAAO,UAAU;QAE5C,YAAY;QAEZ,IAAI;YACF,MAAM,CAAA,GAAA,iKAAA,CAAA,YAAS,AAAD,EAAE,CAAA,GAAA,iKAAA,CAAA,MAAG,AAAD,EAAE,sHAAA,CAAA,KAAE,EAAE,QAAQ;YAChC,OAAO,IAAI,CAAC;QACd,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,uBAAuB;YACrC,SAAS;YACT,YAAY;QACd;IACF;IAEA,MAAM,kBAAkB,CAAC;QACvB,MAAM,UAAU,0HAAA,CAAA,cAAW,CAAC,IAAI,CAAC,CAAA,IAAK,EAAE,EAAE,KAAK;QAC/C,OAAO,UAAU,QAAQ,KAAK,GAAG;IACnC;IAEA,IAAI,eAAe,SAAS;QAC1B,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,IAAI,OAAO;QACT,qBACE,8OAAC;YAAI,WAAU;;8BACb,8OAAC;oBAAI,WAAU;8BACZ;;;;;;8BAEH,8OAAC,4JAAA,CAAA,UAAI;oBACH,MAAK;oBACL,WAAU;8BACX;;;;;;;;;;;;IAKP;IAEA,IAAI,CAAC,KAAK;QACR,qBACE,8OAAC;YAAI,WAAU;sBACb,cAAA,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA0B;;;;;;kCACxC,8OAAC;wBAAE,WAAU;kCAA8C;;;;;;kCAG3D,8OAAC,4JAAA,CAAA,UAAI;wBACH,MAAK;wBACL,WAAU;kCACX;;;;;;;;;;;;;;;;;IAMT;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,eAAe,EAAE,GAAG,KAAK,CAAC;gCACjC,WAAU;0CACX;;;;;;0CAGD,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAK;gCACL,WAAU;0CACX;;;;;;;;;;;;;;;;;;0BAML,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;;kDACC,8OAAC;wCAAG,WAAU;kDAA2B,IAAI,KAAK;;;;;;kDAClD,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAK,WAAU;0DACb,IAAI,QAAQ;;;;;;0DAEf,8OAAC;gDAAK,WAAU;0DACb,IAAI,cAAc,CAAC,OAAO,CAAC,KAAK;;;;;;4CAElC,IAAI,MAAM,kBACT,8OAAC;gDAAK,WAAU;;oDACb,IAAI,MAAM,CAAC,GAAG;oDAAC;oDAAI,IAAI,MAAM,CAAC,GAAG;oDAAC;oDAAE,IAAI,MAAM,CAAC,QAAQ;;;;;;;;;;;;;;;;;;;0CAKhE,8OAAC;gCAAI,WAAU;;oCACZ,IAAI,SAAS,kBACZ,8OAAC;wCAAK,WAAU;kDAAoH;;;;;;kDAItI,8OAAC;wCAAK,WAAU;;4CAAiD;4CACpD,IAAI,SAAS,CAAC,kBAAkB;;;;;;;;;;;;;;;;;;;kCAKjD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAE,WAAU;8CAAuB,IAAI,WAAW;;;;;;;;;;;;;;;;;kCAIvD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAG,WAAU;0CACX,IAAI,YAAY,CAAC,GAAG,CAAC,CAAC,aAAqB,sBAC1C,8OAAC;kDAAgB;uCAAR;;;;;;;;;;;;;;;;kCAKf,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;0CACZ,IAAI,QAAQ,CAAC,GAAG,CAAC,CAAC,0BACjB,8OAAC;wCAAoB,WAAU;;0DAC7B,8OAAC;gDAAI,OAAM;gDAA6B,WAAU;gDAA8B,SAAQ;gDAAY,MAAK;0DACvG,cAAA,8OAAC;oDAAK,UAAS;oDAAU,GAAE;oDAAwI,UAAS;;;;;;;;;;;0DAE9K,8OAAC;0DAAM,gBAAgB;;;;;;;uCAJf;;;;;;;;;;;;;;;;oBAUf,IAAI,IAAI,IAAI,IAAI,IAAI,CAAC,MAAM,GAAG,mBAC7B,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAG,WAAU;0CAA6B;;;;;;0CAC3C,8OAAC;gCAAI,WAAU;0CACZ,IAAI,IAAI,CAAC,GAAG,CAAC,CAAC,KAAa,sBAC1B,8OAAC;wCAEC,WAAU;kDAET;uCAHI;;;;;;;;;;;;;;;;kCAUf,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAA6B;;;;;;sDAC3C,8OAAC;4CAAE,WAAU;;gDACV,IAAI,YAAY,CAAC,MAAM;gDAAC;gDAAa,IAAI,YAAY,CAAC,MAAM,KAAK,IAAI,MAAM;;;;;;;;;;;;;8CAGhF,8OAAC,4JAAA,CAAA,UAAI;oCACH,MAAM,CAAC,6BAA6B,EAAE,IAAI;oCAC1C,WAAU;8CACX;;;;;;;;;;;;;;;;;;;;;;;0BAOP,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAG,WAAU;kCAA6B;;;;;;kCAE3C,8OAAC;wBAAI,WAAU;;4BACZ,IAAI,SAAS,IAAI,IAAI,YAAY,IAAI,IAAI,SAAS,IAAI,YAAY,iBACjE,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAA8D;;;;;;sDAG5E,8OAAC;4CAAE,WAAU;;gDAA0C;gDACzB,IAAI,YAAY,CAAC,kBAAkB;;;;;;;sDAEjE,8OAAC,4JAAA,CAAA,UAAI;4CACH,MAAM,CAAC,eAAe,EAAE,GAAG,QAAQ,CAAC;4CACpC,WAAU;sDACX;;;;;;;;;;;;;;;;qDAML,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,eAAe,EAAE,GAAG,QAAQ,CAAC;gCACpC,WAAU;0CACX;;;;;;0CAKH,8OAAC,4JAAA,CAAA,UAAI;gCACH,MAAM,CAAC,MAAM,EAAE,IAAI;gCACnB,WAAU;0CACX;;;;;;0CAID,8OAAC;gCACC,SAAS,IAAM,qBAAqB;gCACpC,WAAU;0CACX;;;;;;;;;;;;oBAKF,mCACC,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAyB;;;;;;8CACvC,8OAAC;oCAAE,WAAU;8CAA8C;;;;;;8CAG3D,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CACC,SAAS,IAAM,qBAAqB;4CACpC,WAAU;sDACX;;;;;;sDAGD,8OAAC;4CACC,SAAS;4CACT,UAAU;4CACV,WAAU;sDAET,WAAW,gBAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAS9C", "debugId": null}}, {"offset": {"line": 820, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/node_modules/next/navigation.js"], "sourcesContent": ["module.exports = require('./dist/client/components/navigation')\n"], "names": [], "mappings": "AAAA,OAAO,OAAO", "ignoreList": [0], "debugId": null}}]}