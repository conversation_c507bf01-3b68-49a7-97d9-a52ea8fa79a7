"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { doc, collection, addDoc, getDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { jobBenefits, MIN_BENEFITS_REQUIRED } from "@/data/jobBenefits";
import { JobRequirements } from "@/types";
import StructuredRequirementsForm from "@/components/employer/StructuredRequirementsForm";
import { countries } from "@/data/countries";

// Form validation schema
const jobFormSchema = z.object({
  title: z.string().min(5, "Job title must be at least 5 characters"),
  description: z.string().min(50, "Job description must be at least 50 characters"),
  location: z.string().min(2, "Location is required"),
  employmentType: z.enum(["full-time", "part-time", "contract", "temporary", "internship"]),
  salaryMin: z.coerce.number().min(0, "Minimum salary cannot be negative").optional(),
  salaryMax: z.coerce.number().min(0, "Maximum salary cannot be negative").optional(),
  salaryCurrency: z.string().min(1, "Currency is required"),
});

type JobFormValues = z.infer<typeof jobFormSchema>;

export default function NewJobPage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [employerData, setEmployerData] = useState<any>(null);
  const [selectedBenefits, setSelectedBenefits] = useState<string[]>([]);
  const [benefitsError, setBenefitsError] = useState<string | null>(null);
  const [structuredRequirements, setStructuredRequirements] = useState<JobRequirements>({});

  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<JobFormValues>({
    resolver: zodResolver(jobFormSchema),
    defaultValues: {
      title: "",
      description: "",
      location: "",
      employmentType: "full-time",
      salaryMin: undefined,
      salaryMax: undefined,
      salaryCurrency: "EUR",
    },
  });

  useEffect(() => {
    // If not authenticated or not an employer, redirect to auth page
    if (!authLoading && (!user || user.role !== "employer")) {
      router.push("/auth");
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    const fetchEmployerData = async () => {
      if (!user) return;

      try {
        const employerDoc = await getDoc(doc(db, "employers", user.uid));

        if (employerDoc.exists()) {
          setEmployerData(employerDoc.data());
        } else {
          // If employer profile doesn't exist, redirect to profile creation
          router.push("/employer/profile");
        }
      } catch (error) {
        console.error("Error fetching employer data:", error);
      } finally {
        setLoading(false);
      }
    };

    if (user && user.role === "employer") {
      fetchEmployerData();
    }
  }, [user, router]);

  const toggleBenefit = (benefitId: string) => {
    setSelectedBenefits(prev => {
      if (prev.includes(benefitId)) {
        return prev.filter(id => id !== benefitId);
      } else {
        return [...prev, benefitId];
      }
    });

    // Clear error if enough benefits are selected
    if (selectedBenefits.length >= MIN_BENEFITS_REQUIRED - 1) {
      setBenefitsError(null);
    }
  };

  const onSubmit = async (data: JobFormValues) => {
    if (selectedBenefits.length < MIN_BENEFITS_REQUIRED) {
      setBenefitsError(`Please select at least ${MIN_BENEFITS_REQUIRED} benefits`);
      return;
    }

    if (!user) return;

    setSaving(true);
    setError(null);

    try {
      // Process form data
      console.log("User data:", user);
      console.log("Employer data:", employerData);
      const jobData = {
        title: data.title,
        description: data.description,
        location: data.location,
        employmentType: data.employmentType,
        salary: data.salaryMin || data.salaryMax ? {
          min: data.salaryMin || 0,
          max: data.salaryMax || 0,
          currency: data.salaryCurrency,
        } : null,
        benefits: selectedBenefits,
        employerId: user.uid,
        companyName: employerData?.companyName || "",
        companyLogo: employerData?.logoURL || null,
        isPremium: false,
        applications: [],
        structuredRequirements: structuredRequirements,
        createdAt: new Date(),
        updatedAt: new Date(),
      };

      // Save to Firestore
      console.log("Creating job with data:", jobData);
      const docRef = await addDoc(collection(db, "jobs"), jobData);
      console.log("Job created with ID:", docRef.id);

      // Redirect to job detail page
      router.push(`/employer/jobs/${docRef.id}`);
    } catch (error: any) {
      console.error("Error creating job:", error);
      setError(error.message || "Failed to create job. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto py-12">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Post a New Job</h1>
        <Link
          href="/employer/dashboard"
          className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
        >
          Back to Dashboard
        </Link>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded-md">
          {error}
        </div>
      )}

      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8">
        <form onSubmit={handleSubmit(onSubmit)}>
          <div className="mb-6">
            <label htmlFor="title" className="block text-sm font-medium mb-1">
              Job Title <span className="text-red-500">*</span>
            </label>
            <input
              id="title"
              {...register("title")}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              placeholder="e.g., Senior Electrician, Software Developer"
            />
            {errors.title && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.title.message}</p>
            )}
          </div>

          <div className="mb-6">
            <label htmlFor="description" className="block text-sm font-medium mb-1">
              Job Description <span className="text-red-500">*</span>
            </label>
            <textarea
              id="description"
              {...register("description")}
              rows={6}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              placeholder="Provide a detailed description of the job, responsibilities, and what the candidate will be doing"
            ></textarea>
            {errors.description && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.description.message}</p>
            )}
          </div>

          <div className="mb-6">
            <StructuredRequirementsForm
              value={structuredRequirements}
              onChange={setStructuredRequirements}
            />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label htmlFor="location" className="block text-sm font-medium mb-1">
                Location <span className="text-red-500">*</span>
              </label>
              <input
                id="location"
                {...register("location")}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                placeholder="e.g., Berlin, Germany"
              />
              {errors.location && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.location.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="employmentType" className="block text-sm font-medium mb-1">
                Employment Type <span className="text-red-500">*</span>
              </label>
              <select
                id="employmentType"
                {...register("employmentType")}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              >
                <option value="full-time">Full-time</option>
                <option value="part-time">Part-time</option>
                <option value="contract">Contract</option>
                <option value="temporary">Temporary</option>
                <option value="internship">Internship</option>
              </select>
              {errors.employmentType && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.employmentType.message}</p>
              )}
            </div>
          </div>

          <div className="mb-6">
            <h3 className="text-lg font-medium mb-2">Salary Range (Optional)</h3>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              <div>
                <label htmlFor="salaryMin" className="block text-sm font-medium mb-1">
                  Minimum
                </label>
                <input
                  id="salaryMin"
                  type="number"
                  {...register("salaryMin")}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                  placeholder="e.g., 30000"
                />
                {errors.salaryMin && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.salaryMin.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="salaryMax" className="block text-sm font-medium mb-1">
                  Maximum
                </label>
                <input
                  id="salaryMax"
                  type="number"
                  {...register("salaryMax")}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                  placeholder="e.g., 50000"
                />
                {errors.salaryMax && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.salaryMax.message}</p>
                )}
              </div>

              <div>
                <label htmlFor="salaryCurrency" className="block text-sm font-medium mb-1">
                  Currency <span className="text-red-500">*</span>
                </label>
                <input
                  id="salaryCurrency"
                  {...register("salaryCurrency")}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md bg-neutral-100 dark:bg-neutral-800"
                  value="EUR"
                  readOnly
                />
                {errors.salaryCurrency && (
                  <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.salaryCurrency.message}</p>
                )}
              </div>
            </div>
          </div>



          <div className="mb-8">
            <h3 className="text-lg font-medium mb-2">
              Job Benefits <span className="text-red-500">*</span>
            </h3>
            <p className="text-sm text-neutral-600 dark:text-neutral-400 mb-4">
              Select at least {MIN_BENEFITS_REQUIRED} benefits that your company offers for this position.
              These will be used to match with candidate preferences.
            </p>

            {benefitsError && (
              <p className="mb-4 text-sm text-red-600 dark:text-red-400">{benefitsError}</p>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {jobBenefits.map((benefit) => (
                <div
                  key={benefit.id}
                  className={`p-3 rounded-md border cursor-pointer transition-all ${
                    selectedBenefits.includes(benefit.id)
                      ? "border-neutral-900 dark:border-white bg-neutral-50 dark:bg-neutral-700"
                      : "border-neutral-200 dark:border-neutral-700 hover:border-neutral-400 dark:hover:border-neutral-500"
                  }`}
                  onClick={() => toggleBenefit(benefit.id)}
                >
                  <div className="flex items-center">
                    <div className={`w-5 h-5 rounded-sm border flex items-center justify-center mr-3 ${
                      selectedBenefits.includes(benefit.id)
                        ? "bg-neutral-900 dark:bg-white border-neutral-900 dark:border-white"
                        : "border-neutral-400 dark:border-neutral-500"
                    }`}>
                      {selectedBenefits.includes(benefit.id) && (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-white dark:text-neutral-900" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </div>
                    <div>
                      <h4 className="font-medium">{benefit.label}</h4>
                      <p className="text-xs text-neutral-500 dark:text-neutral-400">{benefit.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={saving}
              className={`px-6 py-3 rounded-md font-medium ${
                saving
                  ? "bg-neutral-700 text-white cursor-wait"
                  : "bg-neutral-900 hover:bg-neutral-800 text-white"
              }`}
            >
              {saving ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Creating Job...
                </>
              ) : (
                "Post Job"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
