{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/data/positions.ts"], "sourcesContent": ["import { Position } from \"@/types\";\n\n// Primjer standardiziranih pozicija na više jezika\nexport const positions: Position[] = [\n  {\n    id: \"electrician\",\n    translations: {\n      de: \"Elektriker\",\n      en: \"Electrician\",\n      bs: \"Elek<PERSON><PERSON><PERSON>\",\n      hr: \"Elektri<PERSON><PERSON>\",\n      sr: \"Električar\",\n      me: \"Električ<PERSON>\",\n      mk: \"Електричар\",\n      al: \"Elektricist\"\n    },\n    category: \"construction\",\n    skills: [\"electrical-wiring\", \"electrical-maintenance\", \"electrical-installation\"]\n  },\n  {\n    id: \"plumber\",\n    translations: {\n      de: \"<PERSON><PERSON><PERSON><PERSON>\",\n      en: \"Plumber\",\n      bs: \"Vodoinstalater\",\n      hr: \"Vodoinstalater\",\n      sr: \"Vodoinstalater\",\n      me: \"Vodoinstalater\",\n      mk: \"Водоводџија\",\n      al: \"Hidraulik\"\n    },\n    category: \"construction\",\n    skills: [\"plumbing\", \"pipe-fitting\", \"water-systems\"]\n  },\n  {\n    id: \"carpenter\",\n    translations: {\n      de: \"Tischler\",\n      en: \"Carpenter\",\n      bs: \"Stolar\",\n      hr: \"Stolar\",\n      sr: \"Stolar\",\n      me: \"Stolar\",\n      mk: \"Столар\",\n      al: \"Marangoz\"\n    },\n    category: \"construction\",\n    skills: [\"woodworking\", \"furniture-making\", \"carpentry\"]\n  },\n  {\n    id: \"welder\",\n    translations: {\n      de: \"Schweißer\",\n      en: \"Welder\",\n      bs: \"Zavarivač\",\n      hr: \"Zavarivač\",\n      sr: \"Zavarivač\",\n      me: \"Zavarivač\",\n      mk: \"Заварувач\",\n      al: \"Saldator\"\n    },\n    category: \"manufacturing\",\n    skills: [\"welding\", \"metal-fabrication\", \"blueprint-reading\"]\n  },\n  {\n    id: \"chef\",\n    translations: {\n      de: \"Koch\",\n      en: \"Chef\",\n      bs: \"Kuhar\",\n      hr: \"Kuhar\",\n      sr: \"Kuvar\",\n      me: \"Kuvar\",\n      mk: \"Готвач\",\n      al: \"Kuzhinier\"\n    },\n    category: \"hospitality\",\n    skills: [\"cooking\", \"food-preparation\", \"menu-planning\"]\n  },\n  {\n    id: \"waiter\",\n    translations: {\n      de: \"Kellner\",\n      en: \"Waiter\",\n      bs: \"Konobar\",\n      hr: \"Konobar\",\n      sr: \"Konobar\",\n      me: \"Konobar\",\n      mk: \"Келнер\",\n      al: \"Kamarier\"\n    },\n    category: \"hospitality\",\n    skills: [\"customer-service\", \"food-service\", \"order-taking\"]\n  },\n  {\n    id: \"nurse\",\n    translations: {\n      de: \"Krankenpfleger\",\n      en: \"Nurse\",\n      bs: \"Medicinska sestra/tehničar\",\n      hr: \"Medicinska sestra/tehničar\",\n      sr: \"Medicinska sestra/tehničar\",\n      me: \"Medicinska sestra/tehničar\",\n      mk: \"Медицинска сестра/техничар\",\n      al: \"Infermier\"\n    },\n    category: \"healthcare\",\n    skills: [\"patient-care\", \"medical-assistance\", \"health-monitoring\"]\n  },\n  {\n    id: \"caregiver\",\n    translations: {\n      de: \"Pflegekraft\",\n      en: \"Caregiver\",\n      bs: \"Njegovatelj\",\n      hr: \"Njegovatelj\",\n      sr: \"Negovatelj\",\n      me: \"Njegovatelj\",\n      mk: \"Негувател\",\n      al: \"Kujdestar\"\n    },\n    category: \"healthcare\",\n    skills: [\"elderly-care\", \"personal-care\", \"medication-management\"]\n  },\n  {\n    id: \"driver\",\n    translations: {\n      de: \"Fahrer\",\n      en: \"Driver\",\n      bs: \"Vozač\",\n      hr: \"Vozač\",\n      sr: \"Vozač\",\n      me: \"Vozač\",\n      mk: \"Возач\",\n      al: \"Shofer\"\n    },\n    category: \"transportation\",\n    skills: [\"driving\", \"vehicle-maintenance\", \"route-planning\"]\n  },\n  {\n    id: \"software-developer\",\n    translations: {\n      de: \"Softwareentwickler\",\n      en: \"Software Developer\",\n      bs: \"Programer\",\n      hr: \"Programer\",\n      sr: \"Programer\",\n      me: \"Programer\",\n      mk: \"Програмер\",\n      al: \"Zhvillues Softueri\"\n    },\n    category: \"it\",\n    skills: [\"programming\", \"software-development\", \"problem-solving\"]\n  },\n  {\n    id: \"accountant\",\n    translations: {\n      de: \"Buchhalter\",\n      en: \"Accountant\",\n      bs: \"Računovođa\",\n      hr: \"Računovođa\",\n      sr: \"Računovođa\",\n      me: \"Računovođa\",\n      mk: \"Сметководител\",\n      al: \"Kontabilist\"\n    },\n    category: \"finance\",\n    skills: [\"accounting\", \"bookkeeping\", \"financial-reporting\"]\n  },\n  {\n    id: \"teacher\",\n    translations: {\n      de: \"Lehrer\",\n      en: \"Teacher\",\n      bs: \"Nastavnik\",\n      hr: \"Nastavnik\",\n      sr: \"Nastavnik\",\n      me: \"Nastavnik\",\n      mk: \"Наставник\",\n      al: \"Mësues\"\n    },\n    category: \"education\",\n    skills: [\"teaching\", \"curriculum-development\", \"student-assessment\"]\n  },\n  {\n    id: \"sales-representative\",\n    translations: {\n      de: \"Vertriebsmitarbeiter\",\n      en: \"Sales Representative\",\n      bs: \"Prodajni predstavnik\",\n      hr: \"Prodajni predstavnik\",\n      sr: \"Prodajni predstavnik\",\n      me: \"Prodajni predstavnik\",\n      mk: \"Продажен претставник\",\n      al: \"Përfaqësues Shitjesh\"\n    },\n    category: \"sales\",\n    skills: [\"sales\", \"customer-relations\", \"negotiation\"]\n  },\n  {\n    id: \"warehouse-worker\",\n    translations: {\n      de: \"Lagerarbeiter\",\n      en: \"Warehouse Worker\",\n      bs: \"Skladištar\",\n      hr: \"Skladištar\",\n      sr: \"Magacioner\",\n      me: \"Magacioner\",\n      mk: \"Магационер\",\n      al: \"Punëtor Magazinimi\"\n    },\n    category: \"logistics\",\n    skills: [\"inventory-management\", \"forklift-operation\", \"order-picking\"]\n  },\n  {\n    id: \"cleaner\",\n    translations: {\n      de: \"Reinigungskraft\",\n      en: \"Cleaner\",\n      bs: \"Čistač\",\n      hr: \"Čistač\",\n      sr: \"Čistač\",\n      me: \"Čistač\",\n      mk: \"Хигиеничар\",\n      al: \"Pastrues\"\n    },\n    category: \"facility-services\",\n    skills: [\"cleaning\", \"sanitation\", \"housekeeping\"]\n  }\n];\n"], "names": [], "mappings": ";;;AAGO,MAAM,YAAwB;IACnC;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAqB;YAA0B;SAA0B;IACpF;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAY;YAAgB;SAAgB;IACvD;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAe;YAAoB;SAAY;IAC1D;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAW;YAAqB;SAAoB;IAC/D;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAW;YAAoB;SAAgB;IAC1D;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAoB;YAAgB;SAAe;IAC9D;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAgB;YAAsB;SAAoB;IACrE;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAgB;YAAiB;SAAwB;IACpE;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAW;YAAuB;SAAiB;IAC9D;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAe;YAAwB;SAAkB;IACpE;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAc;YAAe;SAAsB;IAC9D;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAY;YAA0B;SAAqB;IACtE;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAS;YAAsB;SAAc;IACxD;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAwB;YAAsB;SAAgB;IACzE;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAY;YAAc;SAAe;IACpD;CACD", "debugId": null}}, {"offset": {"line": 306, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/data/studyFields.ts"], "sourcesContent": ["import { StudyField } from \"@/types\";\n\n// Standardized study fields in multiple languages\nexport const studyFields: StudyField[] = [\n  {\n    id: \"electrical-engineering\",\n    translations: {\n      de: \"Elektrotechnik\",\n      en: \"Electrical Engineering\",\n      bs: \"Elektrotehnika\",\n      hr: \"Elektrotehnika\",\n      sr: \"Elektrotehnika\",\n      me: \"Elektrotehn<PERSON>\",\n      mk: \"Електротехника\",\n      al: \"Inxhinieri Elektrike\"\n    },\n    category: \"engineering\"\n  },\n  {\n    id: \"mechanical-engineering\",\n    translations: {\n      de: \"Maschinenbau\",\n      en: \"Mechanical Engineering\",\n      bs: \"Mašinstvo\",\n      hr: \"St<PERSON>jarst<PERSON>\",\n      sr: \"<PERSON><PERSON>inst<PERSON>\",\n      me: \"<PERSON><PERSON>instvo\",\n      mk: \"Машинство\",\n      al: \"Inxhinieri Mekanike\"\n    },\n    category: \"engineering\"\n  },\n  {\n    id: \"automotive-mechanic\",\n    translations: {\n      de: \"Kfz-Mechaniker\",\n      en: \"Automotive Mechanic\",\n      bs: \"Automehaničar\",\n      hr: \"Autome<PERSON><PERSON>ar\",\n      sr: \"Automehaničar\",\n      me: \"Automehaničar\",\n      mk: \"Автомеханичар\",\n      al: \"Mekanik Automjetesh\"\n    },\n    category: \"vocational\"\n  },\n  {\n    id: \"automotive-painter\",\n    translations: {\n      de: \"Kfz-Lackierer\",\n      en: \"Automotive Painter\",\n      bs: \"Autolakirer\",\n      hr: \"Autolakirer\",\n      sr: \"Autolakirer\",\n      me: \"Autolakirer\",\n      mk: \"Автолакер\",\n      al: \"Bojaxhi Automjetesh\"\n    },\n    category: \"vocational\"\n  },\n  {\n    id: \"carpentry\",\n    translations: {\n      de: \"Tischlerei\",\n      en: \"Carpentry\",\n      bs: \"Stolarstvo\",\n      hr: \"Stolarstvo\",\n      sr: \"Stolarstvo\",\n      me: \"Stolarstvo\",\n      mk: \"Столарство\",\n      al: \"Marangozi\"\n    },\n    category: \"vocational\"\n  },\n  {\n    id: \"plumbing\",\n    translations: {\n      de: \"Sanitärinstallation\",\n      en: \"Plumbing\",\n      bs: \"Vodoinstalaterstvo\",\n      hr: \"Vodoinstalaterstvo\",\n      sr: \"Vodoinstalaterstvo\",\n      me: \"Vodoinstalaterstvo\",\n      mk: \"Водоинсталатерство\",\n      al: \"Hidraulikë\"\n    },\n    category: \"vocational\"\n  },\n  {\n    id: \"welding\",\n    translations: {\n      de: \"Schweißen\",\n      en: \"Welding\",\n      bs: \"Zavarivanje\",\n      hr: \"Zavarivanje\",\n      sr: \"Zavarivanje\",\n      me: \"Zavarivanje\",\n      mk: \"Заварување\",\n      al: \"Saldim\"\n    },\n    category: \"vocational\"\n  },\n  {\n    id: \"culinary-arts\",\n    translations: {\n      de: \"Kochkunst\",\n      en: \"Culinary Arts\",\n      bs: \"Kuharstvo\",\n      hr: \"Kuharstvo\",\n      sr: \"Kuvarstvo\",\n      me: \"Kuvarstvo\",\n      mk: \"Готварство\",\n      al: \"Arti Kulinar\"\n    },\n    category: \"hospitality\"\n  },\n  {\n    id: \"nursing\",\n    translations: {\n      de: \"Krankenpflege\",\n      en: \"Nursing\",\n      bs: \"Sestrinstvo\",\n      hr: \"Sestrinstvo\",\n      sr: \"Sestrinstvo\",\n      me: \"Sestrinstvo\",\n      mk: \"Медицинска нега\",\n      al: \"Infermieri\"\n    },\n    category: \"healthcare\"\n  },\n  {\n    id: \"general-high-school\",\n    translations: {\n      de: \"Allgemeine Hochschulreife\",\n      en: \"General High School\",\n      bs: \"Gimnazija\",\n      hr: \"Gimnazija\",\n      sr: \"Gimnazija\",\n      me: \"Gimnazija\",\n      mk: \"Гимназија\",\n      al: \"Gjimnaz\"\n    },\n    category: \"general-education\"\n  }\n];\n"], "names": [], "mappings": ";;;AAGO,MAAM,cAA4B;IACvC;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;Q<PERSON><PERSON>,cAAc;Y<PERSON><PERSON>,IAAI;Y<PERSON><PERSON>,IAAI;<PERSON><PERSON><PERSON>,IAAI;Y<PERSON><PERSON>,IAAI;YACJ,IAAI;<PERSON><PERSON><PERSON>,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;IACZ;CACD", "debugId": null}}, {"offset": {"line": 460, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/data/driverLicenses.ts"], "sourcesContent": ["export interface DriverLicense {\n  id: string;\n  name: string;\n  description: string;\n  translations: {\n    [languageCode: string]: {\n      name: string;\n      description: string;\n    };\n  };\n  category: \"car\" | \"motorcycle\" | \"truck\" | \"bus\" | \"special\" | \"none\";\n}\n\nexport const driverLicenses: DriverLicense[] = [\n  {\n    id: \"none\",\n    name: \"No License\",\n    description: \"No driver's license\",\n    translations: {\n      en: {\n        name: \"No License\",\n        description: \"No driver's license\"\n      },\n      de: {\n        name: \"Kein Führerschein\",\n        description: \"Kein Führerschein vorhanden\"\n      },\n      bs: {\n        name: \"<PERSON><PERSON> vozačke\",\n        description: \"Bez vozačke dozvole\"\n      }\n    },\n    category: \"none\"\n  },\n  {\n    id: \"am\",\n    name: \"AM\",\n    description: \"Mopeds and light quadricycles\",\n    translations: {\n      en: {\n        name: \"AM\",\n        description: \"Mopeds and light quadricycles\"\n      },\n      de: {\n        name: \"AM\",\n        description: \"Mopeds und leichte Vierradfahrzeuge\"\n      },\n      bs: {\n        name: \"AM\",\n        description: \"Mopedi i laki četverocikli\"\n      }\n    },\n    category: \"motorcycle\"\n  },\n  {\n    id: \"a1\",\n    name: \"<PERSON>\",\n    description: \"Light motorcycles\",\n    translations: {\n      en: {\n        name: \"A1\",\n        description: \"Light motorcycles\"\n      },\n      de: {\n        name: \"A1\",\n        description: \"Leichte Krafträder\"\n      },\n      bs: {\n        name: \"A1\",\n        description: \"Laki motocikli\"\n      }\n    },\n    category: \"motorcycle\"\n  },\n  {\n    id: \"a2\",\n    name: \"A2\",\n    description: \"Medium motorcycles\",\n    translations: {\n      en: {\n        name: \"A2\",\n        description: \"Medium motorcycles\"\n      },\n      de: {\n        name: \"A2\",\n        description: \"Mittlere Krafträder\"\n      },\n      bs: {\n        name: \"A2\",\n        description: \"Srednji motocikli\"\n      }\n    },\n    category: \"motorcycle\"\n  },\n  {\n    id: \"a\",\n    name: \"A\",\n    description: \"Motorcycles\",\n    translations: {\n      en: {\n        name: \"A\",\n        description: \"Motorcycles\"\n      },\n      de: {\n        name: \"A\",\n        description: \"Krafträder\"\n      },\n      bs: {\n        name: \"A\",\n        description: \"Motocikli\"\n      }\n    },\n    category: \"motorcycle\"\n  },\n  {\n    id: \"b1\",\n    name: \"B1\",\n    description: \"Quadricycles\",\n    translations: {\n      en: {\n        name: \"B1\",\n        description: \"Quadricycles\"\n      },\n      de: {\n        name: \"B1\",\n        description: \"Vierradfahrzeuge\"\n      },\n      bs: {\n        name: \"B1\",\n        description: \"Četverocikli\"\n      }\n    },\n    category: \"car\"\n  },\n  {\n    id: \"b\",\n    name: \"B\",\n    description: \"Cars and light vans\",\n    translations: {\n      en: {\n        name: \"B\",\n        description: \"Cars and light vans\"\n      },\n      de: {\n        name: \"B\",\n        description: \"Pkw und leichte Nutzfahrzeuge\"\n      },\n      bs: {\n        name: \"B\",\n        description: \"Automobili i laka dostavna vozila\"\n      }\n    },\n    category: \"car\"\n  },\n  {\n    id: \"be\",\n    name: \"BE\",\n    description: \"Cars with trailer\",\n    translations: {\n      en: {\n        name: \"BE\",\n        description: \"Cars with trailer\"\n      },\n      de: {\n        name: \"BE\",\n        description: \"Pkw mit Anhänger\"\n      },\n      bs: {\n        name: \"BE\",\n        description: \"Automobili s prikolicom\"\n      }\n    },\n    category: \"car\"\n  },\n  {\n    id: \"c1\",\n    name: \"C1\",\n    description: \"Medium-sized vehicles\",\n    translations: {\n      en: {\n        name: \"C1\",\n        description: \"Medium-sized vehicles\"\n      },\n      de: {\n        name: \"C1\",\n        description: \"Mittelgroße Fahrzeuge\"\n      },\n      bs: {\n        name: \"C1\",\n        description: \"Vozila srednje veličine\"\n      }\n    },\n    category: \"truck\"\n  },\n  {\n    id: \"c1e\",\n    name: \"C1E\",\n    description: \"Medium-sized vehicles with trailer\",\n    translations: {\n      en: {\n        name: \"C1E\",\n        description: \"Medium-sized vehicles with trailer\"\n      },\n      de: {\n        name: \"C1E\",\n        description: \"Mittelgroße Fahrzeuge mit Anhänger\"\n      },\n      bs: {\n        name: \"C1E\",\n        description: \"Vozila srednje veličine s prikolicom\"\n      }\n    },\n    category: \"truck\"\n  },\n  {\n    id: \"c\",\n    name: \"C\",\n    description: \"Large goods vehicles\",\n    translations: {\n      en: {\n        name: \"C\",\n        description: \"Large goods vehicles\"\n      },\n      de: {\n        name: \"C\",\n        description: \"Schwere Nutzfahrzeuge\"\n      },\n      bs: {\n        name: \"C\",\n        description: \"Teretna vozila\"\n      }\n    },\n    category: \"truck\"\n  },\n  {\n    id: \"ce\",\n    name: \"CE\",\n    description: \"Large goods vehicles with trailer\",\n    translations: {\n      en: {\n        name: \"CE\",\n        description: \"Large goods vehicles with trailer\"\n      },\n      de: {\n        name: \"CE\",\n        description: \"Schwere Nutzfahrzeuge mit Anhänger\"\n      },\n      bs: {\n        name: \"CE\",\n        description: \"Teretna vozila s prikolicom\"\n      }\n    },\n    category: \"truck\"\n  },\n  {\n    id: \"d1\",\n    name: \"D1\",\n    description: \"Minibuses\",\n    translations: {\n      en: {\n        name: \"D1\",\n        description: \"Minibuses\"\n      },\n      de: {\n        name: \"D1\",\n        description: \"Kleinbusse\"\n      },\n      bs: {\n        name: \"D1\",\n        description: \"Minibusevi\"\n      }\n    },\n    category: \"bus\"\n  },\n  {\n    id: \"d1e\",\n    name: \"D1E\",\n    description: \"Minibuses with trailer\",\n    translations: {\n      en: {\n        name: \"D1E\",\n        description: \"Minibuses with trailer\"\n      },\n      de: {\n        name: \"D1E\",\n        description: \"Kleinbusse mit Anhänger\"\n      },\n      bs: {\n        name: \"D1E\",\n        description: \"Minibusevi s prikolicom\"\n      }\n    },\n    category: \"bus\"\n  },\n  {\n    id: \"d\",\n    name: \"D\",\n    description: \"Buses\",\n    translations: {\n      en: {\n        name: \"D\",\n        description: \"Buses\"\n      },\n      de: {\n        name: \"D\",\n        description: \"Busse\"\n      },\n      bs: {\n        name: \"D\",\n        description: \"Autobusi\"\n      }\n    },\n    category: \"bus\"\n  },\n  {\n    id: \"de\",\n    name: \"DE\",\n    description: \"Buses with trailer\",\n    translations: {\n      en: {\n        name: \"DE\",\n        description: \"Buses with trailer\"\n      },\n      de: {\n        name: \"DE\",\n        description: \"Busse mit Anhänger\"\n      },\n      bs: {\n        name: \"DE\",\n        description: \"Autobusi s prikolicom\"\n      }\n    },\n    category: \"bus\"\n  }\n];\n\n// Group licenses by category for easier display\nexport const driverLicensesByCategory = {\n  none: driverLicenses.filter(license => license.category === \"none\"),\n  car: driverLicenses.filter(license => license.category === \"car\"),\n  motorcycle: driverLicenses.filter(license => license.category === \"motorcycle\"),\n  truck: driverLicenses.filter(license => license.category === \"truck\"),\n  bus: driverLicenses.filter(license => license.category === \"bus\"),\n  special: driverLicenses.filter(license => license.category === \"special\"),\n};\n\n// Helper function to get the highest license category\nexport function getHighestLicenseCategory(licenses: string[]): string {\n  if (!licenses || licenses.length === 0) return \"none\";\n  \n  // Priority order (highest to lowest)\n  const priorityOrder = [\n    \"de\", \"d\", \"d1e\", \"d1\",\n    \"ce\", \"c\", \"c1e\", \"c1\",\n    \"be\", \"b\", \"b1\",\n    \"a\", \"a2\", \"a1\", \"am\",\n    \"none\"\n  ];\n  \n  // Find the license with the highest priority\n  let highestPriority = Number.MAX_SAFE_INTEGER;\n  let highestLicense = \"none\";\n  \n  for (const license of licenses) {\n    const priority = priorityOrder.indexOf(license.toLowerCase());\n    if (priority !== -1 && priority < highestPriority) {\n      highestPriority = priority;\n      highestLicense = license.toLowerCase();\n    }\n  }\n  \n  return highestLicense;\n}\n"], "names": [], "mappings": ";;;;;AAaO,MAAM,iBAAkC;IAC7C;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;IACA;QACE,IAAI;QACJ,MAAM;QACN,aAAa;QACb,cAAc;YACZ,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;YACA,IAAI;gBACF,MAAM;gBACN,aAAa;YACf;QACF;QACA,UAAU;IACZ;CACD;AAGM,MAAM,2BAA2B;IACtC,MAAM,eAAe,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAC5D,KAAK,eAAe,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAC3D,YAAY,eAAe,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAClE,OAAO,eAAe,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAC7D,KAAK,eAAe,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;IAC3D,SAAS,eAAe,MAAM,CAAC,CAAA,UAAW,QAAQ,QAAQ,KAAK;AACjE;AAGO,SAAS,0BAA0B,QAAkB;IAC1D,IAAI,CAAC,YAAY,SAAS,MAAM,KAAK,GAAG,OAAO;IAE/C,qCAAqC;IACrC,MAAM,gBAAgB;QACpB;QAAM;QAAK;QAAO;QAClB;QAAM;QAAK;QAAO;QAClB;QAAM;QAAK;QACX;QAAK;QAAM;QAAM;QACjB;KACD;IAED,6CAA6C;IAC7C,IAAI,kBAAkB,OAAO,gBAAgB;IAC7C,IAAI,iBAAiB;IAErB,KAAK,MAAM,WAAW,SAAU;QAC9B,MAAM,WAAW,cAAc,OAAO,CAAC,QAAQ,WAAW;QAC1D,IAAI,aAAa,CAAC,KAAK,WAAW,iBAAiB;YACjD,kBAAkB;YAClB,iBAAiB,QAAQ,WAAW;QACtC;IACF;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 837, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/data/jobBenefits.ts"], "sourcesContent": ["// Job benefits/features that employers can select when creating a job\n// and candidates can select as preferences\n\nexport const jobBenefits = [\n  {\n    id: \"flexible_hours\",\n    label: \"Flexible Working Hours\",\n    description: \"Flexible start and end times or the ability to work remotely\"\n  },\n  {\n    id: \"remote_work\",\n    label: \"Remote Work Options\",\n    description: \"Possibility to work remotely part-time or full-time\"\n  },\n  {\n    id: \"health_insurance\",\n    label: \"Health Insurance\",\n    description: \"Comprehensive health insurance coverage\"\n  },\n  {\n    id: \"paid_vacation\",\n    label: \"Paid Vacation\",\n    description: \"Generous paid time off and vacation days\"\n  },\n  {\n    id: \"professional_development\",\n    label: \"Professional Development\",\n    description: \"Training, courses, and career advancement opportunities\"\n  },\n  {\n    id: \"retirement_plan\",\n    label: \"Retirement Plan\",\n    description: \"Company pension or retirement savings plan\"\n  },\n  {\n    id: \"relocation_assistance\",\n    label: \"Relocation Assistance\",\n    description: \"Help with moving expenses and finding accommodation\"\n  },\n  {\n    id: \"accommodation_provided_free\",\n    label: \"Free Accommodation Provided\",\n    description: \"Accommodation is provided and fully paid by the employer\"\n  },\n  {\n    id: \"accommodation_provided_paid\",\n    label: \"Accommodation Provided (Paid)\",\n    description: \"Accommodation is arranged by the employer but paid by the employee\"\n  },\n  {\n    id: \"accommodation_not_provided\",\n    label: \"Accommodation Not Provided\",\n    description: \"Employee must arrange their own accommodation\"\n  },\n  {\n    id: \"accommodation_assistance\",\n    label: \"Accommodation Finding Assistance\",\n    description: \"Employer assists in finding accommodation but does not provide it directly\"\n  },\n  {\n    id: \"language_courses\",\n    label: \"Language Courses\",\n    description: \"Free or subsidized language training\"\n  },\n  {\n    id: \"childcare\",\n    label: \"Childcare Support\",\n    description: \"On-site childcare or childcare subsidies\"\n  },\n  {\n    id: \"gym_membership\",\n    label: \"Gym Membership\",\n    description: \"Free or discounted gym or wellness program\"\n  },\n  {\n    id: \"company_car\",\n    label: \"Company Car\",\n    description: \"Company vehicle or car allowance\"\n  },\n  {\n    id: \"meal_allowance\",\n    label: \"Meal Allowance\",\n    description: \"Free or subsidized meals or meal vouchers\"\n  },\n  {\n    id: \"performance_bonus\",\n    label: \"Performance Bonuses\",\n    description: \"Financial bonuses based on performance\"\n  },\n  {\n    id: \"housing_allowance\",\n    label: \"Housing Allowance\",\n    description: \"Assistance with housing costs\"\n  },\n  {\n    id: \"public_transport\",\n    label: \"Public Transport Subsidy\",\n    description: \"Free or subsidized public transportation\"\n  }\n];\n\n// Minimum number of benefits that must be selected when creating a job\nexport const MIN_BENEFITS_REQUIRED = 3;\n"], "names": [], "mappings": "AAAA,sEAAsE;AACtE,2CAA2C;;;;;AAEpC,MAAM,cAAc;IACzB;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;IACA;QACE,IAAI;QACJ,OAAO;QACP,aAAa;IACf;CACD;AAGM,MAAM,wBAAwB", "debugId": null}}, {"offset": {"line": 950, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/data/countries.ts"], "sourcesContent": ["import { Country } from \"@/types\";\n\nexport const countries: Country[] = [\n  {\n    id: \"de\",\n    name: \"Germany\",\n    code: \"DE\",\n    flagUrl: \"/flags/de.svg\",\n    region: \"eu\"\n  },\n  {\n    id: \"at\",\n    name: \"Austria\",\n    code: \"AT\",\n    flagUrl: \"/flags/at.svg\",\n    region: \"eu\"\n  },\n  {\n    id: \"hr\",\n    name: \"Croatia\",\n    code: \"HR\",\n    flagUrl: \"/flags/hr.svg\",\n    region: \"eu\"\n  },\n  {\n    id: \"si\",\n    name: \"Slovenia\",\n    code: \"SI\",\n    flagUrl: \"/flags/si.svg\",\n    region: \"eu\"\n  },\n  {\n    id: \"ba\",\n    name: \"Bosnia and Herzegovina\",\n    code: \"BA\",\n    flagUrl: \"/flags/ba.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"rs\",\n    name: \"Serbia\",\n    code: \"RS\",\n    flagUrl: \"/flags/rs.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"me\",\n    name: \"Montenegro\",\n    code: \"ME\",\n    flagUrl: \"/flags/me.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"mk\",\n    name: \"North Macedonia\",\n    code: \"MK\",\n    flagUrl: \"/flags/mk.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"al\",\n    name: \"Albania\",\n    code: \"AL\",\n    flagUrl: \"/flags/al.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"bg\",\n    name: \"Bulgaria\",\n    code: \"BG\",\n    flagUrl: \"/flags/bg.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"ro\",\n    name: \"Romania\",\n    code: \"RO\",\n    flagUrl: \"/flags/ro.svg\",\n    region: \"balkans\"\n  },\n  {\n    id: \"gr\",\n    name: \"Greece\",\n    code: \"GR\",\n    flagUrl: \"/flags/gr.svg\",\n    region: \"balkans\"\n  }\n];\n"], "names": [], "mappings": ";;;AAEO,MAAM,YAAuB;IAClC;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;IACA;QACE,IAAI;QACJ,MAAM;QACN,MAAM;QACN,SAAS;QACT,QAAQ;IACV;CACD", "debugId": null}}, {"offset": {"line": 1048, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/utils/migrateData.ts"], "sourcesContent": ["import { collection, doc, setDoc, getDocs } from \"firebase/firestore\";\nimport { db } from \"@/lib/firebase\";\nimport { positions } from \"@/data/positions\";\nimport { studyFields } from \"@/data/studyFields\";\nimport { driverLicenses } from \"@/data/driverLicenses\";\nimport { jobBenefits } from \"@/data/jobBenefits\";\nimport { countries } from \"@/data/countries\";\n\ninterface MigrationResult {\n  success: boolean;\n  message: string;\n  details?: any;\n}\n\nexport async function migrateStaticDataToFirestore(): Promise<MigrationResult> {\n  try {\n    console.log(\"Starting data migration to Firestore...\");\n\n    // Migrate positions\n    await migrateCollection(\"positions\", positions);\n    console.log(\"Positions migrated successfully\");\n\n    // Migrate study fields\n    await migrateCollection(\"studyFields\", studyFields);\n    console.log(\"Study fields migrated successfully\");\n\n    // Migrate driver licenses\n    await migrateCollection(\"driverLicenses\", driverLicenses);\n    console.log(\"Driver licenses migrated successfully\");\n\n    // Migrate job benefits\n    await migrateCollection(\"benefits\", jobBenefits);\n    console.log(\"Job benefits migrated successfully\");\n\n    // Migrate countries\n    await migrateCollection(\"countries\", countries);\n    console.log(\"Countries migrated successfully\");\n\n    return {\n      success: true,\n      message: \"All data migrated successfully to Firestore\",\n    };\n  } catch (error) {\n    console.error(\"Error during migration:\", error);\n    return {\n      success: false,\n      message: \"Migration failed\",\n      details: error,\n    };\n  }\n}\n\nasync function migrateCollection(collectionName: string, data: any[]) {\n  // Check if collection already has data\n  const snapshot = await getDocs(collection(db, collectionName));\n  \n  if (!snapshot.empty) {\n    console.log(`Collection ${collectionName} already has data, skipping...`);\n    return;\n  }\n\n  // Migrate data\n  for (const item of data) {\n    const docRef = doc(db, collectionName, item.id);\n    await setDoc(docRef, {\n      ...item,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n    });\n  }\n}\n\nexport async function checkMigrationStatus(): Promise<{\n  positions: number;\n  studyFields: number;\n  driverLicenses: number;\n  benefits: number;\n  countries: number;\n}> {\n  const collections = [\"positions\", \"studyFields\", \"driverLicenses\", \"benefits\", \"countries\"];\n  const status: any = {};\n\n  for (const collectionName of collections) {\n    const snapshot = await getDocs(collection(db, collectionName));\n    status[collectionName] = snapshot.size;\n  }\n\n  return status;\n}\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAQO,eAAe;IACpB,IAAI;QACF,QAAQ,GAAG,CAAC;QAEZ,oBAAoB;QACpB,MAAM,kBAAkB,aAAa,2HAAA,CAAA,YAAS;QAC9C,QAAQ,GAAG,CAAC;QAEZ,uBAAuB;QACvB,MAAM,kBAAkB,eAAe,6HAAA,CAAA,cAAW;QAClD,QAAQ,GAAG,CAAC;QAEZ,0BAA0B;QAC1B,MAAM,kBAAkB,kBAAkB,gIAAA,CAAA,iBAAc;QACxD,QAAQ,GAAG,CAAC;QAEZ,uBAAuB;QACvB,MAAM,kBAAkB,YAAY,6HAAA,CAAA,cAAW;QAC/C,QAAQ,GAAG,CAAC;QAEZ,oBAAoB;QACpB,MAAM,kBAAkB,aAAa,2HAAA,CAAA,YAAS;QAC9C,QAAQ,GAAG,CAAC;QAEZ,OAAO;YACL,SAAS;YACT,SAAS;QACX;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,2BAA2B;QACzC,OAAO;YACL,SAAS;YACT,SAAS;YACT,SAAS;QACX;IACF;AACF;AAEA,eAAe,kBAAkB,cAAsB,EAAE,IAAW;IAClE,uCAAuC;IACvC,MAAM,WAAW,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE;IAE9C,IAAI,CAAC,SAAS,KAAK,EAAE;QACnB,QAAQ,GAAG,CAAC,CAAC,WAAW,EAAE,eAAe,8BAA8B,CAAC;QACxE;IACF;IAEA,eAAe;IACf,KAAK,MAAM,QAAQ,KAAM;QACvB,MAAM,SAAS,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,gBAAgB,KAAK,EAAE;QAC9C,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,QAAQ;YACnB,GAAG,IAAI;YACP,WAAW,IAAI;YACf,WAAW,IAAI;QACjB;IACF;AACF;AAEO,eAAe;IAOpB,MAAM,cAAc;QAAC;QAAa;QAAe;QAAkB;QAAY;KAAY;IAC3F,MAAM,SAAc,CAAC;IAErB,KAAK,MAAM,kBAAkB,YAAa;QACxC,MAAM,WAAW,MAAM,CAAA,GAAA,sKAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,aAAU,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE;QAC9C,MAAM,CAAC,eAAe,GAAG,SAAS,IAAI;IACxC;IAEA,OAAO;AACT", "debugId": null}}, {"offset": {"line": 1139, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/components/admin/DataMigration.tsx"], "sourcesContent": ["import { useState, useEffect } from \"react\";\nimport { migrateStaticDataToFirestore, checkMigrationStatus } from \"@/utils/migrateData\";\n\nexport default function DataMigration() {\n  const [migrating, setMigrating] = useState(false);\n  const [migrationResult, setMigrationResult] = useState<any>(null);\n  const [migrationStatus, setMigrationStatus] = useState<any>(null);\n  const [loading, setLoading] = useState(true);\n\n  useEffect(() => {\n    checkStatus();\n  }, []);\n\n  const checkStatus = async () => {\n    try {\n      setLoading(true);\n      const status = await checkMigrationStatus();\n      setMigrationStatus(status);\n    } catch (error) {\n      console.error(\"Error checking migration status:\", error);\n    } finally {\n      setLoading(false);\n    }\n  };\n\n  const handleMigration = async () => {\n    setMigrating(true);\n    setMigrationResult(null);\n\n    try {\n      const result = await migrateStaticDataToFirestore();\n      setMigrationResult(result);\n      \n      if (result.success) {\n        // Refresh status after successful migration\n        await checkStatus();\n      }\n    } catch (error) {\n      setMigrationResult({\n        success: false,\n        message: \"Migration failed with error\",\n        details: error,\n      });\n    } finally {\n      setMigrating(false);\n    }\n  };\n\n  if (loading) {\n    return (\n      <div className=\"bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6\">\n        <div className=\"animate-pulse\">\n          <div className=\"h-4 bg-neutral-200 dark:bg-neutral-700 rounded w-1/4 mb-4\"></div>\n          <div className=\"h-4 bg-neutral-200 dark:bg-neutral-700 rounded w-3/4 mb-2\"></div>\n          <div className=\"h-4 bg-neutral-200 dark:bg-neutral-700 rounded w-1/2\"></div>\n        </div>\n      </div>\n    );\n  }\n\n  const totalItems = migrationStatus ? Object.values(migrationStatus).reduce((a: any, b: any) => a + b, 0) : 0;\n  const hasData = totalItems > 0;\n\n  return (\n    <div className=\"bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6\">\n      <h3 className=\"text-lg font-medium mb-4\">Data Migration</h3>\n      \n      <div className=\"mb-6\">\n        <h4 className=\"text-md font-medium mb-2\">Current Status</h4>\n        <div className=\"grid grid-cols-2 md:grid-cols-3 gap-4\">\n          <div className=\"bg-neutral-50 dark:bg-neutral-900 p-3 rounded\">\n            <div className=\"text-sm text-neutral-600 dark:text-neutral-400\">Positions</div>\n            <div className=\"text-lg font-semibold\">{migrationStatus?.positions || 0}</div>\n          </div>\n          <div className=\"bg-neutral-50 dark:bg-neutral-900 p-3 rounded\">\n            <div className=\"text-sm text-neutral-600 dark:text-neutral-400\">Study Fields</div>\n            <div className=\"text-lg font-semibold\">{migrationStatus?.studyFields || 0}</div>\n          </div>\n          <div className=\"bg-neutral-50 dark:bg-neutral-900 p-3 rounded\">\n            <div className=\"text-sm text-neutral-600 dark:text-neutral-400\">Driver Licenses</div>\n            <div className=\"text-lg font-semibold\">{migrationStatus?.driverLicenses || 0}</div>\n          </div>\n          <div className=\"bg-neutral-50 dark:bg-neutral-900 p-3 rounded\">\n            <div className=\"text-sm text-neutral-600 dark:text-neutral-400\">Benefits</div>\n            <div className=\"text-lg font-semibold\">{migrationStatus?.benefits || 0}</div>\n          </div>\n          <div className=\"bg-neutral-50 dark:bg-neutral-900 p-3 rounded\">\n            <div className=\"text-sm text-neutral-600 dark:text-neutral-400\">Countries</div>\n            <div className=\"text-lg font-semibold\">{migrationStatus?.countries || 0}</div>\n          </div>\n        </div>\n      </div>\n\n      {!hasData && (\n        <div className=\"mb-6\">\n          <div className=\"bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-4\">\n            <div className=\"flex\">\n              <div className=\"flex-shrink-0\">\n                <svg className=\"h-5 w-5 text-yellow-400\" viewBox=\"0 0 20 20\" fill=\"currentColor\">\n                  <path fillRule=\"evenodd\" d=\"M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z\" clipRule=\"evenodd\" />\n                </svg>\n              </div>\n              <div className=\"ml-3\">\n                <h3 className=\"text-sm font-medium text-yellow-800 dark:text-yellow-200\">\n                  No data found in Firestore\n                </h3>\n                <div className=\"mt-2 text-sm text-yellow-700 dark:text-yellow-300\">\n                  <p>\n                    Your application is currently using static data files. To enable full admin panel functionality,\n                    you need to migrate this data to Firestore.\n                  </p>\n                </div>\n              </div>\n            </div>\n          </div>\n        </div>\n      )}\n\n      <div className=\"flex items-center justify-between\">\n        <div>\n          <button\n            onClick={handleMigration}\n            disabled={migrating}\n            className={`px-4 py-2 rounded-md ${\n              migrating\n                ? \"bg-neutral-300 dark:bg-neutral-700 cursor-not-allowed\"\n                : \"bg-blue-600 hover:bg-blue-700 text-white\"\n            }`}\n          >\n            {migrating ? \"Migrating...\" : hasData ? \"Re-migrate Data\" : \"Migrate Data to Firestore\"}\n          </button>\n          \n          <button\n            onClick={checkStatus}\n            disabled={loading}\n            className=\"ml-3 px-4 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md hover:bg-neutral-50 dark:hover:bg-neutral-700\"\n          >\n            Refresh Status\n          </button>\n        </div>\n      </div>\n\n      {migrationResult && (\n        <div className={`mt-4 p-4 rounded-md ${\n          migrationResult.success\n            ? \"bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300\"\n            : \"bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300\"\n        }`}>\n          <p className=\"font-medium\">{migrationResult.message}</p>\n          {migrationResult.details && (\n            <pre className=\"mt-2 text-xs overflow-auto\">\n              {JSON.stringify(migrationResult.details, null, 2)}\n            </pre>\n          )}\n        </div>\n      )}\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;;;AAEe,SAAS;;IACtB,MAAM,CAAC,WAAW,aAAa,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAC3C,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC5D,MAAM,CAAC,iBAAiB,mBAAmB,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAO;IAC5D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IAEvC,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;mCAAE;YACR;QACF;kCAAG,EAAE;IAEL,MAAM,cAAc;QAClB,IAAI;YACF,WAAW;YACX,MAAM,SAAS,MAAM,CAAA,GAAA,8HAAA,CAAA,uBAAoB,AAAD;YACxC,mBAAmB;QACrB,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,oCAAoC;QACpD,SAAU;YACR,WAAW;QACb;IACF;IAEA,MAAM,kBAAkB;QACtB,aAAa;QACb,mBAAmB;QAEnB,IAAI;YACF,MAAM,SAAS,MAAM,CAAA,GAAA,8HAAA,CAAA,+BAA4B,AAAD;YAChD,mBAAmB;YAEnB,IAAI,OAAO,OAAO,EAAE;gBAClB,4CAA4C;gBAC5C,MAAM;YACR;QACF,EAAE,OAAO,OAAO;YACd,mBAAmB;gBACjB,SAAS;gBACT,SAAS;gBACT,SAAS;YACX;QACF,SAAU;YACR,aAAa;QACf;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;kCACf,6LAAC;wBAAI,WAAU;;;;;;;;;;;;;;;;;IAIvB;IAEA,MAAM,aAAa,kBAAkB,OAAO,MAAM,CAAC,iBAAiB,MAAM,CAAC,CAAC,GAAQ,IAAW,IAAI,GAAG,KAAK;IAC3G,MAAM,UAAU,aAAa;IAE7B,qBACE,6LAAC;QAAI,WAAU;;0BACb,6LAAC;gBAAG,WAAU;0BAA2B;;;;;;0BAEzC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAA2B;;;;;;kCACzC,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAiD;;;;;;kDAChE,6LAAC;wCAAI,WAAU;kDAAyB,iBAAiB,aAAa;;;;;;;;;;;;0CAExE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAiD;;;;;;kDAChE,6LAAC;wCAAI,WAAU;kDAAyB,iBAAiB,eAAe;;;;;;;;;;;;0CAE1E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAiD;;;;;;kDAChE,6LAAC;wCAAI,WAAU;kDAAyB,iBAAiB,kBAAkB;;;;;;;;;;;;0CAE7E,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAiD;;;;;;kDAChE,6LAAC;wCAAI,WAAU;kDAAyB,iBAAiB,YAAY;;;;;;;;;;;;0CAEvE,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;kDAAiD;;;;;;kDAChE,6LAAC;wCAAI,WAAU;kDAAyB,iBAAiB,aAAa;;;;;;;;;;;;;;;;;;;;;;;;YAK3E,CAAC,yBACA,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;oBAAI,WAAU;8BACb,cAAA,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;oCAA0B,SAAQ;oCAAY,MAAK;8CAChE,cAAA,6LAAC;wCAAK,UAAS;wCAAU,GAAE;wCAAoN,UAAS;;;;;;;;;;;;;;;;0CAG5P,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAG,WAAU;kDAA2D;;;;;;kDAGzE,6LAAC;wCAAI,WAAU;kDACb,cAAA,6LAAC;sDAAE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAWf,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC;;sCACC,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAW,CAAC,qBAAqB,EAC/B,YACI,0DACA,4CACJ;sCAED,YAAY,iBAAiB,UAAU,oBAAoB;;;;;;sCAG9D,6LAAC;4BACC,SAAS;4BACT,UAAU;4BACV,WAAU;sCACX;;;;;;;;;;;;;;;;;YAMJ,iCACC,6LAAC;gBAAI,WAAW,CAAC,oBAAoB,EACnC,gBAAgB,OAAO,GACnB,wEACA,+DACJ;;kCACA,6LAAC;wBAAE,WAAU;kCAAe,gBAAgB,OAAO;;;;;;oBAClD,gBAAgB,OAAO,kBACtB,6LAAC;wBAAI,WAAU;kCACZ,KAAK,SAAS,CAAC,gBAAgB,OAAO,EAAE,MAAM;;;;;;;;;;;;;;;;;;AAO7D;GA3JwB;KAAA", "debugId": null}}, {"offset": {"line": 1553, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/app/admin/settings/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { doc, getDoc, setDoc } from \"firebase/firestore\";\nimport { db } from \"@/lib/firebase\";\nimport DataMigration from \"@/components/admin/DataMigration\";\n\ninterface PlatformSettings {\n  languages: {\n    enabled: string[];\n    default: string;\n  };\n  registration: {\n    employerApprovalRequired: boolean;\n    candidateEmailVerificationRequired: boolean;\n  };\n  notifications: {\n    adminEmail: string;\n    newEmployerNotification: boolean;\n    newCandidateNotification: boolean;\n    newJobNotification: boolean;\n  };\n  premium: {\n    enabled: boolean;\n    prices: {\n      jobListing: {\n        sevenDays: number;\n        fourteenDays: number;\n        thirtyDays: number;\n      };\n      cvAccess: {\n        tenInvites: number;\n        twentyFiveInvites: number;\n        fiftyInvites: number;\n      };\n    };\n  };\n}\n\nconst defaultSettings: PlatformSettings = {\n  languages: {\n    enabled: [\"en\", \"de\", \"bs\"],\n    default: \"en\",\n  },\n  registration: {\n    employerApprovalRequired: false,\n    candidateEmailVerificationRequired: true,\n  },\n  notifications: {\n    adminEmail: \"\",\n    newEmployerNotification: true,\n    newCandidateNotification: false,\n    newJobNotification: false,\n  },\n  premium: {\n    enabled: true,\n    prices: {\n      jobListing: {\n        sevenDays: 5,\n        fourteenDays: 8,\n        thirtyDays: 15,\n      },\n      cvAccess: {\n        tenInvites: 5,\n        twentyFiveInvites: 10,\n        fiftyInvites: 20,\n      },\n    },\n  },\n};\n\nexport default function AdminSettingsPage() {\n  const [settings, setSettings] = useState<PlatformSettings>(defaultSettings);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchSettings = async () => {\n      try {\n        const settingsDoc = await getDoc(doc(db, \"settings\", \"platform\"));\n        \n        if (settingsDoc.exists()) {\n          setSettings(settingsDoc.data() as PlatformSettings);\n        } else {\n          // If no settings document exists, create one with defaults\n          await setDoc(doc(db, \"settings\", \"platform\"), defaultSettings);\n        }\n      } catch (error) {\n        console.error(\"Error fetching settings:\", error);\n        setError(\"Failed to load settings. Please try again.\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchSettings();\n  }, []);\n\n  const handleSaveSettings = async () => {\n    setSaving(true);\n    setError(null);\n    setSuccess(null);\n\n    try {\n      await setDoc(doc(db, \"settings\", \"platform\"), settings);\n      setSuccess(\"Settings saved successfully\");\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (error) {\n      console.error(\"Error saving settings:\", error);\n      setError(\"Failed to save settings. Please try again.\");\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleLanguageToggle = (language: string) => {\n    setSettings(prev => {\n      const enabled = [...prev.languages.enabled];\n      const index = enabled.indexOf(language);\n      \n      if (index === -1) {\n        enabled.push(language);\n      } else {\n        // Don't allow removing the default language\n        if (language === prev.languages.default) {\n          return prev;\n        }\n        enabled.splice(index, 1);\n      }\n      \n      return {\n        ...prev,\n        languages: {\n          ...prev.languages,\n          enabled,\n        },\n      };\n    });\n  };\n\n  const handleDefaultLanguageChange = (language: string) => {\n    setSettings(prev => {\n      // Make sure the default language is also enabled\n      let enabled = [...prev.languages.enabled];\n      if (!enabled.includes(language)) {\n        enabled.push(language);\n      }\n      \n      return {\n        ...prev,\n        languages: {\n          enabled,\n          default: language,\n        },\n      };\n    });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-full\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <div className=\"flex justify-between items-center mb-6\">\n        <h1 className=\"text-2xl font-bold\">Platform Settings</h1>\n        <button\n          onClick={handleSaveSettings}\n          disabled={saving}\n          className={`px-4 py-2 rounded-md ${\n            saving\n              ? \"bg-neutral-400 cursor-not-allowed\"\n              : \"bg-blue-600 hover:bg-blue-700\"\n          } text-white`}\n        >\n          {saving ? \"Saving...\" : \"Save Settings\"}\n        </button>\n      </div>\n\n      {error && (\n        <div className=\"bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6\">\n          {error}\n        </div>\n      )}\n\n      {success && (\n        <div className=\"bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 p-4 rounded-md mb-6\">\n          {success}\n        </div>\n      )}\n\n      {/* Data Migration */}\n      <div className=\"mb-6\">\n        <DataMigration />\n      </div>\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Language Settings */}\n        <div className=\"bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6\">\n          <h2 className=\"text-lg font-medium mb-4\">Language Settings</h2>\n          \n          <div className=\"mb-4\">\n            <label className=\"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2\">\n              Enabled Languages\n            </label>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id=\"lang-en\"\n                  checked={settings.languages.enabled.includes(\"en\")}\n                  onChange={() => handleLanguageToggle(\"en\")}\n                  className=\"mr-2\"\n                  disabled={settings.languages.default === \"en\"}\n                />\n                <label htmlFor=\"lang-en\" className=\"text-sm\">English</label>\n              </div>\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id=\"lang-de\"\n                  checked={settings.languages.enabled.includes(\"de\")}\n                  onChange={() => handleLanguageToggle(\"de\")}\n                  className=\"mr-2\"\n                  disabled={settings.languages.default === \"de\"}\n                />\n                <label htmlFor=\"lang-de\" className=\"text-sm\">German</label>\n              </div>\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id=\"lang-bs\"\n                  checked={settings.languages.enabled.includes(\"bs\")}\n                  onChange={() => handleLanguageToggle(\"bs\")}\n                  className=\"mr-2\"\n                  disabled={settings.languages.default === \"bs\"}\n                />\n                <label htmlFor=\"lang-bs\" className=\"text-sm\">Bosnian</label>\n              </div>\n            </div>\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2\">\n              Default Language\n            </label>\n            <select\n              value={settings.languages.default}\n              onChange={(e) => handleDefaultLanguageChange(e.target.value)}\n              className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n            >\n              {settings.languages.enabled.map(lang => (\n                <option key={lang} value={lang}>\n                  {lang === \"en\" ? \"English\" : lang === \"de\" ? \"German\" : \"Bosnian\"}\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n\n        {/* Registration Settings */}\n        <div className=\"bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6\">\n          <h2 className=\"text-lg font-medium mb-4\">Registration Settings</h2>\n          \n          <div className=\"space-y-4\">\n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"employer-approval\"\n                checked={settings.registration.employerApprovalRequired}\n                onChange={(e) => setSettings({\n                  ...settings,\n                  registration: {\n                    ...settings.registration,\n                    employerApprovalRequired: e.target.checked,\n                  },\n                })}\n                className=\"mr-2\"\n              />\n              <label htmlFor=\"employer-approval\" className=\"text-sm\">\n                Require manual approval for employer accounts\n              </label>\n            </div>\n            \n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"candidate-verification\"\n                checked={settings.registration.candidateEmailVerificationRequired}\n                onChange={(e) => setSettings({\n                  ...settings,\n                  registration: {\n                    ...settings.registration,\n                    candidateEmailVerificationRequired: e.target.checked,\n                  },\n                })}\n                className=\"mr-2\"\n              />\n              <label htmlFor=\"candidate-verification\" className=\"text-sm\">\n                Require email verification for candidate accounts\n              </label>\n            </div>\n          </div>\n        </div>\n\n        {/* Notification Settings */}\n        <div className=\"bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6\">\n          <h2 className=\"text-lg font-medium mb-4\">Notification Settings</h2>\n          \n          <div className=\"mb-4\">\n            <label htmlFor=\"admin-email\" className=\"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\">\n              Admin Email for Notifications\n            </label>\n            <input\n              type=\"email\"\n              id=\"admin-email\"\n              value={settings.notifications.adminEmail}\n              onChange={(e) => setSettings({\n                ...settings,\n                notifications: {\n                  ...settings.notifications,\n                  adminEmail: e.target.value,\n                },\n              })}\n              className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n              placeholder=\"<EMAIL>\"\n            />\n          </div>\n          \n          <div className=\"space-y-3\">\n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"new-employer\"\n                checked={settings.notifications.newEmployerNotification}\n                onChange={(e) => setSettings({\n                  ...settings,\n                  notifications: {\n                    ...settings.notifications,\n                    newEmployerNotification: e.target.checked,\n                  },\n                })}\n                className=\"mr-2\"\n              />\n              <label htmlFor=\"new-employer\" className=\"text-sm\">\n                Notify admin when a new employer registers\n              </label>\n            </div>\n            \n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"new-candidate\"\n                checked={settings.notifications.newCandidateNotification}\n                onChange={(e) => setSettings({\n                  ...settings,\n                  notifications: {\n                    ...settings.notifications,\n                    newCandidateNotification: e.target.checked,\n                  },\n                })}\n                className=\"mr-2\"\n              />\n              <label htmlFor=\"new-candidate\" className=\"text-sm\">\n                Notify admin when a new candidate registers\n              </label>\n            </div>\n            \n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"new-job\"\n                checked={settings.notifications.newJobNotification}\n                onChange={(e) => setSettings({\n                  ...settings,\n                  notifications: {\n                    ...settings.notifications,\n                    newJobNotification: e.target.checked,\n                  },\n                })}\n                className=\"mr-2\"\n              />\n              <label htmlFor=\"new-job\" className=\"text-sm\">\n                Notify admin when a new job is posted\n              </label>\n            </div>\n          </div>\n        </div>\n\n        {/* Premium Features Settings */}\n        <div className=\"bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6\">\n          <h2 className=\"text-lg font-medium mb-4\">Premium Features</h2>\n          \n          <div className=\"mb-4\">\n            <div className=\"flex items-center mb-4\">\n              <input\n                type=\"checkbox\"\n                id=\"premium-enabled\"\n                checked={settings.premium.enabled}\n                onChange={(e) => setSettings({\n                  ...settings,\n                  premium: {\n                    ...settings.premium,\n                    enabled: e.target.checked,\n                  },\n                })}\n                className=\"mr-2\"\n              />\n              <label htmlFor=\"premium-enabled\" className=\"text-sm font-medium\">\n                Enable premium features\n              </label>\n            </div>\n          </div>\n          \n          {settings.premium.enabled && (\n            <>\n              <h3 className=\"text-md font-medium mb-2\">Job Listing Prices (€)</h3>\n              <div className=\"grid grid-cols-3 gap-4 mb-6\">\n                <div>\n                  <label htmlFor=\"price-7days\" className=\"block text-sm text-neutral-700 dark:text-neutral-300 mb-1\">\n                    7 Days\n                  </label>\n                  <input\n                    type=\"number\"\n                    id=\"price-7days\"\n                    value={settings.premium.prices.jobListing.sevenDays}\n                    onChange={(e) => setSettings({\n                      ...settings,\n                      premium: {\n                        ...settings.premium,\n                        prices: {\n                          ...settings.premium.prices,\n                          jobListing: {\n                            ...settings.premium.prices.jobListing,\n                            sevenDays: Number(e.target.value),\n                          },\n                        },\n                      },\n                    })}\n                    min=\"0\"\n                    step=\"1\"\n                    className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"price-14days\" className=\"block text-sm text-neutral-700 dark:text-neutral-300 mb-1\">\n                    14 Days\n                  </label>\n                  <input\n                    type=\"number\"\n                    id=\"price-14days\"\n                    value={settings.premium.prices.jobListing.fourteenDays}\n                    onChange={(e) => setSettings({\n                      ...settings,\n                      premium: {\n                        ...settings.premium,\n                        prices: {\n                          ...settings.premium.prices,\n                          jobListing: {\n                            ...settings.premium.prices.jobListing,\n                            fourteenDays: Number(e.target.value),\n                          },\n                        },\n                      },\n                    })}\n                    min=\"0\"\n                    step=\"1\"\n                    className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"price-30days\" className=\"block text-sm text-neutral-700 dark:text-neutral-300 mb-1\">\n                    30 Days\n                  </label>\n                  <input\n                    type=\"number\"\n                    id=\"price-30days\"\n                    value={settings.premium.prices.jobListing.thirtyDays}\n                    onChange={(e) => setSettings({\n                      ...settings,\n                      premium: {\n                        ...settings.premium,\n                        prices: {\n                          ...settings.premium.prices,\n                          jobListing: {\n                            ...settings.premium.prices.jobListing,\n                            thirtyDays: Number(e.target.value),\n                          },\n                        },\n                      },\n                    })}\n                    min=\"0\"\n                    step=\"1\"\n                    className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n                  />\n                </div>\n              </div>\n              \n              <h3 className=\"text-md font-medium mb-2\">CV Access Prices (€)</h3>\n              <div className=\"grid grid-cols-3 gap-4\">\n                <div>\n                  <label htmlFor=\"price-10invites\" className=\"block text-sm text-neutral-700 dark:text-neutral-300 mb-1\">\n                    10 Invites\n                  </label>\n                  <input\n                    type=\"number\"\n                    id=\"price-10invites\"\n                    value={settings.premium.prices.cvAccess.tenInvites}\n                    onChange={(e) => setSettings({\n                      ...settings,\n                      premium: {\n                        ...settings.premium,\n                        prices: {\n                          ...settings.premium.prices,\n                          cvAccess: {\n                            ...settings.premium.prices.cvAccess,\n                            tenInvites: Number(e.target.value),\n                          },\n                        },\n                      },\n                    })}\n                    min=\"0\"\n                    step=\"1\"\n                    className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"price-25invites\" className=\"block text-sm text-neutral-700 dark:text-neutral-300 mb-1\">\n                    25 Invites\n                  </label>\n                  <input\n                    type=\"number\"\n                    id=\"price-25invites\"\n                    value={settings.premium.prices.cvAccess.twentyFiveInvites}\n                    onChange={(e) => setSettings({\n                      ...settings,\n                      premium: {\n                        ...settings.premium,\n                        prices: {\n                          ...settings.premium.prices,\n                          cvAccess: {\n                            ...settings.premium.prices.cvAccess,\n                            twentyFiveInvites: Number(e.target.value),\n                          },\n                        },\n                      },\n                    })}\n                    min=\"0\"\n                    step=\"1\"\n                    className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"price-50invites\" className=\"block text-sm text-neutral-700 dark:text-neutral-300 mb-1\">\n                    50 Invites\n                  </label>\n                  <input\n                    type=\"number\"\n                    id=\"price-50invites\"\n                    value={settings.premium.prices.cvAccess.fiftyInvites}\n                    onChange={(e) => setSettings({\n                      ...settings,\n                      premium: {\n                        ...settings.premium,\n                        prices: {\n                          ...settings.premium.prices,\n                          cvAccess: {\n                            ...settings.premium.prices.cvAccess,\n                            fiftyInvites: Number(e.target.value),\n                          },\n                        },\n                      },\n                    })}\n                    min=\"0\"\n                    step=\"1\"\n                    className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n                  />\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;AACA;;;AALA;;;;;AAuCA,MAAM,kBAAoC;IACxC,WAAW;QACT,SAAS;YAAC;YAAM;YAAM;SAAK;QAC3B,SAAS;IACX;IACA,cAAc;QACZ,0BAA0B;QAC1B,oCAAoC;IACtC;IACA,eAAe;QACb,YAAY;QACZ,yBAAyB;QACzB,0BAA0B;QAC1B,oBAAoB;IACtB;IACA,SAAS;QACP,SAAS;QACT,QAAQ;YACN,YAAY;gBACV,WAAW;gBACX,cAAc;gBACd,YAAY;YACd;YACA,UAAU;gBACR,YAAY;gBACZ,mBAAmB;gBACnB,cAAc;YAChB;QACF;IACF;AACF;AAEe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;6DAAgB;oBACpB,IAAI;wBACF,MAAM,cAAc,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,YAAY;wBAErD,IAAI,YAAY,MAAM,IAAI;4BACxB,YAAY,YAAY,IAAI;wBAC9B,OAAO;4BACL,2DAA2D;4BAC3D,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,YAAY,aAAa;wBAChD;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;sCAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,UAAU;QACV,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,YAAY,aAAa;YAC9C,WAAW;YACX,WAAW,IAAM,WAAW,OAAO;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS;QACX,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY,CAAA;YACV,MAAM,UAAU;mBAAI,KAAK,SAAS,CAAC,OAAO;aAAC;YAC3C,MAAM,QAAQ,QAAQ,OAAO,CAAC;YAE9B,IAAI,UAAU,CAAC,GAAG;gBAChB,QAAQ,IAAI,CAAC;YACf,OAAO;gBACL,4CAA4C;gBAC5C,IAAI,aAAa,KAAK,SAAS,CAAC,OAAO,EAAE;oBACvC,OAAO;gBACT;gBACA,QAAQ,MAAM,CAAC,OAAO;YACxB;YAEA,OAAO;gBACL,GAAG,IAAI;gBACP,WAAW;oBACT,GAAG,KAAK,SAAS;oBACjB;gBACF;YACF;QACF;IACF;IAEA,MAAM,8BAA8B,CAAC;QACnC,YAAY,CAAA;YACV,iDAAiD;YACjD,IAAI,UAAU;mBAAI,KAAK,SAAS,CAAC,OAAO;aAAC;YACzC,IAAI,CAAC,QAAQ,QAAQ,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;YAEA,OAAO;gBACL,GAAG,IAAI;gBACP,WAAW;oBACT;oBACA,SAAS;gBACX;YACF;QACF;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,6LAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAW,CAAC,qBAAqB,EAC/B,SACI,sCACA,gCACL,WAAW,CAAC;kCAEZ,SAAS,cAAc;;;;;;;;;;;;YAI3B,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;YAIJ,yBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAKL,6LAAC;gBAAI,WAAU;0BACb,cAAA,6LAAC,+IAAA,CAAA,UAAa;;;;;;;;;;0BAGhB,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAwE;;;;;;kDAGzF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,SAAS,SAAS,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC;wDAC7C,UAAU,IAAM,qBAAqB;wDACrC,WAAU;wDACV,UAAU,SAAS,SAAS,CAAC,OAAO,KAAK;;;;;;kEAE3C,6LAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAAU;;;;;;;;;;;;0DAE/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,SAAS,SAAS,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC;wDAC7C,UAAU,IAAM,qBAAqB;wDACrC,WAAU;wDACV,UAAU,SAAS,SAAS,CAAC,OAAO,KAAK;;;;;;kEAE3C,6LAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAAU;;;;;;;;;;;;0DAE/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,SAAS,SAAS,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC;wDAC7C,UAAU,IAAM,qBAAqB;wDACrC,WAAU;wDACV,UAAU,SAAS,SAAS,CAAC,OAAO,KAAK;;;;;;kEAE3C,6LAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAKnD,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAwE;;;;;;kDAGzF,6LAAC;wCACC,OAAO,SAAS,SAAS,CAAC,OAAO;wCACjC,UAAU,CAAC,IAAM,4BAA4B,EAAE,MAAM,CAAC,KAAK;wCAC3D,WAAU;kDAET,SAAS,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,qBAC9B,6LAAC;gDAAkB,OAAO;0DACvB,SAAS,OAAO,YAAY,SAAS,OAAO,WAAW;+CAD7C;;;;;;;;;;;;;;;;;;;;;;kCASrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,SAAS,SAAS,YAAY,CAAC,wBAAwB;gDACvD,UAAU,CAAC,IAAM,YAAY;wDAC3B,GAAG,QAAQ;wDACX,cAAc;4DACZ,GAAG,SAAS,YAAY;4DACxB,0BAA0B,EAAE,MAAM,CAAC,OAAO;wDAC5C;oDACF;gDACA,WAAU;;;;;;0DAEZ,6LAAC;gDAAM,SAAQ;gDAAoB,WAAU;0DAAU;;;;;;;;;;;;kDAKzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,SAAS,SAAS,YAAY,CAAC,kCAAkC;gDACjE,UAAU,CAAC,IAAM,YAAY;wDAC3B,GAAG,QAAQ;wDACX,cAAc;4DACZ,GAAG,SAAS,YAAY;4DACxB,oCAAoC,EAAE,MAAM,CAAC,OAAO;wDACtD;oDACF;gDACA,WAAU;;;;;;0DAEZ,6LAAC;gDAAM,SAAQ;gDAAyB,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAQlE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,SAAQ;wCAAc,WAAU;kDAAwE;;;;;;kDAG/G,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,OAAO,SAAS,aAAa,CAAC,UAAU;wCACxC,UAAU,CAAC,IAAM,YAAY;gDAC3B,GAAG,QAAQ;gDACX,eAAe;oDACb,GAAG,SAAS,aAAa;oDACzB,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC5B;4CACF;wCACA,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,SAAS,SAAS,aAAa,CAAC,uBAAuB;gDACvD,UAAU,CAAC,IAAM,YAAY;wDAC3B,GAAG,QAAQ;wDACX,eAAe;4DACb,GAAG,SAAS,aAAa;4DACzB,yBAAyB,EAAE,MAAM,CAAC,OAAO;wDAC3C;oDACF;gDACA,WAAU;;;;;;0DAEZ,6LAAC;gDAAM,SAAQ;gDAAe,WAAU;0DAAU;;;;;;;;;;;;kDAKpD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,SAAS,SAAS,aAAa,CAAC,wBAAwB;gDACxD,UAAU,CAAC,IAAM,YAAY;wDAC3B,GAAG,QAAQ;wDACX,eAAe;4DACb,GAAG,SAAS,aAAa;4DACzB,0BAA0B,EAAE,MAAM,CAAC,OAAO;wDAC5C;oDACF;gDACA,WAAU;;;;;;0DAEZ,6LAAC;gDAAM,SAAQ;gDAAgB,WAAU;0DAAU;;;;;;;;;;;;kDAKrD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,SAAS,SAAS,aAAa,CAAC,kBAAkB;gDAClD,UAAU,CAAC,IAAM,YAAY;wDAC3B,GAAG,QAAQ;wDACX,eAAe;4DACb,GAAG,SAAS,aAAa;4DACzB,oBAAoB,EAAE,MAAM,CAAC,OAAO;wDACtC;oDACF;gDACA,WAAU;;;;;;0DAEZ,6LAAC;gDAAM,SAAQ;gDAAU,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAQnD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,SAAS,SAAS,OAAO,CAAC,OAAO;4CACjC,UAAU,CAAC,IAAM,YAAY;oDAC3B,GAAG,QAAQ;oDACX,SAAS;wDACP,GAAG,SAAS,OAAO;wDACnB,SAAS,EAAE,MAAM,CAAC,OAAO;oDAC3B;gDACF;4CACA,WAAU;;;;;;sDAEZ,6LAAC;4CAAM,SAAQ;4CAAkB,WAAU;sDAAsB;;;;;;;;;;;;;;;;;4BAMpE,SAAS,OAAO,CAAC,OAAO,kBACvB;;kDACE,6LAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAc,WAAU;kEAA4D;;;;;;kEAGnG,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,OAAO,SAAS,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS;wDACnD,UAAU,CAAC,IAAM,YAAY;gEAC3B,GAAG,QAAQ;gEACX,SAAS;oEACP,GAAG,SAAS,OAAO;oEACnB,QAAQ;wEACN,GAAG,SAAS,OAAO,CAAC,MAAM;wEAC1B,YAAY;4EACV,GAAG,SAAS,OAAO,CAAC,MAAM,CAAC,UAAU;4EACrC,WAAW,OAAO,EAAE,MAAM,CAAC,KAAK;wEAClC;oEACF;gEACF;4DACF;wDACA,KAAI;wDACJ,MAAK;wDACL,WAAU;;;;;;;;;;;;0DAGd,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAe,WAAU;kEAA4D;;;;;;kEAGpG,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,OAAO,SAAS,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY;wDACtD,UAAU,CAAC,IAAM,YAAY;gEAC3B,GAAG,QAAQ;gEACX,SAAS;oEACP,GAAG,SAAS,OAAO;oEACnB,QAAQ;wEACN,GAAG,SAAS,OAAO,CAAC,MAAM;wEAC1B,YAAY;4EACV,GAAG,SAAS,OAAO,CAAC,MAAM,CAAC,UAAU;4EACrC,cAAc,OAAO,EAAE,MAAM,CAAC,KAAK;wEACrC;oEACF;gEACF;4DACF;wDACA,KAAI;wDACJ,MAAK;wDACL,WAAU;;;;;;;;;;;;0DAGd,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAe,WAAU;kEAA4D;;;;;;kEAGpG,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,OAAO,SAAS,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,UAAU;wDACpD,UAAU,CAAC,IAAM,YAAY;gEAC3B,GAAG,QAAQ;gEACX,SAAS;oEACP,GAAG,SAAS,OAAO;oEACnB,QAAQ;wEACN,GAAG,SAAS,OAAO,CAAC,MAAM;wEAC1B,YAAY;4EACV,GAAG,SAAS,OAAO,CAAC,MAAM,CAAC,UAAU;4EACrC,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;wEACnC;oEACF;gEACF;4DACF;wDACA,KAAI;wDACJ,MAAK;wDACL,WAAU;;;;;;;;;;;;;;;;;;kDAKhB,6LAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAkB,WAAU;kEAA4D;;;;;;kEAGvG,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,OAAO,SAAS,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU;wDAClD,UAAU,CAAC,IAAM,YAAY;gEAC3B,GAAG,QAAQ;gEACX,SAAS;oEACP,GAAG,SAAS,OAAO;oEACnB,QAAQ;wEACN,GAAG,SAAS,OAAO,CAAC,MAAM;wEAC1B,UAAU;4EACR,GAAG,SAAS,OAAO,CAAC,MAAM,CAAC,QAAQ;4EACnC,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;wEACnC;oEACF;gEACF;4DACF;wDACA,KAAI;wDACJ,MAAK;wDACL,WAAU;;;;;;;;;;;;0DAGd,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAkB,WAAU;kEAA4D;;;;;;kEAGvG,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,OAAO,SAAS,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,iBAAiB;wDACzD,UAAU,CAAC,IAAM,YAAY;gEAC3B,GAAG,QAAQ;gEACX,SAAS;oEACP,GAAG,SAAS,OAAO;oEACnB,QAAQ;wEACN,GAAG,SAAS,OAAO,CAAC,MAAM;wEAC1B,UAAU;4EACR,GAAG,SAAS,OAAO,CAAC,MAAM,CAAC,QAAQ;4EACnC,mBAAmB,OAAO,EAAE,MAAM,CAAC,KAAK;wEAC1C;oEACF;gEACF;4DACF;wDACA,KAAI;wDACJ,MAAK;wDACL,WAAU;;;;;;;;;;;;0DAGd,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAkB,WAAU;kEAA4D;;;;;;kEAGvG,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,OAAO,SAAS,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY;wDACpD,UAAU,CAAC,IAAM,YAAY;gEAC3B,GAAG,QAAQ;gEACX,SAAS;oEACP,GAAG,SAAS,OAAO;oEACnB,QAAQ;wEACN,GAAG,SAAS,OAAO,CAAC,MAAM;wEAC1B,UAAU;4EACR,GAAG,SAAS,OAAO,CAAC,MAAM,CAAC,QAAQ;4EACnC,cAAc,OAAO,EAAE,MAAM,CAAC,KAAK;wEACrC;oEACF;gEACF;4DACF;wDACA,KAAI;wDACJ,MAAK;wDACL,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9B;GAxgBwB;KAAA", "debugId": null}}]}