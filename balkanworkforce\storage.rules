rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    // Allow public read access to profile images and company logos
    match /profile-images/{userId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    match /company-logos/{employerId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == employerId;
    }
    
    // Allow authenticated users to upload resumes
    match /resumes/{userId}/{fileName} {
      allow read: if request.auth != null && (
        request.auth.uid == userId || 
        exists(/databases/$(database)/documents/applications/{applicationId}) && 
        get(/databases/$(database)/documents/applications/{applicationId}).data.candidateId == userId && 
        get(/databases/$(database)/documents/applications/{applicationId}).data.employerId == request.auth.uid
      );
      allow write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Default rule - deny all other access
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
