"use client";

import React, { createContext, useContext, useEffect, useState } from "react";
import {
  User,
  Google<PERSON>uthP<PERSON>ider,
  signInWithPopup,
  signOut,
  onAuthStateChanged,
} from "firebase/auth";
import { auth, db } from "@/lib/firebase";
import { doc, getDoc, setDoc } from "firebase/firestore";

type UserRole = "employer" | "candidate" | "admin";

interface UserData {
  uid: string;
  email: string | null;
  role: UserRole | null;
  displayName?: string | null;
  photoURL?: string | null;
  isAdmin?: boolean;
}

interface AuthContextType {
  user: UserData | null;
  loading: boolean;
  signInWithGoogle: (role: UserRole) => Promise<any>;
  logout: () => Promise<void>;
  updateUserRole: (role: UserRole) => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<UserData | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const unsubscribe = onAuthStateChanged(auth, async (firebaseUser) => {
      if (firebaseUser) {
        try {
          // Get user data from Firestore
          const userDoc = await getDoc(doc(db, "users", firebaseUser.uid));
          const userData = userDoc.data();

          // Check if user is an admin
          let isAdmin = false;
          try {
            const adminDoc = await getDoc(doc(db, "admins", firebaseUser.uid));
            isAdmin = adminDoc.exists();
          } catch (error) {
            console.error("Error checking admin status:", error);
          }

          setUser({
            uid: firebaseUser.uid,
            email: firebaseUser.email,
            role: userData?.role || null,
            displayName: firebaseUser.displayName,
            photoURL: firebaseUser.photoURL,
            isAdmin,
          });
        } catch (error) {
          console.error("Error getting user data:", error);
          setUser({
            uid: firebaseUser.uid,
            email: firebaseUser.email,
            role: null,
            displayName: firebaseUser.displayName,
            photoURL: firebaseUser.photoURL,
            isAdmin: false,
          });
        }
      } else {
        setUser(null);
      }
      setLoading(false);
    });

    return () => unsubscribe();
  }, []);

  const signInWithGoogle = async (role: UserRole) => {
    try {
      const provider = new GoogleAuthProvider();
      const result = await signInWithPopup(auth, provider);
      const user = result.user;

      console.log("Google sign-in successful:", user.email);

      // Save user data to Firestore
      await setDoc(
        doc(db, "users", user.uid),
        {
          uid: user.uid,
          email: user.email,
          role: role,
          displayName: user.displayName,
          photoURL: user.photoURL,
          createdAt: new Date(),
        },
        { merge: true }
      );

      console.log("User data saved to Firestore");
      return { success: true };
    } catch (error: any) {
      console.error("Error signing in with Google:", error);
      return {
        success: false,
        error: error.message || "Failed to sign in with Google"
      };
    }
  };

  const updateUserRole = async (role: UserRole) => {
    if (!user) return;

    try {
      await setDoc(
        doc(db, "users", user.uid),
        {
          role: role,
        },
        { merge: true }
      );

      setUser({
        ...user,
        role: role,
      });
    } catch (error) {
      console.error("Error updating user role:", error);
    }
  };

  const logout = async () => {
    try {
      await signOut(auth);
    } catch (error) {
      console.error("Error signing out:", error);
    }
  };

  const value = {
    user,
    loading,
    signInWithGoogle,
    logout,
    updateUserRole,
  };

  return <AuthContext.Provider value={value}>{children}</AuthContext.Provider>;
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
