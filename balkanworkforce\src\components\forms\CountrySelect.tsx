import { useFirestoreData } from "@/hooks/useFirestoreData";
import { Country } from "@/types";
import { useLanguage } from "@/contexts/LanguageContext";

interface CountrySelectProps {
  value: string | string[];
  onChange: (value: string | string[]) => void;
  placeholder?: string;
  className?: string;
  required?: boolean;
  multiple?: boolean;
}

export default function CountrySelect({
  value,
  onChange,
  placeholder = "Select country",
  className = "",
  required = false,
  multiple = false,
}: CountrySelectProps) {
  const { language } = useLanguage();
  const { data: countries, loading, error } = useFirestoreData<Country>("countries");

  if (loading) {
    return (
      <select className={`${className} opacity-50`} disabled>
        <option>Loading countries...</option>
      </select>
    );
  }

  if (error) {
    return (
      <select className={`${className} opacity-50`} disabled>
        <option>Error loading countries</option>
      </select>
    );
  }

  const handleChange = (e: React.ChangeEvent<HTMLSelectElement>) => {
    if (multiple) {
      const selectedValues = Array.from(e.target.selectedOptions, option => option.value);
      onChange(selectedValues);
    } else {
      onChange(e.target.value);
    }
  };

  return (
    <select
      value={value}
      onChange={handleChange}
      className={className}
      required={required}
      multiple={multiple}
    >
      {!multiple && <option value="">{placeholder}</option>}
      {countries.map((country) => (
        <option key={country.id} value={country.id}>
          {country.flag} {country.translations[language] || country.translations.en || country.name}
        </option>
      ))}
    </select>
  );
}
