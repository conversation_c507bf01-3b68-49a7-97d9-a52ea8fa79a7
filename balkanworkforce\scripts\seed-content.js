// Skripta za dodavanje početnih podataka u bazu
const { initializeApp } = require('firebase/app');
const { getFirestore, doc, setDoc, collection, getDocs } = require('firebase/firestore');

// Firebase konfiguracija
const firebaseConfig = {
  apiKey: process.env.NEXT_PUBLIC_FIREBASE_API_KEY,
  authDomain: process.env.NEXT_PUBLIC_FIREBASE_AUTH_DOMAIN,
  projectId: process.env.NEXT_PUBLIC_FIREBASE_PROJECT_ID,
  storageBucket: process.env.NEXT_PUBLIC_FIREBASE_STORAGE_BUCKET,
  messagingSenderId: process.env.NEXT_PUBLIC_FIREBASE_MESSAGING_SENDER_ID,
  appId: process.env.NEXT_PUBLIC_FIREBASE_APP_ID
};

// Inicijalizacija Firebase
const app = initializeApp(firebaseConfig);
const db = getFirestore(app);

// Početni podaci za pozicije
const initialPositions = [
  {
    id: "electrician",
    translations: {
      en: "Electrician",
      de: "Elektriker",
      bs: "Električar"
    },
    category: "construction"
  },
  {
    id: "plumber",
    translations: {
      en: "Plumber",
      de: "Klempner",
      bs: "Vodoinstalater"
    },
    category: "construction"
  },
  {
    id: "carpenter",
    translations: {
      en: "Carpenter",
      de: "Zimmermann",
      bs: "Stolar"
    },
    category: "construction"
  },
  {
    id: "nurse",
    translations: {
      en: "Nurse",
      de: "Krankenschwester",
      bs: "Medicinska sestra"
    },
    category: "healthcare"
  },
  {
    id: "chef",
    translations: {
      en: "Chef",
      de: "Koch",
      bs: "Kuhar"
    },
    category: "hospitality"
  },
  {
    id: "waiter",
    translations: {
      en: "Waiter",
      de: "Kellner",
      bs: "Konobar"
    },
    category: "hospitality"
  },
  {
    id: "driver",
    translations: {
      en: "Driver",
      de: "Fahrer",
      bs: "Vozač"
    },
    category: "transportation"
  },
  {
    id: "software-developer",
    translations: {
      en: "Software Developer",
      de: "Softwareentwickler",
      bs: "Programer"
    },
    category: "it"
  }
];

// Početni podaci za obrazovna polja
const initialStudyFields = [
  {
    id: "electrical-engineering",
    translations: {
      en: "Electrical Engineering",
      de: "Elektrotechnik",
      bs: "Elektrotehnika"
    },
    category: "engineering"
  },
  {
    id: "mechanical-engineering",
    translations: {
      en: "Mechanical Engineering",
      de: "Maschinenbau",
      bs: "Mašinstvo"
    },
    category: "engineering"
  },
  {
    id: "nursing",
    translations: {
      en: "Nursing",
      de: "Krankenpflege",
      bs: "Sestrinstvo"
    },
    category: "healthcare"
  },
  {
    id: "culinary-arts",
    translations: {
      en: "Culinary Arts",
      de: "Kochkunst",
      bs: "Kulinarske vještine"
    },
    category: "vocational"
  },
  {
    id: "computer-science",
    translations: {
      en: "Computer Science",
      de: "Informatik",
      bs: "Računarske nauke"
    },
    category: "it"
  },
  {
    id: "business-administration",
    translations: {
      en: "Business Administration",
      de: "Betriebswirtschaft",
      bs: "Poslovna administracija"
    },
    category: "business"
  }
];

// Početni podaci za vozačke dozvole
const initialDriverLicenses = [
  {
    id: "b",
    translations: {
      en: "Category B (Car)",
      de: "Kategorie B (Auto)",
      bs: "Kategorija B (Automobil)"
    },
    description: {
      en: "Vehicles up to 3.5 tonnes",
      de: "Fahrzeuge bis 3,5 Tonnen",
      bs: "Vozila do 3,5 tone"
    }
  },
  {
    id: "c",
    translations: {
      en: "Category C (Truck)",
      de: "Kategorie C (LKW)",
      bs: "Kategorija C (Kamion)"
    },
    description: {
      en: "Vehicles over 3.5 tonnes",
      de: "Fahrzeuge über 3,5 Tonnen",
      bs: "Vozila preko 3,5 tone"
    }
  },
  {
    id: "d",
    translations: {
      en: "Category D (Bus)",
      de: "Kategorie D (Bus)",
      bs: "Kategorija D (Autobus)"
    },
    description: {
      en: "Vehicles for passenger transport",
      de: "Fahrzeuge für Personenbeförderung",
      bs: "Vozila za prevoz putnika"
    }
  },
  {
    id: "e",
    translations: {
      en: "Category E (Trailer)",
      de: "Kategorie E (Anhänger)",
      bs: "Kategorija E (Prikolica)"
    },
    description: {
      en: "Vehicles with trailers",
      de: "Fahrzeuge mit Anhängern",
      bs: "Vozila s prikolicama"
    }
  }
];

// Početni podaci za benefite
const initialBenefits = [
  {
    id: "accommodation",
    translations: {
      en: "Accommodation Provided",
      de: "Unterkunft gestellt",
      bs: "Osiguran smještaj"
    },
    category: "housing"
  },
  {
    id: "meals",
    translations: {
      en: "Meals Provided",
      de: "Mahlzeiten gestellt",
      bs: "Osigurana ishrana"
    },
    category: "food"
  },
  {
    id: "transport",
    translations: {
      en: "Transport to Work",
      de: "Transport zur Arbeit",
      bs: "Prevoz do posla"
    },
    category: "transportation"
  },
  {
    id: "health-insurance",
    translations: {
      en: "Health Insurance",
      de: "Krankenversicherung",
      bs: "Zdravstveno osiguranje"
    },
    category: "insurance"
  },
  {
    id: "paid-vacation",
    translations: {
      en: "Paid Vacation",
      de: "Bezahlter Urlaub",
      bs: "Plaćeni godišnji odmor"
    },
    category: "time-off"
  },
  {
    id: "language-courses",
    translations: {
      en: "Language Courses",
      de: "Sprachkurse",
      bs: "Kursevi jezika"
    },
    category: "education"
  }
];

// Početni podaci za zemlje
const initialCountries = [
  {
    id: "de",
    name: "Germany",
    translations: {
      en: "Germany",
      de: "Deutschland",
      bs: "Njemačka"
    },
    flag: "🇩🇪"
  },
  {
    id: "at",
    name: "Austria",
    translations: {
      en: "Austria",
      de: "Österreich",
      bs: "Austrija"
    },
    flag: "🇦🇹"
  },
  {
    id: "ch",
    name: "Switzerland",
    translations: {
      en: "Switzerland",
      de: "Schweiz",
      bs: "Švicarska"
    },
    flag: "🇨🇭"
  },
  {
    id: "hr",
    name: "Croatia",
    translations: {
      en: "Croatia",
      de: "Kroatien",
      bs: "Hrvatska"
    },
    flag: "🇭🇷"
  },
  {
    id: "si",
    name: "Slovenia",
    translations: {
      en: "Slovenia",
      de: "Slowenien",
      bs: "Slovenija"
    },
    flag: "🇸🇮"
  },
  {
    id: "ba",
    name: "Bosnia and Herzegovina",
    translations: {
      en: "Bosnia and Herzegovina",
      de: "Bosnien und Herzegowina",
      bs: "Bosna i Hercegovina"
    },
    flag: "🇧🇦"
  }
];

// Funkcija za dodavanje podataka u kolekciju
async function seedCollection(collectionName, data) {
  console.log(`Seeding ${collectionName} collection...`);
  
  // Prvo provjerimo da li već postoje podaci u kolekciji
  const snapshot = await getDocs(collection(db, collectionName));
  if (!snapshot.empty) {
    console.log(`Collection ${collectionName} already has ${snapshot.size} documents. Skipping...`);
    return;
  }
  
  // Dodajemo podatke
  for (const item of data) {
    try {
      await setDoc(doc(db, collectionName, item.id), {
        ...item,
        createdAt: new Date(),
      });
      console.log(`Added ${item.id} to ${collectionName}`);
    } catch (error) {
      console.error(`Error adding ${item.id} to ${collectionName}:`, error);
    }
  }
  
  console.log(`Finished seeding ${collectionName} collection.`);
}

// Glavna funkcija za dodavanje svih podataka
async function seedAllData() {
  try {
    await seedCollection("positions", initialPositions);
    await seedCollection("studyFields", initialStudyFields);
    await seedCollection("driverLicenses", initialDriverLicenses);
    await seedCollection("benefits", initialBenefits);
    await seedCollection("countries", initialCountries);
    
    console.log("All data seeded successfully!");
  } catch (error) {
    console.error("Error seeding data:", error);
  }
}

// Poziv glavne funkcije
seedAllData().then(() => {
  console.log("Seed process completed.");
}).catch(error => {
  console.error("Error in seed process:", error);
});
