import { useState, useEffect } from "react";
import { collection, query, getDocs, where, orderBy, limit, Timestamp } from "firebase/firestore";
import { db } from "@/lib/firebase";

interface AnalyticsData {
  totalUsers: number;
  newUsersThisMonth: number;
  newUsersLastMonth: number;
  userGrowthRate: number;
  totalJobs: number;
  activeJobs: number;
  totalApplications: number;
  applicationConversionRate: number;
  jobsByCountry: { [key: string]: number };
  applicationsByStatus: { [key: string]: number };
  usersByType: { employers: number; candidates: number };
  recentActivity: {
    type: string;
    date: Date;
    description: string;
  }[];
}

export default function AnalyticsDashboard() {
  const [analyticsData, setAnalyticsData] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [timeRange, setTimeRange] = useState<"week" | "month" | "year">("month");

  useEffect(() => {
    const fetchAnalyticsData = async () => {
      try {
        setLoading(true);
        
        // Get date ranges
        const now = new Date();
        const thisMonthStart = new Date(now.getFullYear(), now.getMonth(), 1);
        const lastMonthStart = new Date(now.getFullYear(), now.getMonth() - 1, 1);
        const lastMonthEnd = new Date(now.getFullYear(), now.getMonth(), 0);
        
        // Convert to Firestore Timestamps
        const thisMonthStartTs = Timestamp.fromDate(thisMonthStart);
        const lastMonthStartTs = Timestamp.fromDate(lastMonthStart);
        const lastMonthEndTs = Timestamp.fromDate(lastMonthEnd);
        
        // Fetch total users
        const employersSnapshot = await getDocs(collection(db, "employers"));
        const candidatesSnapshot = await getDocs(collection(db, "candidates"));
        const totalEmployers = employersSnapshot.size;
        const totalCandidates = candidatesSnapshot.size;
        const totalUsers = totalEmployers + totalCandidates;
        
        // Fetch new users this month
        const newEmployersThisMonthQuery = query(
          collection(db, "employers"),
          where("createdAt", ">=", thisMonthStartTs)
        );
        const newCandidatesThisMonthQuery = query(
          collection(db, "candidates"),
          where("createdAt", ">=", thisMonthStartTs)
        );
        const newEmployersThisMonth = (await getDocs(newEmployersThisMonthQuery)).size;
        const newCandidatesThisMonth = (await getDocs(newCandidatesThisMonthQuery)).size;
        const newUsersThisMonth = newEmployersThisMonth + newCandidatesThisMonth;
        
        // Fetch new users last month
        const newEmployersLastMonthQuery = query(
          collection(db, "employers"),
          where("createdAt", ">=", lastMonthStartTs),
          where("createdAt", "<=", lastMonthEndTs)
        );
        const newCandidatesLastMonthQuery = query(
          collection(db, "candidates"),
          where("createdAt", ">=", lastMonthStartTs),
          where("createdAt", "<=", lastMonthEndTs)
        );
        const newEmployersLastMonth = (await getDocs(newEmployersLastMonthQuery)).size;
        const newCandidatesLastMonth = (await getDocs(newCandidatesLastMonthQuery)).size;
        const newUsersLastMonth = newEmployersLastMonth + newCandidatesLastMonth;
        
        // Calculate growth rate
        const userGrowthRate = newUsersLastMonth > 0 
          ? ((newUsersThisMonth - newUsersLastMonth) / newUsersLastMonth) * 100 
          : 100;
        
        // Fetch jobs data
        const jobsSnapshot = await getDocs(collection(db, "jobs"));
        const totalJobs = jobsSnapshot.size;
        
        const activeJobsQuery = query(
          collection(db, "jobs"),
          where("status", "==", "active")
        );
        const activeJobs = (await getDocs(activeJobsQuery)).size;
        
        // Fetch applications data
        const applicationsSnapshot = await getDocs(collection(db, "applications"));
        const totalApplications = applicationsSnapshot.size;
        
        // Calculate application conversion rate
        const applicationConversionRate = totalJobs > 0 
          ? (totalApplications / totalJobs) * 100 
          : 0;
        
        // Get jobs by country
        const jobsByCountry: { [key: string]: number } = {};
        jobsSnapshot.docs.forEach(doc => {
          const country = doc.data().country;
          if (country) {
            jobsByCountry[country] = (jobsByCountry[country] || 0) + 1;
          }
        });
        
        // Get applications by status
        const applicationsByStatus: { [key: string]: number } = {};
        applicationsSnapshot.docs.forEach(doc => {
          const status = doc.data().status;
          if (status) {
            applicationsByStatus[status] = (applicationsByStatus[status] || 0) + 1;
          }
        });
        
        // Get recent activity
        const recentActivity: { type: string; date: Date; description: string }[] = [];
        
        // Recent job postings
        const recentJobsQuery = query(
          collection(db, "jobs"),
          orderBy("createdAt", "desc"),
          limit(5)
        );
        const recentJobs = await getDocs(recentJobsQuery);
        recentJobs.docs.forEach(doc => {
          const jobData = doc.data();
          recentActivity.push({
            type: "job",
            date: jobData.createdAt?.toDate() || new Date(),
            description: `New job posted: ${jobData.title || "Untitled"}`
          });
        });
        
        // Recent applications
        const recentApplicationsQuery = query(
          collection(db, "applications"),
          orderBy("createdAt", "desc"),
          limit(5)
        );
        const recentApplications = await getDocs(recentApplicationsQuery);
        recentApplications.docs.forEach(doc => {
          const appData = doc.data();
          recentActivity.push({
            type: "application",
            date: appData.createdAt?.toDate() || new Date(),
            description: `New application for: ${appData.jobTitle || "Unknown Job"}`
          });
        });
        
        // Recent user registrations
        const recentEmployersQuery = query(
          collection(db, "employers"),
          orderBy("createdAt", "desc"),
          limit(3)
        );
        const recentCandidatesQuery = query(
          collection(db, "candidates"),
          orderBy("createdAt", "desc"),
          limit(3)
        );
        
        const recentEmployers = await getDocs(recentEmployersQuery);
        const recentCandidates = await getDocs(recentCandidatesQuery);
        
        recentEmployers.docs.forEach(doc => {
          const userData = doc.data();
          recentActivity.push({
            type: "employer",
            date: userData.createdAt?.toDate() || new Date(),
            description: `New employer registered: ${userData.companyName || "Unknown Company"}`
          });
        });
        
        recentCandidates.docs.forEach(doc => {
          const userData = doc.data();
          recentActivity.push({
            type: "candidate",
            date: userData.createdAt?.toDate() || new Date(),
            description: `New candidate registered: ${userData.profession || "Unknown"}, ${userData.age || "?"}`
          });
        });
        
        // Sort recent activity by date
        recentActivity.sort((a, b) => b.date.getTime() - a.date.getTime());
        
        // Set analytics data
        setAnalyticsData({
          totalUsers,
          newUsersThisMonth,
          newUsersLastMonth,
          userGrowthRate,
          totalJobs,
          activeJobs,
          totalApplications,
          applicationConversionRate,
          jobsByCountry,
          applicationsByStatus,
          usersByType: {
            employers: totalEmployers,
            candidates: totalCandidates
          },
          recentActivity: recentActivity.slice(0, 10) // Limit to 10 most recent activities
        });
        
      } catch (error) {
        console.error("Error fetching analytics data:", error);
        setError("Failed to load analytics data. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchAnalyticsData();
  }, [timeRange]);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md">
        {error}
      </div>
    );
  }

  if (!analyticsData) {
    return (
      <div className="bg-yellow-50 dark:bg-yellow-900/20 text-yellow-600 dark:text-yellow-400 p-4 rounded-md">
        No analytics data available.
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-xl font-bold">Platform Analytics</h2>
        <div className="flex space-x-2">
          <button
            onClick={() => setTimeRange("week")}
            className={`px-3 py-1 text-sm rounded-md ${
              timeRange === "week"
                ? "bg-blue-600 text-white"
                : "bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300"
            }`}
          >
            Week
          </button>
          <button
            onClick={() => setTimeRange("month")}
            className={`px-3 py-1 text-sm rounded-md ${
              timeRange === "month"
                ? "bg-blue-600 text-white"
                : "bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300"
            }`}
          >
            Month
          </button>
          <button
            onClick={() => setTimeRange("year")}
            className={`px-3 py-1 text-sm rounded-md ${
              timeRange === "year"
                ? "bg-blue-600 text-white"
                : "bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300"
            }`}
          >
            Year
          </button>
        </div>
      </div>

      {/* Key Metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-4">
          <h3 className="text-sm font-medium text-neutral-500 dark:text-neutral-400">Total Users</h3>
          <p className="text-2xl font-bold mt-1">{analyticsData.totalUsers}</p>
          <div className="flex items-center mt-2">
            <span className={`text-xs ${analyticsData.userGrowthRate >= 0 ? "text-green-600 dark:text-green-400" : "text-red-600 dark:text-red-400"}`}>
              {analyticsData.userGrowthRate >= 0 ? "+" : ""}{analyticsData.userGrowthRate.toFixed(1)}%
            </span>
            <span className="text-xs text-neutral-500 dark:text-neutral-400 ml-1">vs last month</span>
          </div>
        </div>

        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-4">
          <h3 className="text-sm font-medium text-neutral-500 dark:text-neutral-400">Active Jobs</h3>
          <p className="text-2xl font-bold mt-1">{analyticsData.activeJobs}</p>
          <div className="flex items-center mt-2">
            <span className="text-xs text-neutral-500 dark:text-neutral-400">
              of {analyticsData.totalJobs} total jobs
            </span>
          </div>
        </div>

        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-4">
          <h3 className="text-sm font-medium text-neutral-500 dark:text-neutral-400">Applications</h3>
          <p className="text-2xl font-bold mt-1">{analyticsData.totalApplications}</p>
          <div className="flex items-center mt-2">
            <span className="text-xs text-neutral-500 dark:text-neutral-400">
              {analyticsData.applicationConversionRate.toFixed(1)}% conversion rate
            </span>
          </div>
        </div>

        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-4">
          <h3 className="text-sm font-medium text-neutral-500 dark:text-neutral-400">User Ratio</h3>
          <p className="text-2xl font-bold mt-1">
            {analyticsData.usersByType.candidates} : {analyticsData.usersByType.employers}
          </p>
          <div className="flex items-center mt-2">
            <span className="text-xs text-neutral-500 dark:text-neutral-400">
              Candidates to Employers
            </span>
          </div>
        </div>
      </div>

      {/* Charts and Detailed Stats */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Jobs by Country */}
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6">
          <h3 className="text-md font-medium mb-4">Jobs by Country</h3>
          <div className="space-y-3">
            {Object.entries(analyticsData.jobsByCountry)
              .sort((a, b) => b[1] - a[1])
              .slice(0, 5)
              .map(([country, count]) => (
                <div key={country} className="flex items-center">
                  <div className="w-32 text-sm">{country.toUpperCase()}</div>
                  <div className="flex-1">
                    <div className="h-2 bg-neutral-100 dark:bg-neutral-700 rounded-full overflow-hidden">
                      <div 
                        className="h-full bg-blue-600 rounded-full" 
                        style={{ width: `${(count / analyticsData.totalJobs) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="w-12 text-right text-sm">{count}</div>
                </div>
              ))}
          </div>
        </div>

        {/* Applications by Status */}
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6">
          <h3 className="text-md font-medium mb-4">Applications by Status</h3>
          <div className="space-y-3">
            {Object.entries(analyticsData.applicationsByStatus)
              .sort((a, b) => b[1] - a[1])
              .map(([status, count]) => (
                <div key={status} className="flex items-center">
                  <div className="w-32 text-sm capitalize">{status}</div>
                  <div className="flex-1">
                    <div className="h-2 bg-neutral-100 dark:bg-neutral-700 rounded-full overflow-hidden">
                      <div 
                        className={`h-full rounded-full ${
                          status === "accepted" ? "bg-green-600" :
                          status === "rejected" ? "bg-red-600" :
                          status === "pending" ? "bg-yellow-600" :
                          status === "interview" ? "bg-purple-600" :
                          "bg-blue-600"
                        }`}
                        style={{ width: `${(count / analyticsData.totalApplications) * 100}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="w-12 text-right text-sm">{count}</div>
                </div>
              ))}
          </div>
        </div>
      </div>

      {/* Recent Activity */}
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6">
        <h3 className="text-md font-medium mb-4">Recent Activity</h3>
        <div className="space-y-4">
          {analyticsData.recentActivity.map((activity, index) => (
            <div key={index} className="flex items-start">
              <div className={`w-8 h-8 rounded-full flex items-center justify-center mr-3 ${
                activity.type === "job" ? "bg-blue-100 text-blue-600 dark:bg-blue-900/20 dark:text-blue-400" :
                activity.type === "application" ? "bg-green-100 text-green-600 dark:bg-green-900/20 dark:text-green-400" :
                activity.type === "employer" ? "bg-purple-100 text-purple-600 dark:bg-purple-900/20 dark:text-purple-400" :
                "bg-yellow-100 text-yellow-600 dark:bg-yellow-900/20 dark:text-yellow-400"
              }`}>
                {activity.type === "job" && (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M6 6V5a3 3 0 013-3h2a3 3 0 013 3v1h2a2 2 0 012 2v3.57A22.952 22.952 0 0110 13a22.95 22.95 0 01-8-1.43V8a2 2 0 012-2h2zm2-1a1 1 0 011-1h2a1 1 0 011 1v1H8V5zm1 5a1 1 0 011-1h.01a1 1 0 110 2H10a1 1 0 01-1-1z" clipRule="evenodd" />
                    <path d="M2 13.692V16a2 2 0 002 2h12a2 2 0 002-2v-2.308A24.974 24.974 0 0110 15c-2.796 0-5.487-.46-8-1.308z" />
                  </svg>
                )}
                {activity.type === "application" && (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4 4a2 2 0 012-2h4.586A2 2 0 0112 2.586L15.414 6A2 2 0 0116 7.414V16a2 2 0 01-2 2H6a2 2 0 01-2-2V4zm2 6a1 1 0 011-1h6a1 1 0 110 2H7a1 1 0 01-1-1zm1 3a1 1 0 100 2h6a1 1 0 100-2H7z" clipRule="evenodd" />
                  </svg>
                )}
                {activity.type === "employer" && (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4 4a2 2 0 012-2h8a2 2 0 012 2v12a1 1 0 01-1 1h-2a1 1 0 01-1-1v-2a1 1 0 00-1-1H7a1 1 0 00-1 1v2a1 1 0 01-1 1H3a1 1 0 01-1-1V4zm3 1h2v2H7V5zm2 4H7v2h2V9zm2-4h2v2h-2V5zm2 4h-2v2h2V9z" clipRule="evenodd" />
                  </svg>
                )}
                {activity.type === "candidate" && (
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M10 9a3 3 0 100-6 3 3 0 000 6zm-7 9a7 7 0 1114 0H3z" clipRule="evenodd" />
                  </svg>
                )}
              </div>
              <div className="flex-1">
                <p className="text-sm">{activity.description}</p>
                <p className="text-xs text-neutral-500 dark:text-neutral-400 mt-1">
                  {activity.date.toLocaleString()}
                </p>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}
