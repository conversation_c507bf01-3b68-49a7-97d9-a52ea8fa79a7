"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { collection, query, getDocs, orderBy, where } from "firebase/firestore";
import { db } from "@/lib/firebase";
import Link from "next/link";
import Image from "next/image";
import { jobBenefits } from "@/data/jobBenefits";
import { countries } from "@/data/countries";
import { positions } from "@/data/positions";
import { degrees } from "@/data/degrees";
import { studyFields } from "@/data/studyFields";
import { driverLicenses } from "@/data/driverLicenses";
import CandidateFilterSidebar, { CandidateFilters } from "@/components/employer/CandidateFilterSidebar";
import { LanguageLevel } from "@/types";
import { compareLanguageLevels } from "@/utils/matchingUtils";

export default function EmployerCandidatesPage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [candidates, setCandidates] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [sidebarOpen, setSidebarOpen] = useState(false);
  const [filters, setFilters] = useState<CandidateFilters>({
    profession: "",
    countries: [],
    germanLanguageLevel: undefined,
    englishLanguageLevel: undefined,
    positions: [],
    benefits: [],
    educationLevels: [],
    degrees: [],
    studyFields: [],
    driverLicense: undefined,
  });

  useEffect(() => {
    // If not authenticated or not an employer, redirect to auth page
    if (!authLoading && (!user || user.role !== "employer")) {
      router.push("/auth");
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    const fetchCandidates = async () => {
      if (!user) return;

      try {
        const candidatesCollection = collection(db, "candidates");
        const candidatesQuery = query(candidatesCollection);

        const querySnapshot = await getDocs(candidatesQuery);
        const candidatesList: any[] = [];

        querySnapshot.forEach((doc) => {
          const data = doc.data();
          candidatesList.push({
            id: doc.id,
            ...data,
            createdAt: data.createdAt?.toDate(),
            updatedAt: data.updatedAt?.toDate(),
          });
        });

        setCandidates(candidatesList);
      } catch (error) {
        console.error("Error fetching candidates:", error);
      } finally {
        setLoading(false);
      }
    };

    if (user && user.role === "employer") {
      fetchCandidates();
    }
  }, [user]);

  const toggleSidebar = () => {
    setSidebarOpen(!sidebarOpen);
  };

  const handleFiltersChange = (newFilters: CandidateFilters) => {
    setFilters(newFilters);
  };

  // Function to reset filters
  const resetFilters = () => {
    setFilters({
      profession: "",
      countries: [],
      germanLanguageLevel: undefined,
      englishLanguageLevel: undefined,
      positions: [],
      benefits: [],
      educationLevels: [],
      degrees: [],
      studyFields: [],
      driverLicense: undefined,
    });
  };

  const filteredCandidates = candidates.filter(candidate => {
    // Filter by profession
    if (filters.profession && !candidate.profession?.toLowerCase().includes(filters.profession.toLowerCase())) {
      return false;
    }

    // Filter by countries
    if (filters.countries.length > 0) {
      const candidateCountries = candidate.desiredCountries || [];
      if (!filters.countries.some(country => candidateCountries.includes(country))) {
        return false;
      }
    }

    // Filter by German language level
    if (filters.germanLanguageLevel && candidate.germanLanguageLevel) {
      if (!compareLanguageLevels(candidate.germanLanguageLevel, filters.germanLanguageLevel)) {
        return false;
      }
    }

    // Filter by English language level
    if (filters.englishLanguageLevel && candidate.englishLanguageLevel) {
      if (!compareLanguageLevels(candidate.englishLanguageLevel, filters.englishLanguageLevel)) {
        return false;
      }
    }

    // Filter by positions (work experience)
    if (filters.positions.length > 0) {
      const candidatePositions = candidate.workExperience?.map((exp: any) => exp.positionId) || [];
      if (!filters.positions.some(position => candidatePositions.includes(position))) {
        return false;
      }
    }

    // Filter by benefits
    if (filters.benefits.length > 0) {
      const candidateBenefits = candidate.benefits || [];
      if (!filters.benefits.some(benefit => candidateBenefits.includes(benefit))) {
        return false;
      }
    }

    // Filter by education levels
    if (filters.educationLevels.length > 0) {
      const candidateEducation = candidate.education || [];
      if (candidateEducation.length === 0) return false;

      // Check if any of the candidate's degrees match the selected education levels
      const candidateDegrees = candidateEducation.map((edu: any) => edu.degreeId).filter(Boolean);
      const matchingDegrees = candidateDegrees.filter((degreeId: string) => {
        const degree = degrees.find(d => d.id === degreeId);
        return degree && filters.educationLevels.includes(degree.level);
      });

      if (matchingDegrees.length === 0) return false;
    }

    // Filter by specific degrees
    if (filters.degrees.length > 0) {
      const candidateEducation = candidate.education || [];
      if (candidateEducation.length === 0) return false;

      // Check if any of the candidate's degrees match the selected degrees
      const candidateDegrees = candidateEducation.map((edu: any) => edu.degreeId).filter(Boolean);
      const hasMatchingDegree = filters.degrees.some(degreeId =>
        candidateDegrees.includes(degreeId)
      );

      if (!hasMatchingDegree) return false;
    }

    // Filter by study fields
    if (filters.studyFields.length > 0) {
      const candidateEducation = candidate.education || [];
      if (candidateEducation.length === 0) return false;

      // Check if any of the candidate's fields match the selected study fields
      const candidateFields = candidateEducation.map((edu: any) => edu.fieldId).filter(Boolean);
      const hasMatchingField = filters.studyFields.some(fieldId =>
        candidateFields.includes(fieldId)
      );

      if (!hasMatchingField) return false;
    }

    // Filter by driver's license
    if (filters.driverLicense) {
      // If no driver's license is specified in the candidate profile, filter them out
      if (!candidate.driverLicense) return false;

      // Import the helper function from driverLicenses.ts
      const { getHighestLicenseCategory } = require("@/data/driverLicenses");

      // Get the highest license category between the filter and candidate
      const filterCategory = filters.driverLicense;
      const candidateCategory = candidate.driverLicense;

      // Check if candidate's license is equal to or higher than the required license
      // This is a simplified check - for a more accurate check, we would need to compare
      // the license categories based on their hierarchy
      if (candidateCategory !== filterCategory) {
        // For now, we'll just do a direct match
        return false;
      }
    }

    return true;
  });

  if (authLoading || loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  return (
    <div className="flex flex-col md:flex-row min-h-screen">
      {/* Mobile Filter Button */}
      <div className="md:hidden fixed bottom-4 right-4 z-40">
        <button
          onClick={toggleSidebar}
          className="bg-blue-600 text-white p-3 rounded-full shadow-lg flex items-center justify-center"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 4a1 1 0 011-1h16a1 1 0 011 1v2.586a1 1 0 01-.293.707l-6.414 6.414a1 1 0 00-.293.707V17l-4 4v-6.586a1 1 0 00-.293-.707L3.293 7.293A1 1 0 013 6.586V4z" />
          </svg>
        </button>
      </div>

      {/* Filter Sidebar */}
      <div className="md:w-80 flex-shrink-0">
        <CandidateFilterSidebar
          filters={filters}
          onChange={handleFiltersChange}
          isOpen={sidebarOpen}
          onClose={() => setSidebarOpen(false)}
        />
      </div>

      {/* Main Content */}
      <div className="flex-grow p-4 md:p-8">
        <div className="flex justify-between items-center mb-6">
          <h1 className="text-2xl font-bold">Browse Candidates</h1>
          <div className="text-sm text-neutral-500 dark:text-neutral-400">
            {filteredCandidates.length} candidate{filteredCandidates.length !== 1 ? 's' : ''} found
          </div>
        </div>

        {/* Applied Filters */}
        {(filters.profession || filters.countries.length > 0 || filters.germanLanguageLevel ||
          filters.englishLanguageLevel || filters.positions.length > 0 || filters.benefits.length > 0 ||
          filters.educationLevels.length > 0 || filters.degrees.length > 0 || filters.studyFields.length > 0 ||
          filters.driverLicense) && (
          <div className="mb-6 p-4 bg-neutral-50 dark:bg-neutral-800/50 rounded-lg">
            <div className="flex justify-between items-center mb-2">
              <h3 className="text-sm font-medium">Applied Filters</h3>
              <button
                onClick={resetFilters}
                className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300 text-xs"
              >
                Clear All
              </button>
            </div>
            <div className="flex flex-wrap gap-2">
              {filters.profession && (
                <span className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-xs flex items-center">
                  Profession: {filters.profession}
                </span>
              )}

              {filters.countries.map(countryId => {
                const country = countries.find(c => c.id === countryId);
                return (
                  <span key={countryId} className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-xs flex items-center">
                    Country: {country?.name || countryId}
                  </span>
                );
              })}

              {filters.germanLanguageLevel && (
                <span className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-xs flex items-center">
                  German: {filters.germanLanguageLevel}+
                </span>
              )}

              {filters.englishLanguageLevel && (
                <span className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-xs flex items-center">
                  English: {filters.englishLanguageLevel}+
                </span>
              )}

              {filters.positions.map(positionId => {
                const position = positions.find(p => p.id === positionId);
                return (
                  <span key={positionId} className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-xs flex items-center">
                    Position: {position?.translations.en || positionId}
                  </span>
                );
              })}

              {filters.educationLevels.map(level => {
                const educationLevel = [
                  { id: 'secondary', label: 'Secondary Education' },
                  { id: 'vocational', label: 'Vocational Training' },
                  { id: 'tertiary', label: 'Tertiary Education' },
                  { id: 'bachelor', label: 'Bachelor Degree' },
                  { id: 'master', label: 'Master Degree' },
                  { id: 'doctorate', label: 'Doctorate' },
                  { id: 'professional', label: 'Professional Degree' },
                ].find(l => l.id === level);

                return (
                  <span key={level} className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-xs flex items-center">
                    Education: {educationLevel?.label || level}
                  </span>
                );
              })}

              {filters.degrees.map(degreeId => {
                const degree = degrees.find(d => d.id === degreeId);
                return (
                  <span key={degreeId} className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-xs flex items-center">
                    Degree: {degree?.translations.en || degreeId}
                  </span>
                );
              })}

              {filters.benefits.map(benefitId => {
                const benefit = jobBenefits.find(b => b.id === benefitId);
                return (
                  <span key={benefitId} className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-xs flex items-center">
                    Benefit: {benefit?.label || benefitId}
                  </span>
                );
              })}

              {filters.studyFields.map(fieldId => {
                const field = studyFields.find(f => f.id === fieldId);
                return (
                  <span key={fieldId} className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-xs flex items-center">
                    Field: {field?.translations.en || fieldId}
                  </span>
                );
              })}

              {filters.driverLicense && (
                <span className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-xs flex items-center">
                  Driver's License: {driverLicenses.find(l => l.id === filters.driverLicense)?.name || filters.driverLicense}
                </span>
              )}
            </div>
          </div>
        )}

        {/* Candidate Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredCandidates.map((candidate) => (
            <div
              key={candidate.id}
              className="border border-neutral-200 dark:border-neutral-700 rounded-lg overflow-hidden shadow-sm hover:shadow-md transition-shadow"
            >
              <div className="p-6">
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h2 className="text-xl font-semibold">
                      {candidate.profession}, {candidate.age}
                    </h2>
                    <p className="text-neutral-600 dark:text-neutral-400">
                      {candidate.countryOfOrigin}
                    </p>
                  </div>
                </div>

                {/* Languages */}
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-neutral-500 dark:text-neutral-400 mb-1">
                    Languages
                  </h3>
                  <div className="flex flex-wrap gap-2">
                    {candidate.germanLanguageLevel && (
                      <span className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-xs">
                        German: {candidate.germanLanguageLevel}
                      </span>
                    )}
                    {candidate.englishLanguageLevel && (
                      <span className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 px-2 py-1 rounded-full text-xs">
                        English: {candidate.englishLanguageLevel}
                      </span>
                    )}
                  </div>
                </div>

                {/* Desired Countries */}
                {candidate.desiredCountries && candidate.desiredCountries.length > 0 && (
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-neutral-500 dark:text-neutral-400 mb-1">
                      Desired Countries
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {candidate.desiredCountries.map((countryId: string) => {
                        const country = countries.find(c => c.id === countryId);
                        return (
                          <span key={countryId} className="flex items-center bg-neutral-100 dark:bg-neutral-800 text-neutral-700 dark:text-neutral-300 text-xs px-2 py-1 rounded">
                            {country?.flagUrl && (
                              <div className="w-3 h-2 mr-1 overflow-hidden rounded-sm">
                                <Image
                                  src={country.flagUrl}
                                  alt={country.name}
                                  width={12}
                                  height={8}
                                  className="w-full h-full object-cover"
                                />
                              </div>
                            )}
                            {country ? country.name : countryId}
                          </span>
                        );
                      })}
                    </div>
                  </div>
                )}

                {/* Education */}
                {candidate.education && candidate.education.length > 0 && (
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-neutral-500 dark:text-neutral-400 mb-1">
                      Education
                    </h3>
                    <div className="flex flex-wrap gap-1">
                      {candidate.education.slice(0, 2).map((edu: any, index: number) => {
                        const degree = edu.degreeId ? degrees.find(d => d.id === edu.degreeId) : null;
                        const field = edu.fieldId ? studyFields.find(f => f.id === edu.fieldId) : null;
                        return (
                          <span key={index} className="bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 text-xs px-2 py-1 rounded">
                            {degree ? degree.translations.en : edu.degree}
                            {field && ` (${field.translations.en})`}
                          </span>
                        );
                      })}
                      {candidate.education.length > 2 && (
                        <span className="text-green-600 dark:text-green-400 text-xs">
                          +{candidate.education.length - 2} more
                        </span>
                      )}
                    </div>
                  </div>
                )}

                {/* Work Experience */}
                {candidate.workExperience && candidate.workExperience.length > 0 && (
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-neutral-500 dark:text-neutral-400 mb-1">
                      Work Experience
                    </h3>
                    <div className="flex flex-wrap gap-1">
                      {candidate.workExperience.slice(0, 2).map((exp: any, index: number) => {
                        const position = positions.find(p => p.id === exp.positionId);
                        return (
                          <span key={index} className="bg-amber-50 dark:bg-amber-900/20 text-amber-700 dark:text-amber-400 text-xs px-2 py-1 rounded">
                            {position ? position.translations.en : exp.position}
                          </span>
                        );
                      })}
                      {candidate.workExperience.length > 2 && (
                        <span className="text-amber-600 dark:text-amber-400 text-xs">
                          +{candidate.workExperience.length - 2} more
                        </span>
                      )}
                    </div>
                  </div>
                )}

                {/* Job Preferences */}
                {candidate.benefits && candidate.benefits.length > 0 && (
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-neutral-500 dark:text-neutral-400 mb-1">
                      Job Preferences
                    </h3>
                    <div className="flex flex-wrap gap-1">
                      {candidate.benefits.slice(0, 3).map((benefitId: string) => {
                        const benefit = jobBenefits.find(b => b.id === benefitId);
                        return (
                          <span key={benefitId} className="bg-purple-50 dark:bg-purple-900/20 text-purple-700 dark:text-purple-400 text-xs px-2 py-1 rounded-full">
                            {benefit ? benefit.label : benefitId}
                          </span>
                        );
                      })}
                      {candidate.benefits.length > 3 && (
                        <span className="text-purple-600 dark:text-purple-400 text-xs">
                          +{candidate.benefits.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>
                )}

                <div className="mt-4 flex justify-end">
                  <Link
                    href={`/employer/candidates/${candidate.id}`}
                    className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm flex items-center"
                  >
                    View Profile
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-4 w-4 ml-1" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10.293 5.293a1 1 0 011.414 0l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414-1.414L12.586 11H5a1 1 0 110-2h7.586l-2.293-2.293a1 1 0 010-1.414z" clipRule="evenodd" />
                    </svg>
                  </Link>
                </div>
              </div>
            </div>
          ))}
        </div>

        {filteredCandidates.length === 0 && (
          <div className="text-center py-12">
            <p className="text-neutral-600 dark:text-neutral-400">No candidates found matching your filters.</p>
          </div>
        )}
      </div>
    </div>
  );
}
