{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/data/positions.ts"], "sourcesContent": ["import { Position } from \"@/types\";\n\n// Primjer standardiziranih pozicija na više jezika\nexport const positions: Position[] = [\n  {\n    id: \"electrician\",\n    translations: {\n      de: \"Elektriker\",\n      en: \"Electrician\",\n      bs: \"Elek<PERSON><PERSON><PERSON>\",\n      hr: \"Elektri<PERSON><PERSON>\",\n      sr: \"Električar\",\n      me: \"Električ<PERSON>\",\n      mk: \"Електричар\",\n      al: \"Elektricist\"\n    },\n    category: \"construction\",\n    skills: [\"electrical-wiring\", \"electrical-maintenance\", \"electrical-installation\"]\n  },\n  {\n    id: \"plumber\",\n    translations: {\n      de: \"<PERSON><PERSON><PERSON><PERSON>\",\n      en: \"Plumber\",\n      bs: \"Vodoinstalater\",\n      hr: \"Vodoinstalater\",\n      sr: \"Vodoinstalater\",\n      me: \"Vodoinstalater\",\n      mk: \"Водоводџија\",\n      al: \"Hidraulik\"\n    },\n    category: \"construction\",\n    skills: [\"plumbing\", \"pipe-fitting\", \"water-systems\"]\n  },\n  {\n    id: \"carpenter\",\n    translations: {\n      de: \"Tischler\",\n      en: \"Carpenter\",\n      bs: \"Stolar\",\n      hr: \"Stolar\",\n      sr: \"Stolar\",\n      me: \"Stolar\",\n      mk: \"Столар\",\n      al: \"Marangoz\"\n    },\n    category: \"construction\",\n    skills: [\"woodworking\", \"furniture-making\", \"carpentry\"]\n  },\n  {\n    id: \"welder\",\n    translations: {\n      de: \"Schweißer\",\n      en: \"Welder\",\n      bs: \"Zavarivač\",\n      hr: \"Zavarivač\",\n      sr: \"Zavarivač\",\n      me: \"Zavarivač\",\n      mk: \"Заварувач\",\n      al: \"Saldator\"\n    },\n    category: \"manufacturing\",\n    skills: [\"welding\", \"metal-fabrication\", \"blueprint-reading\"]\n  },\n  {\n    id: \"chef\",\n    translations: {\n      de: \"Koch\",\n      en: \"Chef\",\n      bs: \"Kuhar\",\n      hr: \"Kuhar\",\n      sr: \"Kuvar\",\n      me: \"Kuvar\",\n      mk: \"Готвач\",\n      al: \"Kuzhinier\"\n    },\n    category: \"hospitality\",\n    skills: [\"cooking\", \"food-preparation\", \"menu-planning\"]\n  },\n  {\n    id: \"waiter\",\n    translations: {\n      de: \"Kellner\",\n      en: \"Waiter\",\n      bs: \"Konobar\",\n      hr: \"Konobar\",\n      sr: \"Konobar\",\n      me: \"Konobar\",\n      mk: \"Келнер\",\n      al: \"Kamarier\"\n    },\n    category: \"hospitality\",\n    skills: [\"customer-service\", \"food-service\", \"order-taking\"]\n  },\n  {\n    id: \"nurse\",\n    translations: {\n      de: \"Krankenpfleger\",\n      en: \"Nurse\",\n      bs: \"Medicinska sestra/tehničar\",\n      hr: \"Medicinska sestra/tehničar\",\n      sr: \"Medicinska sestra/tehničar\",\n      me: \"Medicinska sestra/tehničar\",\n      mk: \"Медицинска сестра/техничар\",\n      al: \"Infermier\"\n    },\n    category: \"healthcare\",\n    skills: [\"patient-care\", \"medical-assistance\", \"health-monitoring\"]\n  },\n  {\n    id: \"caregiver\",\n    translations: {\n      de: \"Pflegekraft\",\n      en: \"Caregiver\",\n      bs: \"Njegovatelj\",\n      hr: \"Njegovatelj\",\n      sr: \"Negovatelj\",\n      me: \"Njegovatelj\",\n      mk: \"Негувател\",\n      al: \"Kujdestar\"\n    },\n    category: \"healthcare\",\n    skills: [\"elderly-care\", \"personal-care\", \"medication-management\"]\n  },\n  {\n    id: \"driver\",\n    translations: {\n      de: \"Fahrer\",\n      en: \"Driver\",\n      bs: \"Vozač\",\n      hr: \"Vozač\",\n      sr: \"Vozač\",\n      me: \"Vozač\",\n      mk: \"Возач\",\n      al: \"Shofer\"\n    },\n    category: \"transportation\",\n    skills: [\"driving\", \"vehicle-maintenance\", \"route-planning\"]\n  },\n  {\n    id: \"software-developer\",\n    translations: {\n      de: \"Softwareentwickler\",\n      en: \"Software Developer\",\n      bs: \"Programer\",\n      hr: \"Programer\",\n      sr: \"Programer\",\n      me: \"Programer\",\n      mk: \"Програмер\",\n      al: \"Zhvillues Softueri\"\n    },\n    category: \"it\",\n    skills: [\"programming\", \"software-development\", \"problem-solving\"]\n  },\n  {\n    id: \"accountant\",\n    translations: {\n      de: \"Buchhalter\",\n      en: \"Accountant\",\n      bs: \"Računovođa\",\n      hr: \"Računovođa\",\n      sr: \"Računovođa\",\n      me: \"Računovođa\",\n      mk: \"Сметководител\",\n      al: \"Kontabilist\"\n    },\n    category: \"finance\",\n    skills: [\"accounting\", \"bookkeeping\", \"financial-reporting\"]\n  },\n  {\n    id: \"teacher\",\n    translations: {\n      de: \"Lehrer\",\n      en: \"Teacher\",\n      bs: \"Nastavnik\",\n      hr: \"Nastavnik\",\n      sr: \"Nastavnik\",\n      me: \"Nastavnik\",\n      mk: \"Наставник\",\n      al: \"Mësues\"\n    },\n    category: \"education\",\n    skills: [\"teaching\", \"curriculum-development\", \"student-assessment\"]\n  },\n  {\n    id: \"sales-representative\",\n    translations: {\n      de: \"Vertriebsmitarbeiter\",\n      en: \"Sales Representative\",\n      bs: \"Prodajni predstavnik\",\n      hr: \"Prodajni predstavnik\",\n      sr: \"Prodajni predstavnik\",\n      me: \"Prodajni predstavnik\",\n      mk: \"Продажен претставник\",\n      al: \"Përfaqësues Shitjesh\"\n    },\n    category: \"sales\",\n    skills: [\"sales\", \"customer-relations\", \"negotiation\"]\n  },\n  {\n    id: \"warehouse-worker\",\n    translations: {\n      de: \"Lagerarbeiter\",\n      en: \"Warehouse Worker\",\n      bs: \"Skladištar\",\n      hr: \"Skladištar\",\n      sr: \"Magacioner\",\n      me: \"Magacioner\",\n      mk: \"Магационер\",\n      al: \"Punëtor Magazinimi\"\n    },\n    category: \"logistics\",\n    skills: [\"inventory-management\", \"forklift-operation\", \"order-picking\"]\n  },\n  {\n    id: \"cleaner\",\n    translations: {\n      de: \"Reinigungskraft\",\n      en: \"Cleaner\",\n      bs: \"Čistač\",\n      hr: \"Čistač\",\n      sr: \"Čistač\",\n      me: \"Čistač\",\n      mk: \"Хигиеничар\",\n      al: \"Pastrues\"\n    },\n    category: \"facility-services\",\n    skills: [\"cleaning\", \"sanitation\", \"housekeeping\"]\n  }\n];\n"], "names": [], "mappings": ";;;AAGO,MAAM,YAAwB;IACnC;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAqB;YAA0B;SAA0B;IACpF;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAY;YAAgB;SAAgB;IACvD;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAe;YAAoB;SAAY;IAC1D;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAW;YAAqB;SAAoB;IAC/D;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAW;YAAoB;SAAgB;IAC1D;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAoB;YAAgB;SAAe;IAC9D;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAgB;YAAsB;SAAoB;IACrE;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAgB;YAAiB;SAAwB;IACpE;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAW;YAAuB;SAAiB;IAC9D;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAe;YAAwB;SAAkB;IACpE;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAc;YAAe;SAAsB;IAC9D;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAY;YAA0B;SAAqB;IACtE;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAS;YAAsB;SAAc;IACxD;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAwB;YAAsB;SAAgB;IACzE;IACA;QACE,IAAI;QACJ,cAAc;YACZ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;YACJ,IAAI;QACN;QACA,UAAU;QACV,QAAQ;YAAC;YAAY;YAAc;SAAe;IACpD;CACD", "debugId": null}}]}