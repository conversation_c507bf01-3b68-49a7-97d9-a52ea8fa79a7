"use client";

import { useState, useEffect } from "react";
import { collection, query, getDocs, doc, updateDoc, deleteDoc, setDoc } from "firebase/firestore";
import { createUserWithEmailAndPassword } from "firebase/auth";
import { db, auth } from "@/lib/firebase";
import { Admin } from "@/types";

export default function AdminUsersPage() {
  const [admins, setAdmins] = useState<Admin[]>([]);
  const [loading, setLoading] = useState(true);
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [newAdmin, setNewAdmin] = useState({
    email: "",
    password: "",
    name: "",
    permissions: {
      users: true,
      content: true,
      settings: false,
      finance: false,
      superAdmin: false,
    },
  });
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  useEffect(() => {
    const fetchAdmins = async () => {
      try {
        const adminsQuery = query(collection(db, "admins"));
        const snapshot = await getDocs(adminsQuery);
        const adminsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          lastLogin: doc.data().lastLogin?.toDate(),
        })) as Admin[];
        setAdmins(adminsData);
      } catch (error) {
        console.error("Error fetching admins:", error);
        setError("Failed to load admin users. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchAdmins();
  }, []);

  const handleAddNewAdmin = () => {
    setIsAddingNew(true);
    setNewAdmin({
      email: "",
      password: "",
      name: "",
      permissions: {
        users: true,
        content: true,
        settings: false,
        finance: false,
        superAdmin: false,
      },
    });
    setError(null);
  };

  const handleCancelAdd = () => {
    setIsAddingNew(false);
    setError(null);
  };

  const handleCreateAdmin = async () => {
    // Validate
    if (!newAdmin.email || !newAdmin.password || !newAdmin.name) {
      setError("All fields are required");
      return;
    }

    if (newAdmin.password.length < 6) {
      setError("Password must be at least 6 characters");
      return;
    }

    try {
      // Create user in Firebase Auth
      const userCredential = await createUserWithEmailAndPassword(
        auth,
        newAdmin.email,
        newAdmin.password
      );
      
      const uid = userCredential.user.uid;
      
      // Create admin document in Firestore
      const adminData: Admin = {
        uid,
        email: newAdmin.email,
        role: "admin",
        name: newAdmin.name,
        permissions: newAdmin.permissions,
        createdAt: new Date(),
      };

      await setDoc(doc(db, "admins", uid), adminData);

      // Update local state
      setAdmins([...admins, adminData]);
      setIsAddingNew(false);
      setSuccess("Admin user created successfully");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error: any) {
      console.error("Error creating admin:", error);
      setError(error.message || "Failed to create admin user. Please try again.");
    }
  };

  const handleUpdatePermissions = async (adminId: string, permissions: any) => {
    try {
      await updateDoc(doc(db, "admins", adminId), {
        permissions,
        updatedAt: new Date(),
      });

      // Update local state
      setAdmins(admins.map(admin => 
        admin.uid === adminId 
          ? { ...admin, permissions, updatedAt: new Date() }
          : admin
      ));
      
      setSuccess("Permissions updated successfully");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Error updating permissions:", error);
      setError("Failed to update permissions. Please try again.");
    }
  };

  const handleDeleteAdmin = async (adminId: string) => {
    if (!confirm("Are you sure you want to delete this admin user? This action cannot be undone.")) {
      return;
    }

    try {
      await deleteDoc(doc(db, "admins", adminId));
      
      // Update local state
      setAdmins(admins.filter(admin => admin.uid !== adminId));
      setSuccess("Admin user deleted successfully");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Error deleting admin:", error);
      setError("Failed to delete admin user. Please try again.");
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Admin Users</h1>
        <button
          onClick={handleAddNewAdmin}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
        >
          Add New Admin
        </button>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 p-4 rounded-md mb-6">
          {success}
        </div>
      )}

      {/* Add New Admin Form */}
      {isAddingNew && (
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-lg font-medium mb-4">Add New Admin User</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                Email <span className="text-red-500">*</span>
              </label>
              <input
                type="email"
                value={newAdmin.email}
                onChange={(e) => setNewAdmin({...newAdmin, email: e.target.value})}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                placeholder="<EMAIL>"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                Password <span className="text-red-500">*</span>
              </label>
              <input
                type="password"
                value={newAdmin.password}
                onChange={(e) => setNewAdmin({...newAdmin, password: e.target.value})}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                placeholder="Minimum 6 characters"
              />
            </div>
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Name <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={newAdmin.name}
              onChange={(e) => setNewAdmin({...newAdmin, name: e.target.value})}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              placeholder="Admin Name"
            />
          </div>
          
          <div className="mb-6">
            <h3 className="text-md font-medium mb-2">Permissions</h3>
            <div className="space-y-2">
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="perm-users"
                  checked={newAdmin.permissions.users}
                  onChange={(e) => setNewAdmin({
                    ...newAdmin,
                    permissions: {
                      ...newAdmin.permissions,
                      users: e.target.checked,
                    },
                  })}
                  className="mr-2"
                />
                <label htmlFor="perm-users" className="text-sm">User Management</label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="perm-content"
                  checked={newAdmin.permissions.content}
                  onChange={(e) => setNewAdmin({
                    ...newAdmin,
                    permissions: {
                      ...newAdmin.permissions,
                      content: e.target.checked,
                    },
                  })}
                  className="mr-2"
                />
                <label htmlFor="perm-content" className="text-sm">Content Management</label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="perm-settings"
                  checked={newAdmin.permissions.settings}
                  onChange={(e) => setNewAdmin({
                    ...newAdmin,
                    permissions: {
                      ...newAdmin.permissions,
                      settings: e.target.checked,
                    },
                  })}
                  className="mr-2"
                />
                <label htmlFor="perm-settings" className="text-sm">Platform Settings</label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="perm-finance"
                  checked={newAdmin.permissions.finance}
                  onChange={(e) => setNewAdmin({
                    ...newAdmin,
                    permissions: {
                      ...newAdmin.permissions,
                      finance: e.target.checked,
                    },
                  })}
                  className="mr-2"
                />
                <label htmlFor="perm-finance" className="text-sm">Financial Management</label>
              </div>
              
              <div className="flex items-center">
                <input
                  type="checkbox"
                  id="perm-super"
                  checked={newAdmin.permissions.superAdmin}
                  onChange={(e) => setNewAdmin({
                    ...newAdmin,
                    permissions: {
                      ...newAdmin.permissions,
                      superAdmin: e.target.checked,
                    },
                  })}
                  className="mr-2"
                />
                <label htmlFor="perm-super" className="text-sm">Super Admin (All Permissions)</label>
              </div>
            </div>
          </div>
          
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleCancelAdd}
              className="px-4 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-700"
            >
              Cancel
            </button>
            <button
              onClick={handleCreateAdmin}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
            >
              Create Admin
            </button>
          </div>
        </div>
      )}

      {/* Admins Table */}
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-neutral-200 dark:divide-neutral-700">
            <thead className="bg-neutral-50 dark:bg-neutral-900">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Email
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Permissions
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Last Login
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-neutral-800 divide-y divide-neutral-200 dark:divide-neutral-700">
              {admins.map((admin) => (
                <tr key={admin.uid}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-neutral-900 dark:text-white">
                      {admin.name || "N/A"}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-neutral-500 dark:text-neutral-400">
                      {admin.email}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="flex flex-wrap gap-1">
                      {admin.permissions?.users && (
                        <span className="px-2 py-1 text-xs rounded-full bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400">
                          Users
                        </span>
                      )}
                      {admin.permissions?.content && (
                        <span className="px-2 py-1 text-xs rounded-full bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400">
                          Content
                        </span>
                      )}
                      {admin.permissions?.settings && (
                        <span className="px-2 py-1 text-xs rounded-full bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400">
                          Settings
                        </span>
                      )}
                      {admin.permissions?.finance && (
                        <span className="px-2 py-1 text-xs rounded-full bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400">
                          Finance
                        </span>
                      )}
                      {admin.permissions?.superAdmin && (
                        <span className="px-2 py-1 text-xs rounded-full bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400">
                          Super Admin
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-neutral-500 dark:text-neutral-400">
                      {admin.lastLogin ? new Date(admin.lastLogin).toLocaleString() : "Never"}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <button
                      onClick={() => handleDeleteAdmin(admin.uid)}
                      className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
              {admins.length === 0 && (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-neutral-500 dark:text-neutral-400">
                    No admin users found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
