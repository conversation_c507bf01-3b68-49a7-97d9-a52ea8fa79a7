"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { collection, query, where, getDocs, orderBy, doc, updateDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { CandidateInvite } from "@/types";

export default function CandidateInvitesPage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [invites, setInvites] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [responding, setResponding] = useState<string | null>(null);

  useEffect(() => {
    // If not authenticated or not a candidate, redirect to auth page
    if (!authLoading && (!user || user.role !== "candidate")) {
      router.push("/auth");
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    const fetchInvites = async () => {
      if (!user) return;
      
      try {
        const invitesCollection = collection(db, "invites");
        const invitesQuery = query(
          invitesCollection,
          where("candidateId", "==", user.uid),
          orderBy("createdAt", "desc")
        );
        
        const querySnapshot = await getDocs(invitesQuery);
        const invitesList: any[] = [];
        
        querySnapshot.forEach((doc) => {
          const data = doc.data();
          invitesList.push({
            id: doc.id,
            ...data,
            createdAt: data.createdAt.toDate(),
            updatedAt: data.updatedAt.toDate(),
            expiresAt: data.expiresAt.toDate(),
          });
        });
        
        setInvites(invitesList);
      } catch (error) {
        console.error("Error fetching invites:", error);
      } finally {
        setLoading(false);
      }
    };

    if (user && user.role === "candidate") {
      fetchInvites();
    }
  }, [user]);

  const handleResponse = async (inviteId: string, response: 'accepted' | 'declined') => {
    if (!user) return;
    
    setResponding(inviteId);
    
    try {
      const inviteRef = doc(db, "invites", inviteId);
      
      await updateDoc(inviteRef, {
        status: response,
        updatedAt: new Date()
      });
      
      // If accepted, redirect to job application page
      if (response === 'accepted') {
        const invite = invites.find(inv => inv.id === inviteId);
        if (invite) {
          router.push(`/jobs/${invite.jobId}/apply?invite=${inviteId}`);
          return;
        }
      }
      
      // Update local state
      setInvites(prev => 
        prev.map(invite => 
          invite.id === inviteId 
            ? { ...invite, status: response, updatedAt: new Date() } 
            : invite
        )
      );
    } catch (error) {
      console.error("Error responding to invite:", error);
      alert("Failed to respond to invitation. Please try again.");
    } finally {
      setResponding(null);
    }
  };

  const getStatusBadge = (status: string, expiresAt: Date) => {
    // Check if invite is expired
    const isExpired = new Date() > expiresAt && status === 'pending';
    
    if (isExpired) {
      return (
        <span className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 text-xs px-2 py-1 rounded-full">
          Expired
        </span>
      );
    }
    
    switch (status) {
      case "pending":
        return (
          <span className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 text-xs px-2 py-1 rounded-full">
            Pending
          </span>
        );
      case "accepted":
        return (
          <span className="bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 text-xs px-2 py-1 rounded-full">
            Accepted
          </span>
        );
      case "declined":
        return (
          <span className="bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300 text-xs px-2 py-1 rounded-full">
            Declined
          </span>
        );
      default:
        return (
          <span className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 text-xs px-2 py-1 rounded-full">
            {status}
          </span>
        );
    }
  };

  if (authLoading || loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto py-12">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Job Invitations</h1>
        <Link
          href="/candidate/dashboard"
          className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
        >
          Back to Dashboard
        </Link>
      </div>
      
      {invites.length === 0 ? (
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8 text-center">
          <h2 className="text-xl font-medium mb-2">No Invitations Yet</h2>
          <p className="text-neutral-600 dark:text-neutral-400 mb-6">
            You haven't received any job invitations yet. When employers invite you to apply for jobs, they will appear here.
          </p>
          <Link
            href="/jobs"
            className="bg-neutral-900 hover:bg-neutral-800 text-white px-4 py-2 rounded-md"
          >
            Browse Jobs
          </Link>
        </div>
      ) : (
        <div className="space-y-6">
          {invites.map((invite) => {
            const isExpired = new Date() > invite.expiresAt && invite.status === 'pending';
            const isPending = invite.status === 'pending' && !isExpired;
            
            return (
              <div
                key={invite.id}
                className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6"
              >
                <div className="flex justify-between items-start mb-4">
                  <div>
                    <h2 className="text-xl font-semibold mb-1">{invite.jobTitle}</h2>
                    <p className="text-neutral-600 dark:text-neutral-400 mb-2">
                      {invite.employerCompanyName}
                    </p>
                    <div className="flex items-center">
                      {getStatusBadge(invite.status, invite.expiresAt)}
                      <span className="text-sm text-neutral-500 dark:text-neutral-400 ml-3">
                        Received {invite.createdAt.toLocaleDateString()}
                      </span>
                      {isExpired && (
                        <span className="text-sm text-red-500 ml-3">
                          Expired {invite.expiresAt.toLocaleDateString()}
                        </span>
                      )}
                    </div>
                  </div>
                  <Link
                    href={`/jobs/${invite.jobId}`}
                    className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
                  >
                    View Job
                  </Link>
                </div>
                
                {invite.message && (
                  <div className="mb-4 p-4 bg-neutral-50 dark:bg-neutral-900 rounded-md">
                    <h3 className="text-sm font-medium mb-2">Message from Employer:</h3>
                    <p className="text-neutral-600 dark:text-neutral-400 whitespace-pre-line">
                      {invite.message}
                    </p>
                  </div>
                )}
                
                {isPending && (
                  <div className="flex gap-3 mt-4">
                    <button
                      onClick={() => handleResponse(invite.id, 'accepted')}
                      disabled={responding === invite.id}
                      className={`px-4 py-2 rounded-md ${
                        responding === invite.id
                          ? "bg-green-700 text-white"
                          : "bg-green-600 hover:bg-green-700 text-white"
                      }`}
                    >
                      {responding === invite.id ? "Processing..." : "Accept & Apply"}
                    </button>
                    <button
                      onClick={() => handleResponse(invite.id, 'declined')}
                      disabled={responding === invite.id}
                      className={`px-4 py-2 rounded-md ${
                        responding === invite.id
                          ? "bg-neutral-700 text-white"
                          : "bg-neutral-200 hover:bg-neutral-300 text-neutral-800 dark:bg-neutral-700 dark:hover:bg-neutral-600 dark:text-neutral-200"
                      }`}
                    >
                      {responding === invite.id ? "Processing..." : "Decline"}
                    </button>
                  </div>
                )}
                
                {invite.status === 'accepted' && (
                  <div className="mt-4">
                    <Link
                      href={`/jobs/${invite.jobId}`}
                      className="bg-neutral-900 hover:bg-neutral-800 text-white px-4 py-2 rounded-md inline-block"
                    >
                      View Your Application
                    </Link>
                  </div>
                )}
              </div>
            );
          })}
        </div>
      )}
    </div>
  );
}
