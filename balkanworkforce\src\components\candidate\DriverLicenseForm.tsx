import { useState, useEffect } from "react";
import { driverLicenses, driverLicensesByCategory } from "@/data/driverLicenses";

interface DriverLicenseFormProps {
  value: string | undefined;
  onChange: (value: string | undefined) => void;
}

export default function DriverLicenseForm({ value, onChange }: DriverLicenseFormProps) {
  const [license, setLicense] = useState<string | undefined>(value || "none");

  useEffect(() => {
    onChange(license);
  }, [license, onChange]);

  return (
    <div className="space-y-4">
      <div>
        <label className="block text-sm font-medium mb-1">
          Driver's License
        </label>
        <p className="text-sm text-neutral-600 dark:text-neutral-400 mb-2">
          Select your highest driver's license category
        </p>
        <select
          value={license || "none"}
          onChange={(e) => setLicense(e.target.value === "none" ? "none" : e.target.value)}
          className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
        >
          <option value="none">No driver's license</option>
          <optgroup label="Car Licenses">
            {driverLicensesByCategory.car.map((license) => (
              <option key={license.id} value={license.id}>
                {license.name} - {license.description}
              </option>
            ))}
          </optgroup>
          <optgroup label="Motorcycle Licenses">
            {driverLicensesByCategory.motorcycle.map((license) => (
              <option key={license.id} value={license.id}>
                {license.name} - {license.description}
              </option>
            ))}
          </optgroup>
          <optgroup label="Truck Licenses">
            {driverLicensesByCategory.truck.map((license) => (
              <option key={license.id} value={license.id}>
                {license.name} - {license.description}
              </option>
            ))}
          </optgroup>
          <optgroup label="Bus Licenses">
            {driverLicensesByCategory.bus.map((license) => (
              <option key={license.id} value={license.id}>
                {license.name} - {license.description}
              </option>
            ))}
          </optgroup>
        </select>
      </div>
    </div>
  );
}
