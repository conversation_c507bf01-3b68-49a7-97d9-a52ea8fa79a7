"use client";

import { useState, useEffect } from "react";
import { collection, query, getDocs, orderBy, doc, updateDoc, deleteDoc, where } from "firebase/firestore";
import { db } from "@/lib/firebase";
import Link from "next/link";
import { countries } from "@/data/countries";
import { positions } from "@/data/positions";
import BulkOperations from "@/components/admin/BulkOperations";
import { useAuth } from "@/contexts/AuthContext";
import { logAdminActivity } from "@/components/admin/AdminActivityLog";

export default function AdminJobsPage() {
  const { user } = useAuth();
  const [jobs, setJobs] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [statusFilter, setStatusFilter] = useState<string>("all");
  const [sortField, setSortField] = useState("createdAt");
  const [sortDirection, setSortDirection] = useState("desc");
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);
  const [selectedJobs, setSelectedJobs] = useState<string[]>([]);
  const [selectAll, setSelectAll] = useState(false);

  useEffect(() => {
    const fetchJobs = async () => {
      try {
        let jobsQuery;

        if (statusFilter === "all") {
          jobsQuery = query(
            collection(db, "jobs"),
            orderBy("createdAt", "desc")
          );
        } else {
          jobsQuery = query(
            collection(db, "jobs"),
            where("status", "==", statusFilter),
            orderBy("createdAt", "desc")
          );
        }

        const snapshot = await getDocs(jobsQuery);
        const jobsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate(),
          updatedAt: doc.data().updatedAt?.toDate(),
          expiresAt: doc.data().expiresAt?.toDate(),
        }));
        setJobs(jobsData);
      } catch (error) {
        console.error("Error fetching jobs:", error);
        setError("Failed to load jobs. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchJobs();
  }, [statusFilter]);

  const handleSort = (field: string) => {
    if (sortField === field) {
      setSortDirection(sortDirection === "asc" ? "desc" : "asc");
    } else {
      setSortField(field);
      setSortDirection("desc");
    }
  };

  const sortedJobs = [...jobs].sort((a, b) => {
    let aValue = a[sortField];
    let bValue = b[sortField];

    // Handle null or undefined values
    if (aValue === null || aValue === undefined) aValue = "";
    if (bValue === null || bValue === undefined) bValue = "";

    // Handle dates
    if (aValue instanceof Date && bValue instanceof Date) {
      return sortDirection === "asc"
        ? aValue.getTime() - bValue.getTime()
        : bValue.getTime() - aValue.getTime();
    }

    // Handle strings
    if (typeof aValue === "string" && typeof bValue === "string") {
      return sortDirection === "asc"
        ? aValue.localeCompare(bValue)
        : bValue.localeCompare(aValue);
    }

    // Handle numbers
    return sortDirection === "asc" ? aValue - bValue : bValue - aValue;
  });

  const filteredJobs = sortedJobs.filter(job => {
    const searchLower = searchTerm.toLowerCase();
    return (
      (job.title && job.title.toLowerCase().includes(searchLower)) ||
      (job.company && job.company.toLowerCase().includes(searchLower)) ||
      (job.location && job.location.toLowerCase().includes(searchLower)) ||
      (job.employerId && job.employerId.toLowerCase().includes(searchLower))
    );
  });

  const handleUpdateJobStatus = async (jobId: string, newStatus: string) => {
    try {
      await updateDoc(doc(db, "jobs", jobId), {
        status: newStatus,
        updatedAt: new Date(),
      });

      // Update local state
      setJobs(jobs.map(job =>
        job.id === jobId
          ? { ...job, status: newStatus, updatedAt: new Date() }
          : job
      ));

      // Log the activity
      if (user) {
        await logAdminActivity(
          user.uid,
          user.email || "unknown",
          "update",
          "job",
          jobId,
          `Updated job status to ${newStatus}`
        );
      }

      setSuccess(`Job status updated to ${newStatus}`);
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Error updating job status:", error);
      setError("Failed to update job status. Please try again.");
    }
  };

  const handleDeleteJob = async (jobId: string) => {
    if (!confirm("Are you sure you want to delete this job? This action cannot be undone.")) {
      return;
    }

    try {
      await deleteDoc(doc(db, "jobs", jobId));

      // Update local state
      setJobs(jobs.filter(job => job.id !== jobId));

      // Log the activity
      if (user) {
        await logAdminActivity(
          user.uid,
          user.email || "unknown",
          "delete",
          "job",
          jobId,
          `Deleted job`
        );
      }

      setSuccess("Job deleted successfully");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Error deleting job:", error);
      setError("Failed to delete job. Please try again.");
    }
  };

  const handleSelectJob = (jobId: string) => {
    setSelectedJobs(prev => {
      if (prev.includes(jobId)) {
        return prev.filter(id => id !== jobId);
      } else {
        return [...prev, jobId];
      }
    });
  };

  const handleSelectAll = () => {
    if (selectAll) {
      setSelectedJobs([]);
    } else {
      setSelectedJobs(filteredJobs.map(job => job.id));
    }
    setSelectAll(!selectAll);
  };

  const handleBulkOperationComplete = () => {
    setSelectedJobs([]);
    setSelectAll(false);

    // Refresh the jobs list
    const fetchJobs = async () => {
      try {
        let jobsQuery;

        if (statusFilter === "all") {
          jobsQuery = query(
            collection(db, "jobs"),
            orderBy("createdAt", "desc")
          );
        } else {
          jobsQuery = query(
            collection(db, "jobs"),
            where("status", "==", statusFilter),
            orderBy("createdAt", "desc")
          );
        }

        const snapshot = await getDocs(jobsQuery);
        const jobsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate(),
          updatedAt: doc.data().updatedAt?.toDate(),
          expiresAt: doc.data().expiresAt?.toDate(),
        }));
        setJobs(jobsData);
      } catch (error) {
        console.error("Error fetching jobs:", error);
        setError("Failed to refresh jobs. Please try again.");
      }
    };

    fetchJobs();
  };

  const getCountryName = (countryId: string) => {
    const country = countries.find(c => c.id === countryId);
    return country ? country.name : countryId;
  };

  const getPositionName = (positionId: string) => {
    const position = positions.find(p => p.id === positionId);
    return position ? position.translations.en : positionId;
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Job Management</h1>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 p-4 rounded-md mb-6">
          {success}
        </div>
      )}

      {/* Search and Filter */}
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <label htmlFor="search" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Search Jobs
            </label>
            <input
              type="text"
              id="search"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by title, company, or location"
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
            />
          </div>
          <div>
            <label htmlFor="status" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Status
            </label>
            <select
              id="status"
              value={statusFilter}
              onChange={(e) => setStatusFilter(e.target.value)}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
            >
              <option value="all">All Statuses</option>
              <option value="active">Active</option>
              <option value="expired">Expired</option>
              <option value="draft">Draft</option>
              <option value="pending">Pending</option>
              <option value="rejected">Rejected</option>
            </select>
          </div>
        </div>
      </div>

      {/* Bulk Operations */}
      {selectedJobs.length > 0 && (
        <BulkOperations
          entityType="jobs"
          selectedItems={selectedJobs}
          onComplete={handleBulkOperationComplete}
          availableActions={[
            {
              id: "set-active",
              label: "Set as Active",
              action: "update",
              field: "status",
              value: "active",
              dangerLevel: "low",
            },
            {
              id: "set-expired",
              label: "Set as Expired",
              action: "update",
              field: "status",
              value: "expired",
              dangerLevel: "low",
            },
            {
              id: "set-draft",
              label: "Set as Draft",
              action: "update",
              field: "status",
              value: "draft",
              dangerLevel: "low",
            },
            {
              id: "delete",
              label: "Delete Jobs",
              action: "delete",
              confirmationMessage: `Are you sure you want to delete ${selectedJobs.length} jobs? This action cannot be undone.`,
              dangerLevel: "high",
            },
          ]}
        />
      )}

      {/* Jobs Table */}
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-neutral-200 dark:divide-neutral-700">
            <thead className="bg-neutral-50 dark:bg-neutral-900">
              <tr>
                <th className="px-6 py-3 text-center text-xs font-medium text-neutral-500 dark:text-neutral-400">
                  <input
                    type="checkbox"
                    checked={selectAll}
                    onChange={handleSelectAll}
                    className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-neutral-300 rounded"
                  />
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("title")}
                >
                  <div className="flex items-center">
                    Title
                    {sortField === "title" && (
                      <span className="ml-1">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("company")}
                >
                  <div className="flex items-center">
                    Company
                    {sortField === "company" && (
                      <span className="ml-1">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("location")}
                >
                  <div className="flex items-center">
                    Location
                    {sortField === "location" && (
                      <span className="ml-1">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("createdAt")}
                >
                  <div className="flex items-center">
                    Posted
                    {sortField === "createdAt" && (
                      <span className="ml-1">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("expiresAt")}
                >
                  <div className="flex items-center">
                    Expires
                    {sortField === "expiresAt" && (
                      <span className="ml-1">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th
                  className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider cursor-pointer"
                  onClick={() => handleSort("status")}
                >
                  <div className="flex items-center">
                    Status
                    {sortField === "status" && (
                      <span className="ml-1">
                        {sortDirection === "asc" ? "↑" : "↓"}
                      </span>
                    )}
                  </div>
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-neutral-800 divide-y divide-neutral-200 dark:divide-neutral-700">
              {filteredJobs.map((job) => (
                <tr key={job.id} className={selectedJobs.includes(job.id) ? "bg-blue-50 dark:bg-blue-900/10" : ""}>
                  <td className="px-6 py-4 whitespace-nowrap text-center">
                    <input
                      type="checkbox"
                      checked={selectedJobs.includes(job.id)}
                      onChange={() => handleSelectJob(job.id)}
                      className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-neutral-300 rounded"
                    />
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm font-medium text-neutral-900 dark:text-white">
                      {job.title || "N/A"}
                    </div>
                    <div className="text-xs text-neutral-500 dark:text-neutral-400">
                      {job.positionId ? getPositionName(job.positionId) : "N/A"}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-neutral-500 dark:text-neutral-400">
                      {job.company || "N/A"}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-neutral-500 dark:text-neutral-400">
                      {job.location || "N/A"}
                      {job.country && (
                        <div className="text-xs">
                          {getCountryName(job.country)}
                        </div>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-neutral-500 dark:text-neutral-400">
                      {job.createdAt ? new Date(job.createdAt).toLocaleDateString() : "N/A"}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-neutral-500 dark:text-neutral-400">
                      {job.expiresAt ? new Date(job.expiresAt).toLocaleDateString() : "N/A"}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      job.status === "active"
                        ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400"
                        : job.status === "expired"
                        ? "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
                        : job.status === "draft"
                        ? "bg-yellow-100 text-yellow-800 dark:bg-yellow-900/20 dark:text-yellow-400"
                        : job.status === "pending"
                        ? "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400"
                        : "bg-neutral-100 text-neutral-800 dark:bg-neutral-900/20 dark:text-neutral-400"
                    }`}>
                      {job.status ? job.status.charAt(0).toUpperCase() + job.status.slice(1) : "Unknown"}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                    <Link
                      href={`/admin/jobs/${job.id}`}
                      className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-3"
                    >
                      View
                    </Link>
                    <div className="inline-block relative group">
                      <button className="text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white mr-3">
                        Status ▼
                      </button>
                      <div className="absolute right-0 mt-2 w-48 bg-white dark:bg-neutral-800 rounded-md shadow-lg z-10 hidden group-hover:block">
                        <div className="py-1">
                          <button
                            onClick={() => handleUpdateJobStatus(job.id, "active")}
                            className="block w-full text-left px-4 py-2 text-sm text-neutral-700 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-700"
                          >
                            Set as Active
                          </button>
                          <button
                            onClick={() => handleUpdateJobStatus(job.id, "expired")}
                            className="block w-full text-left px-4 py-2 text-sm text-neutral-700 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-700"
                          >
                            Set as Expired
                          </button>
                          <button
                            onClick={() => handleUpdateJobStatus(job.id, "draft")}
                            className="block w-full text-left px-4 py-2 text-sm text-neutral-700 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-700"
                          >
                            Set as Draft
                          </button>
                          <button
                            onClick={() => handleUpdateJobStatus(job.id, "pending")}
                            className="block w-full text-left px-4 py-2 text-sm text-neutral-700 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-700"
                          >
                            Set as Pending
                          </button>
                          <button
                            onClick={() => handleUpdateJobStatus(job.id, "rejected")}
                            className="block w-full text-left px-4 py-2 text-sm text-neutral-700 dark:text-neutral-300 hover:bg-neutral-100 dark:hover:bg-neutral-700"
                          >
                            Set as Rejected
                          </button>
                        </div>
                      </div>
                    </div>
                    <button
                      onClick={() => handleDeleteJob(job.id)}
                      className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                    >
                      Delete
                    </button>
                  </td>
                </tr>
              ))}
              {filteredJobs.length === 0 && (
                <tr>
                  <td colSpan={8} className="px-6 py-4 text-center text-sm text-neutral-500 dark:text-neutral-400">
                    No jobs found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
