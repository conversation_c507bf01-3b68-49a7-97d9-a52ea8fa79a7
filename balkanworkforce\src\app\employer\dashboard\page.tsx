"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { collection, query, where, getDocs, orderBy } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { JobListing } from "@/types";

export default function EmployerDashboard() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [jobs, setJobs] = useState<JobListing[]>([]);
  const [loading, setLoading] = useState(true);
  const [profileComplete, setProfileComplete] = useState(false);
  const [employerData, setEmployerData] = useState<any>(null);

  useEffect(() => {
    // If not authenticated or not an employer, redirect to auth page
    if (!authLoading && (!user || user.role !== "employer")) {
      router.push("/auth");
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    const fetchEmployerData = async () => {
      if (!user) return;

      try {
        // Fetch employer profile
        const employerDoc = await getDocs(
          query(collection(db, "employers"), where("uid", "==", user.uid))
        );

        if (!employerDoc.empty) {
          const data = employerDoc.docs[0].data();
          setEmployerData(data);
          setProfileComplete(
            !!data.companyName &&
            !!data.companyDescription &&
            !!data.industry &&
            !!data.location
          );
        }

        // Fetch jobs
        const jobsCollection = collection(db, "jobs");
        // Privremeno koristimo samo where bez orderBy dok se ne kreira indeks
        // Originalni kod:
        // const jobsQuery = query(
        //   jobsCollection,
        //   where("employerId", "==", user.uid),
        //   orderBy("createdAt", "desc")
        // );

        // Privremeno rješenje:
        const jobsQuery = query(
          jobsCollection,
          where("employerId", "==", user.uid)
        );

        const querySnapshot = await getDocs(jobsQuery);
        const jobsList: JobListing[] = [];

        console.log("Employer dashboard - Number of jobs found:", querySnapshot.size);
        console.log("Employer dashboard - User ID:", user.uid);

        querySnapshot.forEach((doc) => {
          const data = doc.data() as Omit<JobListing, "id">;
          console.log("Employer dashboard - Job data:", doc.id, data);
          jobsList.push({
            id: doc.id,
            ...data,
            createdAt: data.createdAt.toDate(),
            updatedAt: data.updatedAt.toDate(),
            premiumUntil: data.premiumUntil ? data.premiumUntil.toDate() : undefined,
          });
        });

        console.log("Employer dashboard - Processed jobs:", jobsList);
        setJobs(jobsList);
      } catch (error) {
        console.error("Error fetching employer data:", error);
      } finally {
        setLoading(false);
      }
    };

    if (user && user.role === "employer") {
      fetchEmployerData();
    }
  }, [user]);

  if (authLoading || (user && user.role !== "employer")) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto py-12">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Employer Dashboard</h1>
        <div className="flex gap-4">
          <Link
            href="/employer/profile"
            className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white px-4 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
          >
            Edit Profile
          </Link>
          <Link
            href="/employer/candidates"
            className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white px-4 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
          >
            Browse Candidates
          </Link>
          <Link
            href="/employer/jobs/new"
            className="bg-neutral-900 hover:bg-neutral-800 text-white px-4 py-2 rounded-md"
          >
            Post New Job
          </Link>
        </div>
      </div>

      {!profileComplete && (
        <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-medium text-amber-800 dark:text-amber-400 mb-2">
            Complete Your Company Profile
          </h3>
          <p className="text-amber-700 dark:text-amber-500 mb-4">
            Your company profile is incomplete. Complete your profile to attract more candidates and improve your hiring success.
          </p>
          <Link
            href="/employer/profile"
            className="bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-md inline-block"
          >
            Complete Profile
          </Link>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-10">
        {/* Stats Cards */}
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6">
          <h3 className="text-lg font-medium mb-2">Active Jobs</h3>
          <p className="text-3xl font-bold">{jobs.length}</p>
        </div>
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6">
          <h3 className="text-lg font-medium mb-2">Total Applications</h3>
          <p className="text-3xl font-bold">
            {jobs.reduce((total, job) => total + job.applications.length, 0)}
          </p>
          <div className="mt-4">
            <Link
              href="/employer/applications"
              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
            >
              View All Applications
            </Link>
          </div>
        </div>
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6">
          <h3 className="text-lg font-medium mb-2">Premium Jobs</h3>
          <p className="text-3xl font-bold">
            {jobs.filter((job) => job.isPremium).length}
          </p>
        </div>
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6">
          <h3 className="text-lg font-medium mb-2">Candidate Invites</h3>
          <p className="text-3xl font-bold">
            0
          </p>
          <div className="mt-4">
            <Link
              href="/employer/packages"
              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
            >
              Buy CV Packages
            </Link>
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6">
        <h2 className="text-xl font-semibold mb-6">Your Job Listings</h2>

        {loading ? (
          <div className="flex justify-center py-8">
            <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-neutral-900"></div>
          </div>
        ) : jobs.length === 0 ? (
          <div className="text-center py-8">
            <p className="text-neutral-600 dark:text-neutral-400 mb-4">
              You haven't posted any jobs yet.
            </p>
            <Link
              href="/employer/jobs/new"
              className="bg-neutral-900 hover:bg-neutral-800 text-white px-4 py-2 rounded-md"
            >
              Post Your First Job
            </Link>
          </div>
        ) : (
          <div className="overflow-x-auto">
            <table className="w-full">
              <thead>
                <tr className="border-b border-neutral-200 dark:border-neutral-700">
                  <th className="text-left py-3 px-4">Job Title</th>
                  <th className="text-left py-3 px-4">Status</th>
                  <th className="text-left py-3 px-4">Applications</th>
                  <th className="text-left py-3 px-4">Posted Date</th>
                  <th className="text-left py-3 px-4">Actions</th>
                </tr>
              </thead>
              <tbody>
                {jobs.map((job) => (
                  <tr
                    key={job.id}
                    className="border-b border-neutral-200 dark:border-neutral-700"
                  >
                    <td className="py-3 px-4">
                      <div className="flex items-center">
                        <span className="font-medium">{job.title}</span>
                        {job.isPremium && (
                          <span className="ml-2 bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-100 text-xs px-2 py-0.5 rounded-full">
                            Premium
                          </span>
                        )}
                      </div>
                    </td>
                    <td className="py-3 px-4">
                      <span className="bg-green-100 dark:bg-green-900 text-green-800 dark:text-green-100 text-xs px-2 py-1 rounded-full">
                        Active
                      </span>
                    </td>
                    <td className="py-3 px-4">{job.applications.length}</td>
                    <td className="py-3 px-4">
                      {new Date(job.createdAt).toLocaleDateString()}
                    </td>
                    <td className="py-3 px-4">
                      <div className="flex space-x-2">
                        <Link
                          href={`/employer/jobs/${job.id}`}
                          className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
                        >
                          View
                        </Link>
                        <Link
                          href={`/employer/jobs/${job.id}/edit`}
                          className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
                        >
                          Edit
                        </Link>
                        {!job.isPremium && (
                          <Link
                            href={`/employer/jobs/${job.id}/promote`}
                            className="text-amber-600 hover:text-amber-700 dark:text-amber-400 dark:hover:text-amber-300"
                          >
                            Promote
                          </Link>
                        )}
                      </div>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        )}
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-8">
        <div className="md:col-span-2">
          <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6 mb-8">
            <h2 className="text-xl font-semibold mb-6">Recent Applications</h2>

            {/* In a real implementation, we would fetch and display recent applications */}
            <div className="text-center py-8">
              <p className="text-neutral-600 dark:text-neutral-400">
                No recent applications to display.
              </p>
            </div>
          </div>
        </div>

        <div>
          <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6">
            <h2 className="text-xl font-semibold mb-6">Company Profile</h2>

            {profileComplete ? (
              <div>
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-neutral-500 dark:text-neutral-400 mb-1">
                    Company
                  </h3>
                  <p className="font-medium">
                    {employerData?.companyName}
                  </p>
                  <p className="text-neutral-600 dark:text-neutral-400">
                    {employerData?.industry} • {employerData?.location}
                  </p>
                </div>

                {employerData?.website && (
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-neutral-500 dark:text-neutral-400 mb-1">
                      Website
                    </h3>
                    <a
                      href={employerData.website}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
                    >
                      {employerData.website}
                    </a>
                  </div>
                )}

                <Link
                  href="/employer/profile"
                  className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white text-sm"
                >
                  Edit Profile
                </Link>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-neutral-600 dark:text-neutral-400 mb-4">
                  Complete your company profile to attract more candidates.
                </p>
                <Link
                  href="/employer/profile"
                  className="bg-neutral-900 hover:bg-neutral-800 text-white px-4 py-2 rounded-md"
                >
                  Complete Profile
                </Link>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
