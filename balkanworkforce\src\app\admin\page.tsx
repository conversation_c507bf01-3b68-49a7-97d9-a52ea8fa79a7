"use client";

import { useState, useEffect } from "react";
import { collection, query, getDocs, where, orderBy, limit } from "firebase/firestore";
import { db } from "@/lib/firebase";
import Link from "next/link";
import AnalyticsDashboard from "@/components/admin/AnalyticsDashboard";

export default function AdminDashboardPage() {
  const [stats, setStats] = useState({
    totalEmployers: 0,
    totalCandidates: 0,
    totalJobs: 0,
    totalApplications: 0,
  });
  const [recentEmployers, setRecentEmployers] = useState<any[]>([]);
  const [recentCandidates, setRecentCandidates] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const fetchDashboardData = async () => {
      try {
        // Fetch counts
        const employersSnapshot = await getDocs(collection(db, "employers"));
        const candidatesSnapshot = await getDocs(collection(db, "candidates"));
        const jobsSnapshot = await getDocs(collection(db, "jobs"));
        const applicationsSnapshot = await getDocs(collection(db, "applications"));

        setStats({
          totalEmployers: employersSnapshot.size,
          totalCandidates: candidatesSnapshot.size,
          totalJobs: jobsSnapshot.size,
          totalApplications: applicationsSnapshot.size,
        });

        // Fetch recent employers
        const recentEmployersQuery = query(
          collection(db, "employers"),
          orderBy("createdAt", "desc"),
          limit(5)
        );
        const recentEmployersSnapshot = await getDocs(recentEmployersQuery);
        const employersData = recentEmployersSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate(),
        }));
        setRecentEmployers(employersData);

        // Fetch recent candidates
        const recentCandidatesQuery = query(
          collection(db, "candidates"),
          orderBy("createdAt", "desc"),
          limit(5)
        );
        const recentCandidatesSnapshot = await getDocs(recentCandidatesQuery);
        const candidatesData = recentCandidatesSnapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          createdAt: doc.data().createdAt?.toDate(),
        }));
        setRecentCandidates(candidatesData);
      } catch (error) {
        console.error("Error fetching dashboard data:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchDashboardData();
  }, []);

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  return (
    <div>
      <h1 className="text-2xl font-bold mb-6">Dashboard Overview</h1>

      {/* Stats Cards */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-medium text-neutral-600 dark:text-neutral-400">Total Employers</h2>
          <p className="text-3xl font-bold mt-2">{stats.totalEmployers}</p>
          <Link href="/admin/employers" className="text-blue-600 dark:text-blue-400 text-sm mt-4 inline-block">
            View all employers →
          </Link>
        </div>

        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-medium text-neutral-600 dark:text-neutral-400">Total Candidates</h2>
          <p className="text-3xl font-bold mt-2">{stats.totalCandidates}</p>
          <Link href="/admin/candidates" className="text-blue-600 dark:text-blue-400 text-sm mt-4 inline-block">
            View all candidates →
          </Link>
        </div>

        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-medium text-neutral-600 dark:text-neutral-400">Active Jobs</h2>
          <p className="text-3xl font-bold mt-2">{stats.totalJobs}</p>
          <Link href="/admin/jobs" className="text-blue-600 dark:text-blue-400 text-sm mt-4 inline-block">
            View all jobs →
          </Link>
        </div>

        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-medium text-neutral-600 dark:text-neutral-400">Applications</h2>
          <p className="text-3xl font-bold mt-2">{stats.totalApplications}</p>
          <Link href="/admin/applications" className="text-blue-600 dark:text-blue-400 text-sm mt-4 inline-block">
            View all applications →
          </Link>
        </div>
      </div>

      {/* Analytics Dashboard */}
      <div className="mb-8">
        <AnalyticsDashboard />
      </div>

      {/* Recent Activity */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Recent Employers */}
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-medium mb-4">Recent Employers</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-neutral-200 dark:divide-neutral-700">
              <thead>
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">Company</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">Contact</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">Joined</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-neutral-200 dark:divide-neutral-700">
                {recentEmployers.map((employer) => (
                  <tr key={employer.id}>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="text-sm font-medium">{employer.companyName || "N/A"}</div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="text-sm">{employer.email}</div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="text-sm text-neutral-500 dark:text-neutral-400">
                        {employer.createdAt ? new Date(employer.createdAt).toLocaleDateString() : "N/A"}
                      </div>
                    </td>
                  </tr>
                ))}
                {recentEmployers.length === 0 && (
                  <tr>
                    <td colSpan={3} className="px-4 py-3 text-center text-sm text-neutral-500 dark:text-neutral-400">
                      No employers found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          <div className="mt-4">
            <Link href="/admin/employers" className="text-blue-600 dark:text-blue-400 text-sm">
              View all employers →
            </Link>
          </div>
        </div>

        {/* Recent Candidates */}
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6">
          <h2 className="text-lg font-medium mb-4">Recent Candidates</h2>
          <div className="overflow-x-auto">
            <table className="min-w-full divide-y divide-neutral-200 dark:divide-neutral-700">
              <thead>
                <tr>
                  <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">Candidate</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">Email</th>
                  <th className="px-4 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">Joined</th>
                </tr>
              </thead>
              <tbody className="divide-y divide-neutral-200 dark:divide-neutral-700">
                {recentCandidates.map((candidate) => (
                  <tr key={candidate.id}>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="text-sm font-medium">{candidate.profession}, {candidate.age}</div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="text-sm">{candidate.email}</div>
                    </td>
                    <td className="px-4 py-3 whitespace-nowrap">
                      <div className="text-sm text-neutral-500 dark:text-neutral-400">
                        {candidate.createdAt ? new Date(candidate.createdAt).toLocaleDateString() : "N/A"}
                      </div>
                    </td>
                  </tr>
                ))}
                {recentCandidates.length === 0 && (
                  <tr>
                    <td colSpan={3} className="px-4 py-3 text-center text-sm text-neutral-500 dark:text-neutral-400">
                      No candidates found
                    </td>
                  </tr>
                )}
              </tbody>
            </table>
          </div>
          <div className="mt-4">
            <Link href="/admin/candidates" className="text-blue-600 dark:text-blue-400 text-sm">
              View all candidates →
            </Link>
          </div>
        </div>
      </div>
    </div>
  );
}
