import { useFirestoreData } from "@/hooks/useFirestoreData";
import { StudyField } from "@/types";
import { useLanguage } from "@/contexts/LanguageContext";

interface StudyFieldSelectProps {
  value: string;
  onChange: (value: string) => void;
  placeholder?: string;
  className?: string;
  required?: boolean;
}

export default function StudyFieldSelect({
  value,
  onChange,
  placeholder = "Select field of study",
  className = "",
  required = false,
}: StudyFieldSelectProps) {
  const { language } = useLanguage();
  const { data: studyFields, loading, error } = useFirestoreData<StudyField>("studyFields");

  if (loading) {
    return (
      <select className={`${className} opacity-50`} disabled>
        <option>Loading study fields...</option>
      </select>
    );
  }

  if (error) {
    return (
      <select className={`${className} opacity-50`} disabled>
        <option>Error loading study fields</option>
      </select>
    );
  }

  return (
    <select
      value={value}
      onChange={(e) => onChange(e.target.value)}
      className={className}
      required={required}
    >
      <option value="">{placeholder}</option>
      {studyFields.map((field) => (
        <option key={field.id} value={field.id}>
          {field.translations[language] || field.translations.en || field.id}
        </option>
      ))}
    </select>
  );
}
