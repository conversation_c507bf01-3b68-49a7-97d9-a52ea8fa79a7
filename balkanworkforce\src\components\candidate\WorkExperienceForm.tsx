import { useState, useEffect } from "react";
import { WorkExperience } from "@/types";
import { positions } from "@/data/positions";
import { countries } from "@/data/countries";

interface WorkExperienceFormProps {
  value: WorkExperience[];
  onChange: (value: WorkExperience[]) => void;
  noWorkExperience: boolean;
  onNoWorkExperienceChange: (value: boolean) => void;
}

const industries = [
  { id: "construction", name: "Construction" },
  { id: "manufacturing", name: "Manufacturing" },
  { id: "healthcare", name: "Healthcare" },
  { id: "hospitality", name: "Hospitality" },
  { id: "retail", name: "Retail" },
  { id: "transportation", name: "Transportation" },
  { id: "it", name: "Information Technology" },
  { id: "finance", name: "Finance" },
  { id: "education", name: "Education" },
  { id: "agriculture", name: "Agriculture" },
  { id: "energy", name: "Energy" },
  { id: "telecommunications", name: "Telecommunications" },
  { id: "media", name: "Media" },
  { id: "government", name: "Government" },
  { id: "other", name: "Other" },
];

export default function WorkExperienceForm({
  value,
  onChange,
  noWorkExperience,
  onNoWorkExperienceChange,
}: WorkExperienceFormProps) {
  const [experiences, setExperiences] = useState<WorkExperience[]>(value || []);

  useEffect(() => {
    // Only update parent when experiences change, but avoid deep comparison
    // by using JSON.stringify to compare the current and previous values
    const handleChange = () => {
      onChange(experiences);
    };
    handleChange();
    // We're intentionally not including experiences in the dependency array
    // to prevent infinite loops, but still want to run this when onChange changes
  }, [onChange]);

  const addExperience = () => {
    setExperiences([
      ...experiences,
      {
        company: "",
        position: "",
        startDate: new Date(),
        current: false,
        description: "",
      },
    ]);
  };

  const updateExperience = (index: number, field: keyof WorkExperience, value: any) => {
    const updatedExperiences = [...experiences];
    updatedExperiences[index] = {
      ...updatedExperiences[index],
      [field]: value,
    };
    setExperiences(updatedExperiences);
  };

  const removeExperience = (index: number) => {
    const updatedExperiences = [...experiences];
    updatedExperiences.splice(index, 1);
    setExperiences(updatedExperiences);
  };

  const handleNoWorkExperienceChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const checked = e.target.checked;
    onNoWorkExperienceChange(checked);
    if (checked) {
      setExperiences([]);
    }
  };

  // Helper function to format date for input
  const formatDateForInput = (date: Date | undefined) => {
    if (!date) return "";
    // Check if date is valid
    const d = new Date(date);
    if (isNaN(d.getTime())) return "";
    return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, "0")}`;
  };

  // Helper function to parse date from input
  const parseDateFromInput = (dateString: string) => {
    if (!dateString) return new Date();
    try {
      const [year, month] = dateString.split("-").map(Number);
      if (isNaN(year) || isNaN(month)) return new Date();
      const date = new Date(year, month - 1);
      return date;
    } catch (error) {
      console.error("Error parsing date:", error);
      return new Date();
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex items-center mb-4">
        <input
          type="checkbox"
          id="noWorkExperience"
          checked={noWorkExperience}
          onChange={handleNoWorkExperienceChange}
          className="h-4 w-4 text-neutral-900 focus:ring-neutral-500 border-neutral-300 rounded"
        />
        <label htmlFor="noWorkExperience" className="ml-2 block text-sm text-neutral-700 dark:text-neutral-300">
          I have no work experience
        </label>
      </div>

      {!noWorkExperience && (
        <>
          {experiences.map((experience, index) => (
            <div key={index} className="border border-neutral-200 dark:border-neutral-700 rounded-lg p-4">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium">Work Experience #{index + 1}</h3>
                <button
                  type="button"
                  onClick={() => removeExperience(index)}
                  className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
                >
                  Remove
                </button>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Company <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="text"
                    value={experience.company}
                    onChange={(e) => updateExperience(index, "company", e.target.value)}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                    placeholder="Company name"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Position <span className="text-red-500">*</span>
                  </label>
                  <select
                    value={experience.positionId || ""}
                    onChange={(e) => {
                      const positionId = e.target.value;
                      const position = positions.find(p => p.id === positionId);
                      updateExperience(index, "positionId", positionId || undefined);
                      updateExperience(index, "position", position ? position.translations.en : "");
                    }}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                    required
                  >
                    <option value="">Select a position</option>
                    {positions.map((position) => (
                      <option key={position.id} value={position.id}>
                        {position.translations.en} ({position.category})
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Standardized position section removed - integrated directly into position field */}

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Start Date <span className="text-red-500">*</span>
                  </label>
                  <input
                    type="month"
                    value={formatDateForInput(experience.startDate)}
                    onChange={(e) => updateExperience(index, "startDate", parseDateFromInput(e.target.value))}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                    required
                  />
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    End Date {!experience.current && <span className="text-red-500">*</span>}
                  </label>
                  <input
                    type="month"
                    value={experience.current ? "" : formatDateForInput(experience.endDate)}
                    onChange={(e) => updateExperience(index, "endDate", parseDateFromInput(e.target.value))}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                    disabled={experience.current}
                    required={!experience.current}
                  />
                </div>
              </div>

              <div className="flex items-center mb-4">
                <input
                  type="checkbox"
                  id={`current-${index}`}
                  checked={experience.current || false}
                  onChange={(e) => {
                    updateExperience(index, "current", e.target.checked);
                    if (e.target.checked) {
                      updateExperience(index, "endDate", undefined);
                    }
                  }}
                  className="h-4 w-4 text-neutral-900 focus:ring-neutral-500 border-neutral-300 rounded"
                />
                <label htmlFor={`current-${index}`} className="ml-2 block text-sm text-neutral-700 dark:text-neutral-300">
                  I currently work here
                </label>
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Country
                  </label>
                  <select
                    value={experience.country || ""}
                    onChange={(e) => updateExperience(index, "country", e.target.value || undefined)}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                  >
                    <option value="">Select a country</option>
                    {countries.map((country) => (
                      <option key={country.id} value={country.id}>
                        {country.name}
                      </option>
                    ))}
                  </select>
                </div>

                <div>
                  <label className="block text-sm font-medium mb-1">
                    Industry
                  </label>
                  <select
                    value={experience.industry || ""}
                    onChange={(e) => updateExperience(index, "industry", e.target.value || undefined)}
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                  >
                    <option value="">Select an industry</option>
                    {industries.map((industry) => (
                      <option key={industry.id} value={industry.id}>
                        {industry.name}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Description
                </label>
                <textarea
                  value={experience.description || ""}
                  onChange={(e) => updateExperience(index, "description", e.target.value)}
                  rows={3}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                  placeholder="Describe your responsibilities and achievements"
                ></textarea>
              </div>
            </div>
          ))}

          <button
            type="button"
            onClick={addExperience}
            className="flex items-center text-neutral-700 dark:text-neutral-300 hover:text-neutral-900 dark:hover:text-white"
          >
            <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
              <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
            </svg>
            Add Work Experience
          </button>
        </>
      )}
    </div>
  );
}
