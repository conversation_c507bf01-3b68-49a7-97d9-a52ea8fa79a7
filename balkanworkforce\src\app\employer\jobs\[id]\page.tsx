"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { doc, getDoc, updateDoc, deleteDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import Link from "next/link";
import { jobBenefits } from "@/data/jobBenefits";
import Image from "next/image";

export default function EmployerJobDetailPage() {
  const { id } = useParams();
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [job, setJob] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [deleting, setDeleting] = useState(false);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);

  useEffect(() => {
    // If not authenticated or not an employer, redirect to auth page
    if (!authLoading && (!user || user.role !== "employer")) {
      router.push("/auth");
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    const fetchJob = async () => {
      if (!user || !id || typeof id !== "string") return;

      try {
        const jobDoc = await getDoc(doc(db, "jobs", id));

        if (!jobDoc.exists()) {
          router.push("/employer/dashboard");
          return;
        }

        const jobData = jobDoc.data();

        // Check if this job belongs to the current employer
        if (jobData.employerId !== user.uid) {
          router.push("/employer/dashboard");
          return;
        }

        setJob({
          id: jobDoc.id,
          ...jobData,
          createdAt: jobData.createdAt.toDate(),
          updatedAt: jobData.updatedAt.toDate(),
          premiumUntil: jobData.premiumUntil ? jobData.premiumUntil.toDate() : undefined,
        });
      } catch (error) {
        console.error("Error fetching job:", error);
        setError("Failed to load job details. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    if (user && user.role === "employer") {
      fetchJob();
    }
  }, [user, id, router]);

  const handleDeleteJob = async () => {
    if (!user || !id || typeof id !== "string") return;

    setDeleting(true);

    try {
      await deleteDoc(doc(db, "jobs", id));
      router.push("/employer/dashboard");
    } catch (error) {
      console.error("Error deleting job:", error);
      setError("Failed to delete job. Please try again.");
      setDeleting(false);
    }
  };

  const getBenefitLabel = (benefitId: string) => {
    const benefit = jobBenefits.find(b => b.id === benefitId);
    return benefit ? benefit.label : benefitId;
  };

  if (authLoading || loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto py-12">
        <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6">
          {error}
        </div>
        <Link
          href="/employer/dashboard"
          className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
        >
          Back to Dashboard
        </Link>
      </div>
    );
  }

  if (!job) {
    return (
      <div className="max-w-4xl mx-auto py-12">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Job Not Found</h2>
          <p className="text-neutral-600 dark:text-neutral-400 mb-6">
            The job you're looking for doesn't exist or you don't have permission to view it.
          </p>
          <Link
            href="/employer/dashboard"
            className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
          >
            Back to Dashboard
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto py-12">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Job Details</h1>
        <div className="flex gap-4">
          <Link
            href={`/employer/jobs/${id}/edit`}
            className="bg-neutral-100 hover:bg-neutral-200 dark:bg-neutral-700 dark:hover:bg-neutral-600 text-neutral-900 dark:text-white px-4 py-2 rounded-md"
          >
            Edit Job
          </Link>
          <Link
            href="/employer/dashboard"
            className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white px-4 py-2"
          >
            Back to Dashboard
          </Link>
        </div>
      </div>

      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8 mb-8">
        <div className="flex justify-between items-start mb-6">
          <div>
            <h2 className="text-2xl font-bold mb-2">{job.title}</h2>
            <div className="flex flex-wrap gap-2">
              <span className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 px-3 py-1 rounded-full text-sm">
                {job.location}
              </span>
              <span className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 px-3 py-1 rounded-full text-sm">
                {job.employmentType.replace("-", " ")}
              </span>
              {job.salary && (
                <span className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 px-3 py-1 rounded-full text-sm">
                  {job.salary.min} - {job.salary.max} {job.salary.currency}
                </span>
              )}
            </div>
          </div>
          <div className="flex items-center">
            {job.isPremium && (
              <span className="bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-100 px-3 py-1 rounded-full text-sm font-medium mr-4">
                Premium
              </span>
            )}
            <span className="text-sm text-neutral-500 dark:text-neutral-400">
              Posted on {job.createdAt.toLocaleDateString()}
            </span>
          </div>
        </div>

        <div className="border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6">
          <h3 className="text-xl font-semibold mb-4">Job Description</h3>
          <div className="prose dark:prose-invert max-w-none">
            <p className="whitespace-pre-line">{job.description}</p>
          </div>
        </div>

        <div className="border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6">
          <h3 className="text-xl font-semibold mb-4">Requirements</h3>
          <ul className="list-disc pl-5 space-y-2">
            {job.requirements.map((requirement: string, index: number) => (
              <li key={index}>{requirement}</li>
            ))}
          </ul>
        </div>

        <div className="border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6">
          <h3 className="text-xl font-semibold mb-4">Benefits</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
            {job.benefits.map((benefitId: string) => (
              <div key={benefitId} className="flex items-center">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
                <span>{getBenefitLabel(benefitId)}</span>
              </div>
            ))}
          </div>
        </div>

        {job.tags && job.tags.length > 0 && (
          <div className="border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6">
            <h3 className="text-xl font-semibold mb-4">Tags</h3>
            <div className="flex flex-wrap gap-2">
              {job.tags.map((tag: string, index: number) => (
                <span
                  key={index}
                  className="bg-neutral-100 dark:bg-neutral-700 text-neutral-600 dark:text-neutral-400 px-3 py-1 rounded-full text-sm"
                >
                  {tag}
                </span>
              ))}
            </div>
          </div>
        )}

        <div className="border-t border-neutral-200 dark:border-neutral-700 pt-6">
          <div className="flex justify-between items-center">
            <div>
              <h3 className="text-xl font-semibold mb-2">Applications</h3>
              <p className="text-neutral-600 dark:text-neutral-400">
                {job.applications.length} application{job.applications.length !== 1 ? 's' : ''}
              </p>
            </div>
            <Link
              href={`/employer/applications?jobId=${id}`}
              className="bg-neutral-900 hover:bg-neutral-800 text-white px-4 py-2 rounded-md"
            >
              View Applications
            </Link>
          </div>
        </div>
      </div>

      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8">
        <h3 className="text-xl font-semibold mb-6">Job Actions</h3>

        <div className="flex flex-wrap gap-4">
          {job.isPremium && job.premiumUntil && new Date() < job.premiumUntil ? (
            <div className="mb-4 w-full">
              <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
                <h4 className="text-lg font-medium text-amber-800 dark:text-amber-400 mb-2">
                  Premium Job
                </h4>
                <p className="text-amber-700 dark:text-amber-500 mb-2">
                  This job is promoted until {job.premiumUntil.toLocaleDateString()}
                </p>
                <Link
                  href={`/employer/jobs/${id}/promote`}
                  className="text-amber-800 dark:text-amber-400 underline hover:no-underline"
                >
                  Extend Premium or Add More Countries
                </Link>
              </div>
            </div>
          ) : (
            <Link
              href={`/employer/jobs/${id}/promote`}
              className="bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-md"
            >
              Promote to Premium
            </Link>
          )}

          <Link
            href={`/jobs/${id}`}
            className="bg-neutral-100 hover:bg-neutral-200 dark:bg-neutral-700 dark:hover:bg-neutral-600 text-neutral-900 dark:text-white px-4 py-2 rounded-md"
          >
            View Public Listing
          </Link>

          <button
            onClick={() => setShowDeleteConfirm(true)}
            className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md"
          >
            Delete Job
          </button>
        </div>

        {showDeleteConfirm && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white dark:bg-neutral-800 rounded-lg p-6 max-w-md w-full">
              <h3 className="text-xl font-bold mb-4">Confirm Deletion</h3>
              <p className="text-neutral-600 dark:text-neutral-400 mb-6">
                Are you sure you want to delete this job? This action cannot be undone.
              </p>
              <div className="flex justify-end gap-4">
                <button
                  onClick={() => setShowDeleteConfirm(false)}
                  className="px-4 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                >
                  Cancel
                </button>
                <button
                  onClick={handleDeleteJob}
                  disabled={deleting}
                  className="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-md"
                >
                  {deleting ? "Deleting..." : "Delete Job"}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
