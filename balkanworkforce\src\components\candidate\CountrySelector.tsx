import { useState, useEffect } from "react";
import Image from "next/image";
import { useFirestoreData } from "@/hooks/useFirestoreData";
import { Country } from "@/types";
import { useLanguage } from "@/contexts/LanguageContext";

interface CountrySelectorProps {
  value: string;
  onChange: (value: string) => void;
  required?: boolean;
}

export default function CountrySelector({ value, onChange, required = false }: CountrySelectorProps) {
  const { language } = useLanguage();
  const { data: countries, loading } = useFirestoreData<Country>("countries");
  const [selectedCountry, setSelectedCountry] = useState<string>(value || "");
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");

  useEffect(() => {
    // Only update internal state when the prop value changes
    if (value !== selectedCountry) {
      setSelectedCountry(value || "");
    }
  }, [value, selectedCountry]);

  // Only call onChange when the user makes a selection, not on every render
  // This is handled in handleSelect instead of using a useEffect

  const handleSelect = (countryId: string) => {
    setSelectedCountry(countryId);
    setIsOpen(false);
    setSearchTerm("");
    // Call onChange directly when user makes a selection
    onChange(countryId);
  };

  const filteredCountries = searchTerm && countries
    ? countries.filter(country =>
        (country.translations?.[language] || country.name).toLowerCase().includes(searchTerm.toLowerCase())
      )
    : countries || [];

  // Group countries by region
  const balkanCountries = filteredCountries.filter(country => country.region === "balkans");
  const euCountries = filteredCountries.filter(country => country.region === "eu");
  const otherCountries = filteredCountries.filter(country => country.region !== "balkans" && country.region !== "eu");

  // Get selected country details
  const selected = countries?.find(country => country.id === selectedCountry);

  return (
    <div className="relative">
      <label className="block text-sm font-medium mb-1">
        Country of Origin {required && <span className="text-red-500">*</span>}
      </label>

      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md bg-white dark:bg-neutral-800"
      >
        <div className="flex items-center">
          {selected && selected.flag && (
            <span className="text-xl mr-2">{selected.flag}</span>
          )}
          <span>{selected ? (selected.translations?.[language] || selected.name) : "Select a country"}</span>
        </div>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`h-5 w-5 transition-transform ${isOpen ? "transform rotate-180" : ""}`}
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute z-10 mt-1 w-full bg-white dark:bg-neutral-800 border border-neutral-300 dark:border-neutral-700 rounded-md shadow-lg max-h-60 overflow-auto">
          <div className="sticky top-0 bg-white dark:bg-neutral-800 p-2 border-b border-neutral-300 dark:border-neutral-700">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search countries..."
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              onClick={(e) => e.stopPropagation()}
            />
          </div>

          {balkanCountries.length > 0 && (
            <div>
              <div className="px-3 py-1 text-xs font-semibold text-neutral-500 dark:text-neutral-400 bg-neutral-100 dark:bg-neutral-700">
                Balkan Countries
              </div>
              {balkanCountries.map((country) => (
                <button
                  key={country.id}
                  type="button"
                  onClick={() => handleSelect(country.id)}
                  className={`w-full text-left px-3 py-2 flex items-center hover:bg-neutral-100 dark:hover:bg-neutral-700 ${
                    selectedCountry === country.id ? "bg-neutral-100 dark:bg-neutral-700" : ""
                  }`}
                >
                  {country.flag && (
                    <span className="text-xl mr-2">{country.flag}</span>
                  )}
                  {country.translations?.[language] || country.name}
                </button>
              ))}
            </div>
          )}

          {euCountries.length > 0 && (
            <div>
              <div className="px-3 py-1 text-xs font-semibold text-neutral-500 dark:text-neutral-400 bg-neutral-100 dark:bg-neutral-700">
                European Union
              </div>
              {euCountries.map((country) => (
                <button
                  key={country.id}
                  type="button"
                  onClick={() => handleSelect(country.id)}
                  className={`w-full text-left px-3 py-2 flex items-center hover:bg-neutral-100 dark:hover:bg-neutral-700 ${
                    selectedCountry === country.id ? "bg-neutral-100 dark:bg-neutral-700" : ""
                  }`}
                >
                  {country.flag && (
                    <span className="text-xl mr-2">{country.flag}</span>
                  )}
                  {country.translations?.[language] || country.name}
                </button>
              ))}
            </div>
          )}

          {otherCountries.length > 0 && (
            <div>
              <div className="px-3 py-1 text-xs font-semibold text-neutral-500 dark:text-neutral-400 bg-neutral-100 dark:bg-neutral-700">
                Other Countries
              </div>
              {otherCountries.map((country) => (
                <button
                  key={country.id}
                  type="button"
                  onClick={() => handleSelect(country.id)}
                  className={`w-full text-left px-3 py-2 flex items-center hover:bg-neutral-100 dark:hover:bg-neutral-700 ${
                    selectedCountry === country.id ? "bg-neutral-100 dark:bg-neutral-700" : ""
                  }`}
                >
                  {country.flag && (
                    <span className="text-xl mr-2">{country.flag}</span>
                  )}
                  {country.translations?.[language] || country.name}
                </button>
              ))}
            </div>
          )}

          {filteredCountries.length === 0 && (
            <div className="px-3 py-2 text-neutral-500 dark:text-neutral-400">
              No countries found
            </div>
          )}
        </div>
      )}
    </div>
  );
}
