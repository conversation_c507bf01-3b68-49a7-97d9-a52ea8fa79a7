module.exports = {

"[project]/src/data/studyFields.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "studyFields": (()=>studyFields)
});
const studyFields = [
    {
        id: "electrical-engineering",
        translations: {
            de: "Elektrotechnik",
            en: "Electrical Engineering",
            bs: "Elektrotehnika",
            hr: "Elektrotehnika",
            sr: "Elektrotehnika",
            me: "Elektrotehnika",
            mk: "Електротехника",
            al: "Inxhinieri Elektrike"
        },
        category: "engineering"
    },
    {
        id: "mechanical-engineering",
        translations: {
            de: "Maschinenbau",
            en: "Mechanical Engineering",
            bs: "Mašinstvo",
            hr: "Strojarstvo",
            sr: "<PERSON><PERSON>inst<PERSON>",
            me: "<PERSON><PERSON>inst<PERSON>",
            mk: "Машинство",
            al: "Inxhinieri Mekanike"
        },
        category: "engineering"
    },
    {
        id: "automotive-mechanic",
        translations: {
            de: "Kfz-Mechaniker",
            en: "Automotive Mechanic",
            bs: "Automehaničar",
            hr: "Automehaničar",
            sr: "Automehaničar",
            me: "Automehaničar",
            mk: "Автомеханичар",
            al: "Mekanik Automjetesh"
        },
        category: "vocational"
    },
    {
        id: "automotive-painter",
        translations: {
            de: "Kfz-Lackierer",
            en: "Automotive Painter",
            bs: "Autolakirer",
            hr: "Autolakirer",
            sr: "Autolakirer",
            me: "Autolakirer",
            mk: "Автолакер",
            al: "Bojaxhi Automjetesh"
        },
        category: "vocational"
    },
    {
        id: "carpentry",
        translations: {
            de: "Tischlerei",
            en: "Carpentry",
            bs: "Stolarstvo",
            hr: "Stolarstvo",
            sr: "Stolarstvo",
            me: "Stolarstvo",
            mk: "Столарство",
            al: "Marangozi"
        },
        category: "vocational"
    },
    {
        id: "plumbing",
        translations: {
            de: "Sanitärinstallation",
            en: "Plumbing",
            bs: "Vodoinstalaterstvo",
            hr: "Vodoinstalaterstvo",
            sr: "Vodoinstalaterstvo",
            me: "Vodoinstalaterstvo",
            mk: "Водоинсталатерство",
            al: "Hidraulikë"
        },
        category: "vocational"
    },
    {
        id: "welding",
        translations: {
            de: "Schweißen",
            en: "Welding",
            bs: "Zavarivanje",
            hr: "Zavarivanje",
            sr: "Zavarivanje",
            me: "Zavarivanje",
            mk: "Заварување",
            al: "Saldim"
        },
        category: "vocational"
    },
    {
        id: "culinary-arts",
        translations: {
            de: "Kochkunst",
            en: "Culinary Arts",
            bs: "Kuharstvo",
            hr: "Kuharstvo",
            sr: "Kuvarstvo",
            me: "Kuvarstvo",
            mk: "Готварство",
            al: "Arti Kulinar"
        },
        category: "hospitality"
    },
    {
        id: "nursing",
        translations: {
            de: "Krankenpflege",
            en: "Nursing",
            bs: "Sestrinstvo",
            hr: "Sestrinstvo",
            sr: "Sestrinstvo",
            me: "Sestrinstvo",
            mk: "Медицинска нега",
            al: "Infermieri"
        },
        category: "healthcare"
    },
    {
        id: "general-high-school",
        translations: {
            de: "Allgemeine Hochschulreife",
            en: "General High School",
            bs: "Gimnazija",
            hr: "Gimnazija",
            sr: "Gimnazija",
            me: "Gimnazija",
            mk: "Гимназија",
            al: "Gjimnaz"
        },
        category: "general-education"
    }
];
}}),

};

//# sourceMappingURL=src_data_studyFields_ts_4ffb691d._.js.map