# Firebase Rules Update Instructions

To allow unauthenticated users to view job listings and fix login issues, you need to update your Firebase security rules. Follow these steps:

## 1. Firestore Rules

1. Go to your Firebase Console: https://console.firebase.google.com/
2. Select your project
3. Navigate to Firestore Database in the left sidebar
4. Click on the "Rules" tab
5. Replace the current rules with the following:

```
rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Allow anyone to read jobs
    match /jobs/{jobId} {
      allow read: if true;
      allow write: if request.auth != null &&
                    get(/databases/$(database)/documents/employers/$(request.auth.uid)).data.role == "employer";
    }

    // Users collection - needed for authentication
    match /users/{userId} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow write: if request.auth != null && request.auth.uid == userId;
    }

    // Employers collection
    match /employers/{employerId} {
      allow read: if true;
      allow create: if request.auth != null && request.auth.uid == employerId;
      allow update, delete: if request.auth != null && request.auth.uid == employerId;
    }

    // Candidates collection
    match /candidates/{candidateId} {
      allow read: if true;
      allow create: if request.auth != null && request.auth.uid == candidateId;
      allow update, delete: if request.auth != null && request.auth.uid == candidateId;
    }

    // Applications collection
    match /applications/{applicationId} {
      allow read: if request.auth != null && (
        request.auth.uid == resource.data.candidateId ||
        request.auth.uid == resource.data.employerId
      );
      allow create: if request.auth != null && request.auth.uid == request.resource.data.candidateId;
      allow update: if request.auth != null && (
        request.auth.uid == resource.data.candidateId ||
        request.auth.uid == resource.data.employerId
      );
      allow delete: if request.auth != null && (
        request.auth.uid == resource.data.candidateId ||
        request.auth.uid == resource.data.employerId
      );
    }

    // Invitations collection
    match /invitations/{invitationId} {
      allow read: if request.auth != null && (
        request.auth.uid == resource.data.candidateId ||
        request.auth.uid == resource.data.employerId
      );
      allow create: if request.auth != null && request.auth.uid == request.resource.data.employerId;
      allow update: if request.auth != null && (
        request.auth.uid == resource.data.candidateId ||
        request.auth.uid == resource.data.employerId
      );
      allow delete: if request.auth != null && (
        request.auth.uid == resource.data.candidateId ||
        request.auth.uid == resource.data.employerId
      );
    }
  }
}
```

6. Click "Publish" to save the rules

## 2. Storage Rules

1. Navigate to Storage in the left sidebar
2. Click on the "Rules" tab
3. Replace the current rules with the following:

```
rules_version = '2';

service firebase.storage {
  match /b/{bucket}/o {
    // Allow public read access to profile images and company logos
    match /profile-images/{userId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == userId;
    }

    match /company-logos/{employerId} {
      allow read: if true;
      allow write: if request.auth != null && request.auth.uid == employerId;
    }

    // Allow authenticated users to upload resumes
    match /resumes/{userId}/{fileName} {
      allow read: if request.auth != null && (
        request.auth.uid == userId ||
        exists(/databases/$(database)/documents/applications/{applicationId}) &&
        get(/databases/$(database)/documents/applications/{applicationId}).data.candidateId == userId &&
        get(/databases/$(database)/documents/applications/{applicationId}).data.employerId == request.auth.uid
      );
      allow write: if request.auth != null && request.auth.uid == userId;
    }

    // Default rule - deny all other access
    match /{allPaths=**} {
      allow read, write: if false;
    }
  }
}
```

4. Click "Publish" to save the rules

## 3. Restart Your Application

After updating the Firebase rules, restart your application to ensure the changes take effect:

```
npm run dev
```

Now unauthenticated users should be able to view job listings and users should be able to log in without permission errors.
