"use client";

import { useState, useEffect } from "react";
import { collection, getDocs, query, where, orderBy, limit } from "firebase/firestore";
import { db } from "@/lib/firebase";
import Link from "next/link";
import { JobListing } from "@/types";
import { jobBenefits } from "@/data/jobBenefits";
import { countries } from "@/data/countries";

export default function JobsPage() {
  const [jobs, setJobs] = useState<JobListing[]>([]);
  const [loading, setLoading] = useState(true);
  const [filter, setFilter] = useState({
    location: "",
    employmentType: "",
    keyword: "",
    country: "", // Added country filter
  });

  // Get user's country from browser locale or IP (simplified version)
  const [userCountry, setUserCountry] = useState<string | null>(null);

  useEffect(() => {
    // In a real app, you would use a geolocation service
    // For now, we'll just use the browser's language
    const browserLang = navigator.language || 'en';
    const countryCode = browserLang.split('-')[1]?.toLowerCase() || null;
    setUserCountry(countryCode);
  }, []);

  useEffect(() => {
    const fetchJobs = async () => {
      try {
        // For now, we'll just fetch all jobs
        // In a real implementation, we would add filters based on the filter state
        const jobsCollection = collection(db, "jobs");
        console.log("Jobs collection reference:", jobsCollection);

        // Log all documents in the jobs collection
        const allJobsSnapshot = await getDocs(jobsCollection);
        console.log("All jobs in collection:", allJobsSnapshot.size);
        allJobsSnapshot.forEach(doc => {
          console.log("Job document:", doc.id, doc.data());
        });

        // Sort jobs by premium status and creation date
        // First get all premium jobs for the user's country
        let jobsQuery;

        if (userCountry) {
          // If we know the user's country, prioritize premium jobs for that country
          jobsQuery = query(
            jobsCollection,
            where("premiumCountries", "array-contains", userCountry),
            where("isPremium", "==", true),
            orderBy("createdAt", "desc")
          );
        } else {
          // Otherwise, just get all jobs sorted by creation date
          jobsQuery = query(
            jobsCollection,
            orderBy("createdAt", "desc")
          );
        }

        const querySnapshot = await getDocs(jobsQuery);
        const jobsList: JobListing[] = [];

        console.log("Jobs page - Number of jobs found:", querySnapshot.size);

        querySnapshot.forEach((doc) => {
          const data = doc.data() as Omit<JobListing, "id">;
          jobsList.push({
            id: doc.id,
            ...data,
            createdAt: data.createdAt.toDate(),
            updatedAt: data.updatedAt.toDate(),
            premiumUntil: data.premiumUntil ? data.premiumUntil.toDate() : undefined,
          });
        });

        console.log("Jobs page - Processed jobs:", jobsList);

        // Now get all other jobs
        let allJobsQuery = query(
          jobsCollection,
          orderBy("createdAt", "desc")
        );

        const allJobsQuerySnapshot = await getDocs(allJobsQuery);
        const allJobsList: JobListing[] = [];

        // Add all jobs that aren't already in the premium jobs list
        allJobsQuerySnapshot.forEach((doc) => {
          // Skip if this job is already in the premium jobs list
          if (jobsList.some(job => job.id === doc.id)) {
            return;
          }

          const data = doc.data() as Omit<JobListing, "id">;
          allJobsList.push({
            id: doc.id,
            ...data,
            createdAt: data.createdAt.toDate(),
            updatedAt: data.updatedAt.toDate(),
            premiumUntil: data.premiumUntil ? data.premiumUntil.toDate() : undefined,
          });
        });

        // Combine premium jobs and regular jobs
        setJobs([...jobsList, ...allJobsList]);
      } catch (error) {
        console.error("Error fetching jobs:", error);
      } finally {
        setLoading(false);
      }
    };

    fetchJobs();
  }, []);

  const handleFilterChange = (e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFilter((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  // This would be implemented in a real application
  const applyFilters = () => {
    // Re-fetch jobs with filters
    console.log("Applying filters:", filter);
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto py-12">
      <h1 className="text-3xl font-bold mb-8">Available Jobs</h1>

      {/* Filter Section */}
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6 mb-8">
        <h2 className="text-xl font-semibold mb-4">Filter Jobs</h2>
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <div>
            <label htmlFor="keyword" className="block text-sm font-medium mb-1">
              Keyword
            </label>
            <input
              type="text"
              id="keyword"
              name="keyword"
              value={filter.keyword}
              onChange={handleFilterChange}
              placeholder="Job title, skills, etc."
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
            />
          </div>
          <div>
            <label htmlFor="location" className="block text-sm font-medium mb-1">
              Location
            </label>
            <input
              type="text"
              id="location"
              name="location"
              value={filter.location}
              onChange={handleFilterChange}
              placeholder="City, country"
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
            />
          </div>
          <div>
            <label htmlFor="country" className="block text-sm font-medium mb-1">
              Country
            </label>
            <select
              id="country"
              name="country"
              value={filter.country}
              onChange={handleFilterChange}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
            >
              <option value="">All Countries</option>
              {countries.map(country => (
                <option key={country.id} value={country.id}>
                  {country.name}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label htmlFor="employmentType" className="block text-sm font-medium mb-1">
              Employment Type
            </label>
            <select
              id="employmentType"
              name="employmentType"
              value={filter.employmentType}
              onChange={handleFilterChange}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
            >
              <option value="">All Types</option>
              <option value="full-time">Full-time</option>
              <option value="part-time">Part-time</option>
              <option value="contract">Contract</option>
              <option value="temporary">Temporary</option>
              <option value="internship">Internship</option>
            </select>
          </div>
        </div>
        <button
          onClick={applyFilters}
          className="mt-4 bg-neutral-900 hover:bg-neutral-800 text-white px-4 py-2 rounded-md"
        >
          Apply Filters
        </button>
      </div>

      {/* Job Listings */}
      {jobs.length === 0 ? (
        <div className="text-center py-12">
          <h3 className="text-xl font-medium mb-2">No jobs found</h3>
          <p className="text-neutral-600 dark:text-neutral-400">
            Try adjusting your filters or check back later for new opportunities.
          </p>
        </div>
      ) : (
        <div className="space-y-6">
          {jobs.map((job) => (
            <div
              key={job.id}
              className={`bg-white dark:bg-neutral-800 rounded-lg shadow-sm border ${
                job.isPremium
                  ? "border-amber-400 dark:border-amber-500"
                  : "border-neutral-200 dark:border-neutral-700"
              } p-6 relative`}
            >
              {job.isPremium && (
                <div className="absolute top-4 right-4 bg-amber-100 dark:bg-amber-900 text-amber-800 dark:text-amber-100 text-xs font-medium px-2 py-1 rounded-full flex items-center">
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                    <path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118L2.98 8.72c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
                  </svg>
                  Premium
                </div>
              )}

              {/* Show country badges for premium jobs */}
              {job.isPremium && job.premiumCountries && job.premiumCountries.length > 0 && (
                <div className="absolute top-12 right-4 flex space-x-1">
                  {job.premiumCountries.slice(0, 3).map(countryId => {
                    const country = countries.find(c => c.id === countryId);
                    return country ? (
                      <div key={country.id} className="w-6 h-4 overflow-hidden rounded-sm" title={country.name}>
                        {country.flagUrl && (
                          <img
                            src={country.flagUrl}
                            alt={country.name}
                            className="w-full h-full object-cover"
                          />
                        )}
                      </div>
                    ) : null;
                  })}
                  {job.premiumCountries.length > 3 && (
                    <span className="text-xs text-neutral-500">+{job.premiumCountries.length - 3}</span>
                  )}
                </div>
              )}
              <h3 className="text-xl font-semibold mb-2">{job.title}</h3>
              <div className="flex flex-wrap gap-2 mb-4">
                <span className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 text-xs px-2 py-1 rounded">
                  {job.location}
                </span>
                <span className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 text-xs px-2 py-1 rounded">
                  {job.employmentType.replace("-", " ")}
                </span>
                {job.salary && (
                  <span className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 text-xs px-2 py-1 rounded">
                    {job.salary.min} - {job.salary.max} {job.salary.currency}
                  </span>
                )}
              </div>
              <p className="text-neutral-600 dark:text-neutral-400 mb-4 line-clamp-3">
                {job.description}
              </p>
              <div className="flex flex-wrap gap-2 mb-4">
                {job.tags?.slice(0, 5).map((tag, index) => (
                  <span
                    key={index}
                    className="bg-neutral-100 dark:bg-neutral-700 text-neutral-600 dark:text-neutral-400 text-xs px-2 py-1 rounded"
                  >
                    {tag}
                  </span>
                ))}
                {job.tags?.length > 5 && (
                  <span className="text-neutral-500 text-xs">
                    +{job.tags.length - 5} more
                  </span>
                )}
              </div>

              {job.benefits && job.benefits.length > 0 && (
                <div className="mb-4">
                  <h4 className="text-sm font-medium mb-2">Benefits:</h4>
                  <div className="flex flex-wrap gap-2">
                    {job.benefits.slice(0, 3).map((benefitId: string) => {
                      const benefit = jobBenefits.find(b => b.id === benefitId);
                      return (
                        <span
                          key={benefitId}
                          className="bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 text-xs px-2 py-1 rounded-full flex items-center"
                        >
                          <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                            <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                          </svg>
                          {benefit ? benefit.label : benefitId}
                        </span>
                      );
                    })}
                    {job.benefits.length > 3 && (
                      <span className="text-green-600 dark:text-green-400 text-xs">
                        +{job.benefits.length - 3} more
                      </span>
                    )}
                  </div>
                </div>
              )}
              <div className="flex justify-between items-center">
                <span className="text-sm text-neutral-500">
                  Posted {new Date(job.createdAt).toLocaleDateString()}
                </span>
                <Link
                  href={`/jobs/${job.id}`}
                  className="bg-neutral-900 hover:bg-neutral-800 text-white px-4 py-2 rounded-md text-sm"
                >
                  View Details
                </Link>
              </div>
            </div>
          ))}
        </div>
      )}
    </div>
  );
}
