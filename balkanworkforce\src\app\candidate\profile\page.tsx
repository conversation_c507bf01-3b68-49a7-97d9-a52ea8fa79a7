"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import { doc, getDoc, setDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { z } from "zod";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { jobBenefits, MIN_BENEFITS_REQUIRED } from "@/data/jobBenefits";
import { Gender, LanguageLevel, WorkExperience, Education, LanguageSkill } from "@/types";
import WorkExperienceForm from "@/components/candidate/WorkExperienceForm";
import EducationForm from "@/components/candidate/EducationForm";
import LanguageSkillsForm from "@/components/candidate/LanguageSkillsForm";
import DriverLicenseForm from "@/components/candidate/DriverLicenseForm";
import CountrySelector from "@/components/candidate/CountrySelector";
import LimitedCountrySelector from "@/components/candidate/LimitedCountrySelector";

// Form validation schema
const candidateProfileSchema = z.object({
  profession: z.string().min(2, "Profession is required"),
  age: z.coerce.number().min(18, "Age must be at least 18").max(100, "Age must be less than 100"),
  gender: z.string().min(1, "Gender is required"),
  country: z.string().optional(),
});

type CandidateProfileFormValues = z.infer<typeof candidateProfileSchema>;

export default function CandidateProfilePage() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [profileData, setProfileData] = useState<any>(null);
  const [selectedBenefits, setSelectedBenefits] = useState<string[]>([]);
  const [benefitsError, setBenefitsError] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<boolean>(false);

  // State for structured data
  const [gender, setGender] = useState<Gender>("male");
  const [country, setCountry] = useState<string>("");
  const [workExperiences, setWorkExperiences] = useState<WorkExperience[]>([]);
  const [noWorkExperience, setNoWorkExperience] = useState<boolean>(false);
  const [educations, setEducations] = useState<Education[]>([]);
  const [germanLevel, setGermanLevel] = useState<LanguageLevel>("None");
  const [englishLevel, setEnglishLevel] = useState<LanguageLevel>("None");
  const [otherLanguages, setOtherLanguages] = useState<LanguageSkill[]>([]);
  const [desiredCountries, setDesiredCountries] = useState<string[]>([]);
  const [driverLicense, setDriverLicense] = useState<string>("none");

  const {
    register,
    handleSubmit,
    setValue,
    formState: { errors },
  } = useForm<CandidateProfileFormValues>({
    resolver: zodResolver(candidateProfileSchema),
    defaultValues: {
      profession: "",
      age: undefined,
      gender: "male",
      country: "",
    },
  });

  useEffect(() => {
    // If not authenticated or not a candidate, redirect to auth page
    if (!authLoading && (!user || user.role !== "candidate")) {
      router.push("/auth");
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    const fetchCandidateProfile = async () => {
      if (!user) return;

      try {
        const candidateDoc = await getDoc(doc(db, "candidates", user.uid));

        if (candidateDoc.exists()) {
          const data = candidateDoc.data();
          setProfileData(data);

          // Set form values
          setValue("profession", data.profession || "");
          setValue("age", data.age || "");
          setValue("gender", data.gender || "male");
          setValue("country", data.country || "");
          // No longer needed fields have been removed

          // Set structured data state
          setGender(data.gender || "male");
          setCountry(data.country || "");
          setWorkExperiences(data.workExperience || []);
          setNoWorkExperience(data.noWorkExperience || false);
          setEducations(Array.isArray(data.education) ? data.education : []);
          setGermanLevel(data.germanLanguageLevel || "None");
          setEnglishLevel(data.englishLanguageLevel || "None");
          setOtherLanguages(data.otherLanguages || []);
          setDesiredCountries(Array.isArray(data.desiredCountries) ? data.desiredCountries : []);
          setDriverLicense(data.driverLicense || "none");

          // Set selected benefits if exists
          if (data.benefits && Array.isArray(data.benefits)) {
            setSelectedBenefits(data.benefits);
          }
        }
      } catch (error) {
        console.error("Error fetching candidate profile:", error);
      } finally {
        setLoading(false);
      }
    };

    if (user && user.role === "candidate") {
      fetchCandidateProfile();
    }
  }, [user, setValue]);

  const toggleBenefit = (benefitId: string) => {
    setSelectedBenefits(prev => {
      if (prev.includes(benefitId)) {
        return prev.filter(id => id !== benefitId);
      } else {
        return [...prev, benefitId];
      }
    });

    // Clear error if enough benefits are selected
    if (selectedBenefits.length >= MIN_BENEFITS_REQUIRED - 1) {
      setBenefitsError(null);
    }
  };

  // Profile image functionality removed

  const onSubmit = async (data: CandidateProfileFormValues) => {
    if (!user) return;

    setSaving(true);
    setError(null);
    setSuccess(false);

    // Check if minimum required benefits are selected
    if (selectedBenefits.length < MIN_BENEFITS_REQUIRED) {
      setBenefitsError(`Please select at least ${MIN_BENEFITS_REQUIRED} job preferences`);
      setSaving(false);
      return;
    }

    // Validate work experience
    if (!noWorkExperience && workExperiences.length === 0) {
      setError("Please add at least one work experience or check 'I have no work experience'");
      setSaving(false);
      return;
    }

    // Validate education
    if (educations.length === 0) {
      setError("Please add at least one education entry");
      setSaving(false);
      return;
    }

    // Validate work experience fields
    if (!noWorkExperience) {
      for (const exp of workExperiences) {
        if (!exp.company || !exp.position || !exp.startDate || (!exp.current && !exp.endDate)) {
          setError("Please fill in all required fields in work experience");
          setSaving(false);
          return;
        }
      }
    }

    // Validate education fields
    for (const edu of educations) {
      if (!edu.degree || !edu.field || !edu.startDate || !edu.endDate) {
        setError("Please fill in all required fields in education");
        setSaving(false);
        return;
      }
    }

    try {
      // Process form data
      const candidateData = {
        uid: user.uid,
        email: user.email,
        profession: data.profession,
        age: data.age,
        gender: data.gender as Gender,
        country: data.country,
        workExperience: workExperiences,
        noWorkExperience: noWorkExperience,
        education: Array.isArray(educations) ? educations : [],
        germanLanguageLevel: germanLevel,
        englishLanguageLevel: englishLevel,
        otherLanguages: otherLanguages,
        desiredCountries: desiredCountries,
        driverLicense: driverLicense,
        benefits: selectedBenefits, // Add selected benefits
        profileCompleted: true, // Mark profile as complete
        updatedAt: new Date(),
      };

      // Save to Firestore
      await setDoc(
        doc(db, "candidates", user.uid),
        {
          ...candidateData,
          ...(profileData ? {} : { createdAt: new Date() }),
        },
        { merge: true }
      );

      setSuccess(true);
      setProfileData({
        ...candidateData,
        createdAt: profileData?.createdAt || new Date(),
      });

      // Scroll to top to show success message
      window.scrollTo(0, 0);
    } catch (error: any) {
      console.error("Error saving candidate profile:", error);
      setError(error.message || "Failed to save profile. Please try again.");
    } finally {
      setSaving(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto py-12">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Candidate Profile</h1>
        <Link
          href="/candidate/dashboard"
          className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
        >
          Back to Dashboard
        </Link>
      </div>

      {error && (
        <div className="mb-6 p-4 bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 rounded-md">
          {error}
        </div>
      )}

      {success && (
        <div className="mb-6 p-4 bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 rounded-md">
          Profile saved successfully!
        </div>
      )}

      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8">
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-2">Public Display</h2>
          <p className="text-neutral-600 dark:text-neutral-400 mb-4">
            Your name will be hidden. You will be shown as:
          </p>

          <div className="bg-neutral-50 dark:bg-neutral-900 p-4 rounded-md">
            <div className="text-xl font-medium">
              {profileData?.profession || "Your Profession"}, {profileData?.age || "XX"}
            </div>
          </div>
        </div>

        <form onSubmit={handleSubmit(onSubmit)}>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
            <div>
              <label htmlFor="profession" className="block text-sm font-medium mb-1">
                Profession <span className="text-red-500">*</span>
              </label>
              <input
                id="profession"
                {...register("profession")}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                placeholder="e.g., Electrician, Software Developer"
              />
              {errors.profession && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.profession.message}</p>
              )}
            </div>

            <div>
              <label htmlFor="age" className="block text-sm font-medium mb-1">
                Age <span className="text-red-500">*</span>
              </label>
              <input
                id="age"
                type="number"
                {...register("age")}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                placeholder="Your age"
              />
              {errors.age && (
                <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.age.message}</p>
              )}
            </div>
          </div>

          <div className="mb-6">
            <label className="block text-sm font-medium mb-1">
              Gender <span className="text-red-500">*</span>
            </label>
            <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
              <label className={`flex items-center p-3 border rounded-md cursor-pointer ${gender === "male" ? "border-neutral-900 dark:border-white bg-neutral-50 dark:bg-neutral-700" : "border-neutral-200 dark:border-neutral-700"}`}>
                <input
                  type="radio"
                  {...register("gender")}
                  value="male"
                  checked={gender === "male"}
                  onChange={() => setGender("male")}
                  className="sr-only"
                />
                <div className={`w-4 h-4 rounded-full border mr-2 flex items-center justify-center ${gender === "male" ? "border-neutral-900 dark:border-white" : "border-neutral-400 dark:border-neutral-500"}`}>
                  {gender === "male" && <div className="w-2 h-2 rounded-full bg-neutral-900 dark:bg-white"></div>}
                </div>
                <span>Male</span>
              </label>

              <label className={`flex items-center p-3 border rounded-md cursor-pointer ${gender === "female" ? "border-neutral-900 dark:border-white bg-neutral-50 dark:bg-neutral-700" : "border-neutral-200 dark:border-neutral-700"}`}>
                <input
                  type="radio"
                  {...register("gender")}
                  value="female"
                  checked={gender === "female"}
                  onChange={() => setGender("female")}
                  className="sr-only"
                />
                <div className={`w-4 h-4 rounded-full border mr-2 flex items-center justify-center ${gender === "female" ? "border-neutral-900 dark:border-white" : "border-neutral-400 dark:border-neutral-500"}`}>
                  {gender === "female" && <div className="w-2 h-2 rounded-full bg-neutral-900 dark:bg-white"></div>}
                </div>
                <span>Female</span>
              </label>

              <label className={`flex items-center p-3 border rounded-md cursor-pointer ${gender === "other" ? "border-neutral-900 dark:border-white bg-neutral-50 dark:bg-neutral-700" : "border-neutral-200 dark:border-neutral-700"}`}>
                <input
                  type="radio"
                  {...register("gender")}
                  value="other"
                  checked={gender === "other"}
                  onChange={() => setGender("other")}
                  className="sr-only"
                />
                <div className={`w-4 h-4 rounded-full border mr-2 flex items-center justify-center ${gender === "other" ? "border-neutral-900 dark:border-white" : "border-neutral-400 dark:border-neutral-500"}`}>
                  {gender === "other" && <div className="w-2 h-2 rounded-full bg-neutral-900 dark:bg-white"></div>}
                </div>
                <span>Other</span>
              </label>

              <label className={`flex items-center p-3 border rounded-md cursor-pointer ${gender === "prefer-not-to-say" ? "border-neutral-900 dark:border-white bg-neutral-50 dark:bg-neutral-700" : "border-neutral-200 dark:border-neutral-700"}`}>
                <input
                  type="radio"
                  {...register("gender")}
                  value="prefer-not-to-say"
                  checked={gender === "prefer-not-to-say"}
                  onChange={() => setGender("prefer-not-to-say")}
                  className="sr-only"
                />
                <div className={`w-4 h-4 rounded-full border mr-2 flex items-center justify-center ${gender === "prefer-not-to-say" ? "border-neutral-900 dark:border-white" : "border-neutral-400 dark:border-neutral-500"}`}>
                  {gender === "prefer-not-to-say" && <div className="w-2 h-2 rounded-full bg-neutral-900 dark:bg-white"></div>}
                </div>
                <span>Prefer not to say</span>
              </label>
            </div>
            {errors.gender && (
              <p className="mt-1 text-sm text-red-600 dark:text-red-400">{errors.gender.message}</p>
            )}
          </div>

          <div className="mb-6">
            <CountrySelector
              value={country}
              onChange={(value) => {
                setCountry(value);
                setValue("country", value);
              }}
              required
            />
          </div>

          <div className="mb-6">
            <LimitedCountrySelector
              value={desiredCountries}
              onChange={setDesiredCountries}
              required
              label="Desired Countries (where you want to work)"
            />
          </div>

          <div className="mb-8">
            <h3 className="text-lg font-medium mb-4">Work Experience <span className="text-red-500">*</span></h3>
            <WorkExperienceForm
              value={workExperiences}
              onChange={setWorkExperiences}
              noWorkExperience={noWorkExperience}
              onNoWorkExperienceChange={setNoWorkExperience}
            />
          </div>

          <div className="mb-8">
            <h3 className="text-lg font-medium mb-4">Education <span className="text-red-500">*</span></h3>
            <EducationForm
              value={educations}
              onChange={setEducations}
            />
          </div>

          <div className="mb-8">
            <h3 className="text-lg font-medium mb-4">Language Skills <span className="text-red-500">*</span></h3>
            <LanguageSkillsForm
              germanLevel={germanLevel}
              onGermanLevelChange={setGermanLevel}
              englishLevel={englishLevel}
              onEnglishLevelChange={setEnglishLevel}
              otherLanguages={otherLanguages}
              onOtherLanguagesChange={setOtherLanguages}
            />
          </div>

          <div className="mb-8">
            <h3 className="text-lg font-medium mb-4">Driver's License</h3>
            <DriverLicenseForm
              value={driverLicense}
              onChange={setDriverLicense}
            />
          </div>

          {/* Desired positions and additional job preferences sections removed */}

          <div className="mb-8">
            <h3 className="text-lg font-medium mb-2">
              Job Benefits Preferences <span className="text-red-500">*</span>
            </h3>
            <p className="text-sm text-neutral-600 dark:text-neutral-400 mb-4">
              Select at least {MIN_BENEFITS_REQUIRED} benefits that are important to you when looking for a job.
              These will be used to match you with suitable job opportunities.
            </p>

            {benefitsError && (
              <p className="mb-4 text-sm text-red-600 dark:text-red-400">{benefitsError}</p>
            )}

            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {jobBenefits.map((benefit) => (
                <div
                  key={benefit.id}
                  className={`p-3 rounded-md border cursor-pointer transition-all ${
                    selectedBenefits.includes(benefit.id)
                      ? "border-neutral-900 dark:border-white bg-neutral-50 dark:bg-neutral-700"
                      : "border-neutral-200 dark:border-neutral-700 hover:border-neutral-400 dark:hover:border-neutral-500"
                  }`}
                  onClick={() => toggleBenefit(benefit.id)}
                >
                  <div className="flex items-center">
                    <div className={`w-5 h-5 rounded-sm border flex items-center justify-center mr-3 ${
                      selectedBenefits.includes(benefit.id)
                        ? "bg-neutral-900 dark:bg-white border-neutral-900 dark:border-white"
                        : "border-neutral-400 dark:border-neutral-500"
                    }`}>
                      {selectedBenefits.includes(benefit.id) && (
                        <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 text-white dark:text-neutral-900" viewBox="0 0 20 20" fill="currentColor">
                          <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                        </svg>
                      )}
                    </div>
                    <div>
                      <h4 className="font-medium">{benefit.label}</h4>
                      <p className="text-xs text-neutral-500 dark:text-neutral-400">{benefit.description}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </div>

          <div className="flex justify-end">
            <button
              type="submit"
              disabled={saving}
              className={`px-6 py-3 rounded-md font-medium ${
                saving
                  ? "bg-neutral-700 text-white cursor-wait"
                  : "bg-neutral-900 hover:bg-neutral-800 text-white"
              }`}
            >
              {saving ? (
                <>
                  <svg className="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline-block" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
                    <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  Saving...
                </>
              ) : (
                "Save Profile"
              )}
            </button>
          </div>
        </form>
      </div>
    </div>
  );
}
