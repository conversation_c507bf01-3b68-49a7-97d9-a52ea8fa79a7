{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file://C%3A/Users/<USER>/Desktop/DEV/Software/BalkanWorkForce/balkanworkforce/src/app/admin/settings/page.tsx"], "sourcesContent": ["\"use client\";\n\nimport { useState, useEffect } from \"react\";\nimport { doc, getDoc, setDoc } from \"firebase/firestore\";\nimport { db } from \"@/lib/firebase\";\nimport DataMigration from \"@/components/admin/DataMigration\";\n\ninterface PlatformSettings {\n  languages: {\n    enabled: string[];\n    default: string;\n  };\n  registration: {\n    employerApprovalRequired: boolean;\n    candidateEmailVerificationRequired: boolean;\n  };\n  notifications: {\n    adminEmail: string;\n    newEmployerNotification: boolean;\n    newCandidateNotification: boolean;\n    newJobNotification: boolean;\n  };\n  premium: {\n    enabled: boolean;\n    prices: {\n      jobListing: {\n        sevenDays: number;\n        fourteenDays: number;\n        thirtyDays: number;\n      };\n      cvAccess: {\n        tenInvites: number;\n        twentyFiveInvites: number;\n        fiftyInvites: number;\n      };\n    };\n  };\n}\n\nconst defaultSettings: PlatformSettings = {\n  languages: {\n    enabled: [\"en\", \"de\", \"bs\"],\n    default: \"en\",\n  },\n  registration: {\n    employerApprovalRequired: false,\n    candidateEmailVerificationRequired: true,\n  },\n  notifications: {\n    adminEmail: \"\",\n    newEmployerNotification: true,\n    newCandidateNotification: false,\n    newJobNotification: false,\n  },\n  premium: {\n    enabled: true,\n    prices: {\n      jobListing: {\n        sevenDays: 5,\n        fourteenDays: 8,\n        thirtyDays: 15,\n      },\n      cvAccess: {\n        tenInvites: 5,\n        twentyFiveInvites: 10,\n        fiftyInvites: 20,\n      },\n    },\n  },\n};\n\nexport default function AdminSettingsPage() {\n  const [settings, setSettings] = useState<PlatformSettings>(defaultSettings);\n  const [loading, setLoading] = useState(true);\n  const [saving, setSaving] = useState(false);\n  const [error, setError] = useState<string | null>(null);\n  const [success, setSuccess] = useState<string | null>(null);\n\n  useEffect(() => {\n    const fetchSettings = async () => {\n      try {\n        const settingsDoc = await getDoc(doc(db, \"settings\", \"platform\"));\n        \n        if (settingsDoc.exists()) {\n          setSettings(settingsDoc.data() as PlatformSettings);\n        } else {\n          // If no settings document exists, create one with defaults\n          await setDoc(doc(db, \"settings\", \"platform\"), defaultSettings);\n        }\n      } catch (error) {\n        console.error(\"Error fetching settings:\", error);\n        setError(\"Failed to load settings. Please try again.\");\n      } finally {\n        setLoading(false);\n      }\n    };\n\n    fetchSettings();\n  }, []);\n\n  const handleSaveSettings = async () => {\n    setSaving(true);\n    setError(null);\n    setSuccess(null);\n\n    try {\n      await setDoc(doc(db, \"settings\", \"platform\"), settings);\n      setSuccess(\"Settings saved successfully\");\n      setTimeout(() => setSuccess(null), 3000);\n    } catch (error) {\n      console.error(\"Error saving settings:\", error);\n      setError(\"Failed to save settings. Please try again.\");\n    } finally {\n      setSaving(false);\n    }\n  };\n\n  const handleLanguageToggle = (language: string) => {\n    setSettings(prev => {\n      const enabled = [...prev.languages.enabled];\n      const index = enabled.indexOf(language);\n      \n      if (index === -1) {\n        enabled.push(language);\n      } else {\n        // Don't allow removing the default language\n        if (language === prev.languages.default) {\n          return prev;\n        }\n        enabled.splice(index, 1);\n      }\n      \n      return {\n        ...prev,\n        languages: {\n          ...prev.languages,\n          enabled,\n        },\n      };\n    });\n  };\n\n  const handleDefaultLanguageChange = (language: string) => {\n    setSettings(prev => {\n      // Make sure the default language is also enabled\n      let enabled = [...prev.languages.enabled];\n      if (!enabled.includes(language)) {\n        enabled.push(language);\n      }\n      \n      return {\n        ...prev,\n        languages: {\n          enabled,\n          default: language,\n        },\n      };\n    });\n  };\n\n  if (loading) {\n    return (\n      <div className=\"flex justify-center items-center h-full\">\n        <div className=\"animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900\"></div>\n      </div>\n    );\n  }\n\n  return (\n    <div>\n      <div className=\"flex justify-between items-center mb-6\">\n        <h1 className=\"text-2xl font-bold\">Platform Settings</h1>\n        <button\n          onClick={handleSaveSettings}\n          disabled={saving}\n          className={`px-4 py-2 rounded-md ${\n            saving\n              ? \"bg-neutral-400 cursor-not-allowed\"\n              : \"bg-blue-600 hover:bg-blue-700\"\n          } text-white`}\n        >\n          {saving ? \"Saving...\" : \"Save Settings\"}\n        </button>\n      </div>\n\n      {error && (\n        <div className=\"bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6\">\n          {error}\n        </div>\n      )}\n\n      {success && (\n        <div className=\"bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 p-4 rounded-md mb-6\">\n          {success}\n        </div>\n      )}\n\n      <div className=\"grid grid-cols-1 lg:grid-cols-2 gap-6\">\n        {/* Language Settings */}\n        <div className=\"bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6\">\n          <h2 className=\"text-lg font-medium mb-4\">Language Settings</h2>\n          \n          <div className=\"mb-4\">\n            <label className=\"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2\">\n              Enabled Languages\n            </label>\n            <div className=\"space-y-2\">\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id=\"lang-en\"\n                  checked={settings.languages.enabled.includes(\"en\")}\n                  onChange={() => handleLanguageToggle(\"en\")}\n                  className=\"mr-2\"\n                  disabled={settings.languages.default === \"en\"}\n                />\n                <label htmlFor=\"lang-en\" className=\"text-sm\">English</label>\n              </div>\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id=\"lang-de\"\n                  checked={settings.languages.enabled.includes(\"de\")}\n                  onChange={() => handleLanguageToggle(\"de\")}\n                  className=\"mr-2\"\n                  disabled={settings.languages.default === \"de\"}\n                />\n                <label htmlFor=\"lang-de\" className=\"text-sm\">German</label>\n              </div>\n              <div className=\"flex items-center\">\n                <input\n                  type=\"checkbox\"\n                  id=\"lang-bs\"\n                  checked={settings.languages.enabled.includes(\"bs\")}\n                  onChange={() => handleLanguageToggle(\"bs\")}\n                  className=\"mr-2\"\n                  disabled={settings.languages.default === \"bs\"}\n                />\n                <label htmlFor=\"lang-bs\" className=\"text-sm\">Bosnian</label>\n              </div>\n            </div>\n          </div>\n          \n          <div>\n            <label className=\"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2\">\n              Default Language\n            </label>\n            <select\n              value={settings.languages.default}\n              onChange={(e) => handleDefaultLanguageChange(e.target.value)}\n              className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n            >\n              {settings.languages.enabled.map(lang => (\n                <option key={lang} value={lang}>\n                  {lang === \"en\" ? \"English\" : lang === \"de\" ? \"German\" : \"Bosnian\"}\n                </option>\n              ))}\n            </select>\n          </div>\n        </div>\n\n        {/* Registration Settings */}\n        <div className=\"bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6\">\n          <h2 className=\"text-lg font-medium mb-4\">Registration Settings</h2>\n          \n          <div className=\"space-y-4\">\n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"employer-approval\"\n                checked={settings.registration.employerApprovalRequired}\n                onChange={(e) => setSettings({\n                  ...settings,\n                  registration: {\n                    ...settings.registration,\n                    employerApprovalRequired: e.target.checked,\n                  },\n                })}\n                className=\"mr-2\"\n              />\n              <label htmlFor=\"employer-approval\" className=\"text-sm\">\n                Require manual approval for employer accounts\n              </label>\n            </div>\n            \n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"candidate-verification\"\n                checked={settings.registration.candidateEmailVerificationRequired}\n                onChange={(e) => setSettings({\n                  ...settings,\n                  registration: {\n                    ...settings.registration,\n                    candidateEmailVerificationRequired: e.target.checked,\n                  },\n                })}\n                className=\"mr-2\"\n              />\n              <label htmlFor=\"candidate-verification\" className=\"text-sm\">\n                Require email verification for candidate accounts\n              </label>\n            </div>\n          </div>\n        </div>\n\n        {/* Notification Settings */}\n        <div className=\"bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6\">\n          <h2 className=\"text-lg font-medium mb-4\">Notification Settings</h2>\n          \n          <div className=\"mb-4\">\n            <label htmlFor=\"admin-email\" className=\"block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1\">\n              Admin Email for Notifications\n            </label>\n            <input\n              type=\"email\"\n              id=\"admin-email\"\n              value={settings.notifications.adminEmail}\n              onChange={(e) => setSettings({\n                ...settings,\n                notifications: {\n                  ...settings.notifications,\n                  adminEmail: e.target.value,\n                },\n              })}\n              className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n              placeholder=\"<EMAIL>\"\n            />\n          </div>\n          \n          <div className=\"space-y-3\">\n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"new-employer\"\n                checked={settings.notifications.newEmployerNotification}\n                onChange={(e) => setSettings({\n                  ...settings,\n                  notifications: {\n                    ...settings.notifications,\n                    newEmployerNotification: e.target.checked,\n                  },\n                })}\n                className=\"mr-2\"\n              />\n              <label htmlFor=\"new-employer\" className=\"text-sm\">\n                Notify admin when a new employer registers\n              </label>\n            </div>\n            \n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"new-candidate\"\n                checked={settings.notifications.newCandidateNotification}\n                onChange={(e) => setSettings({\n                  ...settings,\n                  notifications: {\n                    ...settings.notifications,\n                    newCandidateNotification: e.target.checked,\n                  },\n                })}\n                className=\"mr-2\"\n              />\n              <label htmlFor=\"new-candidate\" className=\"text-sm\">\n                Notify admin when a new candidate registers\n              </label>\n            </div>\n            \n            <div className=\"flex items-center\">\n              <input\n                type=\"checkbox\"\n                id=\"new-job\"\n                checked={settings.notifications.newJobNotification}\n                onChange={(e) => setSettings({\n                  ...settings,\n                  notifications: {\n                    ...settings.notifications,\n                    newJobNotification: e.target.checked,\n                  },\n                })}\n                className=\"mr-2\"\n              />\n              <label htmlFor=\"new-job\" className=\"text-sm\">\n                Notify admin when a new job is posted\n              </label>\n            </div>\n          </div>\n        </div>\n\n        {/* Premium Features Settings */}\n        <div className=\"bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6\">\n          <h2 className=\"text-lg font-medium mb-4\">Premium Features</h2>\n          \n          <div className=\"mb-4\">\n            <div className=\"flex items-center mb-4\">\n              <input\n                type=\"checkbox\"\n                id=\"premium-enabled\"\n                checked={settings.premium.enabled}\n                onChange={(e) => setSettings({\n                  ...settings,\n                  premium: {\n                    ...settings.premium,\n                    enabled: e.target.checked,\n                  },\n                })}\n                className=\"mr-2\"\n              />\n              <label htmlFor=\"premium-enabled\" className=\"text-sm font-medium\">\n                Enable premium features\n              </label>\n            </div>\n          </div>\n          \n          {settings.premium.enabled && (\n            <>\n              <h3 className=\"text-md font-medium mb-2\">Job Listing Prices (€)</h3>\n              <div className=\"grid grid-cols-3 gap-4 mb-6\">\n                <div>\n                  <label htmlFor=\"price-7days\" className=\"block text-sm text-neutral-700 dark:text-neutral-300 mb-1\">\n                    7 Days\n                  </label>\n                  <input\n                    type=\"number\"\n                    id=\"price-7days\"\n                    value={settings.premium.prices.jobListing.sevenDays}\n                    onChange={(e) => setSettings({\n                      ...settings,\n                      premium: {\n                        ...settings.premium,\n                        prices: {\n                          ...settings.premium.prices,\n                          jobListing: {\n                            ...settings.premium.prices.jobListing,\n                            sevenDays: Number(e.target.value),\n                          },\n                        },\n                      },\n                    })}\n                    min=\"0\"\n                    step=\"1\"\n                    className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"price-14days\" className=\"block text-sm text-neutral-700 dark:text-neutral-300 mb-1\">\n                    14 Days\n                  </label>\n                  <input\n                    type=\"number\"\n                    id=\"price-14days\"\n                    value={settings.premium.prices.jobListing.fourteenDays}\n                    onChange={(e) => setSettings({\n                      ...settings,\n                      premium: {\n                        ...settings.premium,\n                        prices: {\n                          ...settings.premium.prices,\n                          jobListing: {\n                            ...settings.premium.prices.jobListing,\n                            fourteenDays: Number(e.target.value),\n                          },\n                        },\n                      },\n                    })}\n                    min=\"0\"\n                    step=\"1\"\n                    className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"price-30days\" className=\"block text-sm text-neutral-700 dark:text-neutral-300 mb-1\">\n                    30 Days\n                  </label>\n                  <input\n                    type=\"number\"\n                    id=\"price-30days\"\n                    value={settings.premium.prices.jobListing.thirtyDays}\n                    onChange={(e) => setSettings({\n                      ...settings,\n                      premium: {\n                        ...settings.premium,\n                        prices: {\n                          ...settings.premium.prices,\n                          jobListing: {\n                            ...settings.premium.prices.jobListing,\n                            thirtyDays: Number(e.target.value),\n                          },\n                        },\n                      },\n                    })}\n                    min=\"0\"\n                    step=\"1\"\n                    className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n                  />\n                </div>\n              </div>\n              \n              <h3 className=\"text-md font-medium mb-2\">CV Access Prices (€)</h3>\n              <div className=\"grid grid-cols-3 gap-4\">\n                <div>\n                  <label htmlFor=\"price-10invites\" className=\"block text-sm text-neutral-700 dark:text-neutral-300 mb-1\">\n                    10 Invites\n                  </label>\n                  <input\n                    type=\"number\"\n                    id=\"price-10invites\"\n                    value={settings.premium.prices.cvAccess.tenInvites}\n                    onChange={(e) => setSettings({\n                      ...settings,\n                      premium: {\n                        ...settings.premium,\n                        prices: {\n                          ...settings.premium.prices,\n                          cvAccess: {\n                            ...settings.premium.prices.cvAccess,\n                            tenInvites: Number(e.target.value),\n                          },\n                        },\n                      },\n                    })}\n                    min=\"0\"\n                    step=\"1\"\n                    className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"price-25invites\" className=\"block text-sm text-neutral-700 dark:text-neutral-300 mb-1\">\n                    25 Invites\n                  </label>\n                  <input\n                    type=\"number\"\n                    id=\"price-25invites\"\n                    value={settings.premium.prices.cvAccess.twentyFiveInvites}\n                    onChange={(e) => setSettings({\n                      ...settings,\n                      premium: {\n                        ...settings.premium,\n                        prices: {\n                          ...settings.premium.prices,\n                          cvAccess: {\n                            ...settings.premium.prices.cvAccess,\n                            twentyFiveInvites: Number(e.target.value),\n                          },\n                        },\n                      },\n                    })}\n                    min=\"0\"\n                    step=\"1\"\n                    className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n                  />\n                </div>\n                <div>\n                  <label htmlFor=\"price-50invites\" className=\"block text-sm text-neutral-700 dark:text-neutral-300 mb-1\">\n                    50 Invites\n                  </label>\n                  <input\n                    type=\"number\"\n                    id=\"price-50invites\"\n                    value={settings.premium.prices.cvAccess.fiftyInvites}\n                    onChange={(e) => setSettings({\n                      ...settings,\n                      premium: {\n                        ...settings.premium,\n                        prices: {\n                          ...settings.premium.prices,\n                          cvAccess: {\n                            ...settings.premium.prices.cvAccess,\n                            fiftyInvites: Number(e.target.value),\n                          },\n                        },\n                      },\n                    })}\n                    min=\"0\"\n                    step=\"1\"\n                    className=\"w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md\"\n                  />\n                </div>\n              </div>\n            </>\n          )}\n        </div>\n      </div>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAEA;AACA;AAAA;AACA;;;AAJA;;;;AAuCA,MAAM,kBAAoC;IACxC,WAAW;QACT,SAAS;YAAC;YAAM;YAAM;SAAK;QAC3B,SAAS;IACX;IACA,cAAc;QACZ,0BAA0B;QAC1B,oCAAoC;IACtC;IACA,eAAe;QACb,YAAY;QACZ,yBAAyB;QACzB,0BAA0B;QAC1B,oBAAoB;IACtB;IACA,SAAS;QACP,SAAS;QACT,QAAQ;YACN,YAAY;gBACV,WAAW;gBACX,cAAc;gBACd,YAAY;YACd;YACA,UAAU;gBACR,YAAY;gBACZ,mBAAmB;gBACnB,cAAc;YAChB;QACF;IACF;AACF;AAEe,SAAS;;IACtB,MAAM,CAAC,UAAU,YAAY,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAoB;IAC3D,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACvC,MAAM,CAAC,QAAQ,UAAU,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAE;IACrC,MAAM,CAAC,OAAO,SAAS,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAClD,MAAM,CAAC,SAAS,WAAW,GAAG,CAAA,GAAA,6JAAA,CAAA,WAAQ,AAAD,EAAiB;IAEtD,CAAA,GAAA,6JAAA,CAAA,YAAS,AAAD;uCAAE;YACR,MAAM;6DAAgB;oBACpB,IAAI;wBACF,MAAM,cAAc,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,YAAY;wBAErD,IAAI,YAAY,MAAM,IAAI;4BACxB,YAAY,YAAY,IAAI;wBAC9B,OAAO;4BACL,2DAA2D;4BAC3D,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,YAAY,aAAa;wBAChD;oBACF,EAAE,OAAO,OAAO;wBACd,QAAQ,KAAK,CAAC,4BAA4B;wBAC1C,SAAS;oBACX,SAAU;wBACR,WAAW;oBACb;gBACF;;YAEA;QACF;sCAAG,EAAE;IAEL,MAAM,qBAAqB;QACzB,UAAU;QACV,SAAS;QACT,WAAW;QAEX,IAAI;YACF,MAAM,CAAA,GAAA,sKAAA,CAAA,SAAM,AAAD,EAAE,CAAA,GAAA,sKAAA,CAAA,MAAG,AAAD,EAAE,yHAAA,CAAA,KAAE,EAAE,YAAY,aAAa;YAC9C,WAAW;YACX,WAAW,IAAM,WAAW,OAAO;QACrC,EAAE,OAAO,OAAO;YACd,QAAQ,KAAK,CAAC,0BAA0B;YACxC,SAAS;QACX,SAAU;YACR,UAAU;QACZ;IACF;IAEA,MAAM,uBAAuB,CAAC;QAC5B,YAAY,CAAA;YACV,MAAM,UAAU;mBAAI,KAAK,SAAS,CAAC,OAAO;aAAC;YAC3C,MAAM,QAAQ,QAAQ,OAAO,CAAC;YAE9B,IAAI,UAAU,CAAC,GAAG;gBAChB,QAAQ,IAAI,CAAC;YACf,OAAO;gBACL,4CAA4C;gBAC5C,IAAI,aAAa,KAAK,SAAS,CAAC,OAAO,EAAE;oBACvC,OAAO;gBACT;gBACA,QAAQ,MAAM,CAAC,OAAO;YACxB;YAEA,OAAO;gBACL,GAAG,IAAI;gBACP,WAAW;oBACT,GAAG,KAAK,SAAS;oBACjB;gBACF;YACF;QACF;IACF;IAEA,MAAM,8BAA8B,CAAC;QACnC,YAAY,CAAA;YACV,iDAAiD;YACjD,IAAI,UAAU;mBAAI,KAAK,SAAS,CAAC,OAAO;aAAC;YACzC,IAAI,CAAC,QAAQ,QAAQ,CAAC,WAAW;gBAC/B,QAAQ,IAAI,CAAC;YACf;YAEA,OAAO;gBACL,GAAG,IAAI;gBACP,WAAW;oBACT;oBACA,SAAS;gBACX;YACF;QACF;IACF;IAEA,IAAI,SAAS;QACX,qBACE,6LAAC;YAAI,WAAU;sBACb,cAAA,6LAAC;gBAAI,WAAU;;;;;;;;;;;IAGrB;IAEA,qBACE,6LAAC;;0BACC,6LAAC;gBAAI,WAAU;;kCACb,6LAAC;wBAAG,WAAU;kCAAqB;;;;;;kCACnC,6LAAC;wBACC,SAAS;wBACT,UAAU;wBACV,WAAW,CAAC,qBAAqB,EAC/B,SACI,sCACA,gCACL,WAAW,CAAC;kCAEZ,SAAS,cAAc;;;;;;;;;;;;YAI3B,uBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;YAIJ,yBACC,6LAAC;gBAAI,WAAU;0BACZ;;;;;;0BAIL,6LAAC;gBAAI,WAAU;;kCAEb,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,WAAU;kDAAwE;;;;;;kDAGzF,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,SAAS,SAAS,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC;wDAC7C,UAAU,IAAM,qBAAqB;wDACrC,WAAU;wDACV,UAAU,SAAS,SAAS,CAAC,OAAO,KAAK;;;;;;kEAE3C,6LAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAAU;;;;;;;;;;;;0DAE/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,SAAS,SAAS,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC;wDAC7C,UAAU,IAAM,qBAAqB;wDACrC,WAAU;wDACV,UAAU,SAAS,SAAS,CAAC,OAAO,KAAK;;;;;;kEAE3C,6LAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAAU;;;;;;;;;;;;0DAE/C,6LAAC;gDAAI,WAAU;;kEACb,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,SAAS,SAAS,SAAS,CAAC,OAAO,CAAC,QAAQ,CAAC;wDAC7C,UAAU,IAAM,qBAAqB;wDACrC,WAAU;wDACV,UAAU,SAAS,SAAS,CAAC,OAAO,KAAK;;;;;;kEAE3C,6LAAC;wDAAM,SAAQ;wDAAU,WAAU;kEAAU;;;;;;;;;;;;;;;;;;;;;;;;0CAKnD,6LAAC;;kDACC,6LAAC;wCAAM,WAAU;kDAAwE;;;;;;kDAGzF,6LAAC;wCACC,OAAO,SAAS,SAAS,CAAC,OAAO;wCACjC,UAAU,CAAC,IAAM,4BAA4B,EAAE,MAAM,CAAC,KAAK;wCAC3D,WAAU;kDAET,SAAS,SAAS,CAAC,OAAO,CAAC,GAAG,CAAC,CAAA,qBAC9B,6LAAC;gDAAkB,OAAO;0DACvB,SAAS,OAAO,YAAY,SAAS,OAAO,WAAW;+CAD7C;;;;;;;;;;;;;;;;;;;;;;kCASrB,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,SAAS,SAAS,YAAY,CAAC,wBAAwB;gDACvD,UAAU,CAAC,IAAM,YAAY;wDAC3B,GAAG,QAAQ;wDACX,cAAc;4DACZ,GAAG,SAAS,YAAY;4DACxB,0BAA0B,EAAE,MAAM,CAAC,OAAO;wDAC5C;oDACF;gDACA,WAAU;;;;;;0DAEZ,6LAAC;gDAAM,SAAQ;gDAAoB,WAAU;0DAAU;;;;;;;;;;;;kDAKzD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,SAAS,SAAS,YAAY,CAAC,kCAAkC;gDACjE,UAAU,CAAC,IAAM,YAAY;wDAC3B,GAAG,QAAQ;wDACX,cAAc;4DACZ,GAAG,SAAS,YAAY;4DACxB,oCAAoC,EAAE,MAAM,CAAC,OAAO;wDACtD;oDACF;gDACA,WAAU;;;;;;0DAEZ,6LAAC;gDAAM,SAAQ;gDAAyB,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAQlE,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAM,SAAQ;wCAAc,WAAU;kDAAwE;;;;;;kDAG/G,6LAAC;wCACC,MAAK;wCACL,IAAG;wCACH,OAAO,SAAS,aAAa,CAAC,UAAU;wCACxC,UAAU,CAAC,IAAM,YAAY;gDAC3B,GAAG,QAAQ;gDACX,eAAe;oDACb,GAAG,SAAS,aAAa;oDACzB,YAAY,EAAE,MAAM,CAAC,KAAK;gDAC5B;4CACF;wCACA,WAAU;wCACV,aAAY;;;;;;;;;;;;0CAIhB,6LAAC;gCAAI,WAAU;;kDACb,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,SAAS,SAAS,aAAa,CAAC,uBAAuB;gDACvD,UAAU,CAAC,IAAM,YAAY;wDAC3B,GAAG,QAAQ;wDACX,eAAe;4DACb,GAAG,SAAS,aAAa;4DACzB,yBAAyB,EAAE,MAAM,CAAC,OAAO;wDAC3C;oDACF;gDACA,WAAU;;;;;;0DAEZ,6LAAC;gDAAM,SAAQ;gDAAe,WAAU;0DAAU;;;;;;;;;;;;kDAKpD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,SAAS,SAAS,aAAa,CAAC,wBAAwB;gDACxD,UAAU,CAAC,IAAM,YAAY;wDAC3B,GAAG,QAAQ;wDACX,eAAe;4DACb,GAAG,SAAS,aAAa;4DACzB,0BAA0B,EAAE,MAAM,CAAC,OAAO;wDAC5C;oDACF;gDACA,WAAU;;;;;;0DAEZ,6LAAC;gDAAM,SAAQ;gDAAgB,WAAU;0DAAU;;;;;;;;;;;;kDAKrD,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;gDACC,MAAK;gDACL,IAAG;gDACH,SAAS,SAAS,aAAa,CAAC,kBAAkB;gDAClD,UAAU,CAAC,IAAM,YAAY;wDAC3B,GAAG,QAAQ;wDACX,eAAe;4DACb,GAAG,SAAS,aAAa;4DACzB,oBAAoB,EAAE,MAAM,CAAC,OAAO;wDACtC;oDACF;gDACA,WAAU;;;;;;0DAEZ,6LAAC;gDAAM,SAAQ;gDAAU,WAAU;0DAAU;;;;;;;;;;;;;;;;;;;;;;;;kCAQnD,6LAAC;wBAAI,WAAU;;0CACb,6LAAC;gCAAG,WAAU;0CAA2B;;;;;;0CAEzC,6LAAC;gCAAI,WAAU;0CACb,cAAA,6LAAC;oCAAI,WAAU;;sDACb,6LAAC;4CACC,MAAK;4CACL,IAAG;4CACH,SAAS,SAAS,OAAO,CAAC,OAAO;4CACjC,UAAU,CAAC,IAAM,YAAY;oDAC3B,GAAG,QAAQ;oDACX,SAAS;wDACP,GAAG,SAAS,OAAO;wDACnB,SAAS,EAAE,MAAM,CAAC,OAAO;oDAC3B;gDACF;4CACA,WAAU;;;;;;sDAEZ,6LAAC;4CAAM,SAAQ;4CAAkB,WAAU;sDAAsB;;;;;;;;;;;;;;;;;4BAMpE,SAAS,OAAO,CAAC,OAAO,kBACvB;;kDACE,6LAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAc,WAAU;kEAA4D;;;;;;kEAGnG,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,OAAO,SAAS,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,SAAS;wDACnD,UAAU,CAAC,IAAM,YAAY;gEAC3B,GAAG,QAAQ;gEACX,SAAS;oEACP,GAAG,SAAS,OAAO;oEACnB,QAAQ;wEACN,GAAG,SAAS,OAAO,CAAC,MAAM;wEAC1B,YAAY;4EACV,GAAG,SAAS,OAAO,CAAC,MAAM,CAAC,UAAU;4EACrC,WAAW,OAAO,EAAE,MAAM,CAAC,KAAK;wEAClC;oEACF;gEACF;4DACF;wDACA,KAAI;wDACJ,MAAK;wDACL,WAAU;;;;;;;;;;;;0DAGd,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAe,WAAU;kEAA4D;;;;;;kEAGpG,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,OAAO,SAAS,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,YAAY;wDACtD,UAAU,CAAC,IAAM,YAAY;gEAC3B,GAAG,QAAQ;gEACX,SAAS;oEACP,GAAG,SAAS,OAAO;oEACnB,QAAQ;wEACN,GAAG,SAAS,OAAO,CAAC,MAAM;wEAC1B,YAAY;4EACV,GAAG,SAAS,OAAO,CAAC,MAAM,CAAC,UAAU;4EACrC,cAAc,OAAO,EAAE,MAAM,CAAC,KAAK;wEACrC;oEACF;gEACF;4DACF;wDACA,KAAI;wDACJ,MAAK;wDACL,WAAU;;;;;;;;;;;;0DAGd,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAe,WAAU;kEAA4D;;;;;;kEAGpG,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,OAAO,SAAS,OAAO,CAAC,MAAM,CAAC,UAAU,CAAC,UAAU;wDACpD,UAAU,CAAC,IAAM,YAAY;gEAC3B,GAAG,QAAQ;gEACX,SAAS;oEACP,GAAG,SAAS,OAAO;oEACnB,QAAQ;wEACN,GAAG,SAAS,OAAO,CAAC,MAAM;wEAC1B,YAAY;4EACV,GAAG,SAAS,OAAO,CAAC,MAAM,CAAC,UAAU;4EACrC,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;wEACnC;oEACF;gEACF;4DACF;wDACA,KAAI;wDACJ,MAAK;wDACL,WAAU;;;;;;;;;;;;;;;;;;kDAKhB,6LAAC;wCAAG,WAAU;kDAA2B;;;;;;kDACzC,6LAAC;wCAAI,WAAU;;0DACb,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAkB,WAAU;kEAA4D;;;;;;kEAGvG,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,OAAO,SAAS,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,UAAU;wDAClD,UAAU,CAAC,IAAM,YAAY;gEAC3B,GAAG,QAAQ;gEACX,SAAS;oEACP,GAAG,SAAS,OAAO;oEACnB,QAAQ;wEACN,GAAG,SAAS,OAAO,CAAC,MAAM;wEAC1B,UAAU;4EACR,GAAG,SAAS,OAAO,CAAC,MAAM,CAAC,QAAQ;4EACnC,YAAY,OAAO,EAAE,MAAM,CAAC,KAAK;wEACnC;oEACF;gEACF;4DACF;wDACA,KAAI;wDACJ,MAAK;wDACL,WAAU;;;;;;;;;;;;0DAGd,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAkB,WAAU;kEAA4D;;;;;;kEAGvG,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,OAAO,SAAS,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,iBAAiB;wDACzD,UAAU,CAAC,IAAM,YAAY;gEAC3B,GAAG,QAAQ;gEACX,SAAS;oEACP,GAAG,SAAS,OAAO;oEACnB,QAAQ;wEACN,GAAG,SAAS,OAAO,CAAC,MAAM;wEAC1B,UAAU;4EACR,GAAG,SAAS,OAAO,CAAC,MAAM,CAAC,QAAQ;4EACnC,mBAAmB,OAAO,EAAE,MAAM,CAAC,KAAK;wEAC1C;oEACF;gEACF;4DACF;wDACA,KAAI;wDACJ,MAAK;wDACL,WAAU;;;;;;;;;;;;0DAGd,6LAAC;;kEACC,6LAAC;wDAAM,SAAQ;wDAAkB,WAAU;kEAA4D;;;;;;kEAGvG,6LAAC;wDACC,MAAK;wDACL,IAAG;wDACH,OAAO,SAAS,OAAO,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAY;wDACpD,UAAU,CAAC,IAAM,YAAY;gEAC3B,GAAG,QAAQ;gEACX,SAAS;oEACP,GAAG,SAAS,OAAO;oEACnB,QAAQ;wEACN,GAAG,SAAS,OAAO,CAAC,MAAM;wEAC1B,UAAU;4EACR,GAAG,SAAS,OAAO,CAAC,MAAM,CAAC,QAAQ;4EACnC,cAAc,OAAO,EAAE,MAAM,CAAC,KAAK;wEACrC;oEACF;gEACF;4DACF;wDACA,KAAI;wDACJ,MAAK;wDACL,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAU9B;GAngBwB;KAAA", "debugId": null}}]}