module.exports = {

"[project]/src/data/positions.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "positions": (()=>positions)
});
const positions = [
    {
        id: "electrician",
        translations: {
            de: "Elektriker",
            en: "Electrician",
            bs: "Električar",
            hr: "Električar",
            sr: "Električar",
            me: "Električar",
            mk: "Електричар",
            al: "Elektricist"
        },
        category: "construction",
        skills: [
            "electrical-wiring",
            "electrical-maintenance",
            "electrical-installation"
        ]
    },
    {
        id: "plumber",
        translations: {
            de: "<PERSON><PERSON><PERSON><PERSON>",
            en: "Plumber",
            bs: "Vodoinstalater",
            hr: "Vodoinstalater",
            sr: "Vodoinstalater",
            me: "Vodoinstalater",
            mk: "Водоводџија",
            al: "Hidraulik"
        },
        category: "construction",
        skills: [
            "plumbing",
            "pipe-fitting",
            "water-systems"
        ]
    },
    {
        id: "carpenter",
        translations: {
            de: "Tischler",
            en: "Carpenter",
            bs: "Stolar",
            hr: "Stolar",
            sr: "Stolar",
            me: "Stolar",
            mk: "Столар",
            al: "Marangoz"
        },
        category: "construction",
        skills: [
            "woodworking",
            "furniture-making",
            "carpentry"
        ]
    },
    {
        id: "welder",
        translations: {
            de: "Schweißer",
            en: "Welder",
            bs: "Zavarivač",
            hr: "Zavarivač",
            sr: "Zavarivač",
            me: "Zavarivač",
            mk: "Заварувач",
            al: "Saldator"
        },
        category: "manufacturing",
        skills: [
            "welding",
            "metal-fabrication",
            "blueprint-reading"
        ]
    },
    {
        id: "chef",
        translations: {
            de: "Koch",
            en: "Chef",
            bs: "Kuhar",
            hr: "Kuhar",
            sr: "Kuvar",
            me: "Kuvar",
            mk: "Готвач",
            al: "Kuzhinier"
        },
        category: "hospitality",
        skills: [
            "cooking",
            "food-preparation",
            "menu-planning"
        ]
    },
    {
        id: "waiter",
        translations: {
            de: "Kellner",
            en: "Waiter",
            bs: "Konobar",
            hr: "Konobar",
            sr: "Konobar",
            me: "Konobar",
            mk: "Келнер",
            al: "Kamarier"
        },
        category: "hospitality",
        skills: [
            "customer-service",
            "food-service",
            "order-taking"
        ]
    },
    {
        id: "nurse",
        translations: {
            de: "Krankenpfleger",
            en: "Nurse",
            bs: "Medicinska sestra/tehničar",
            hr: "Medicinska sestra/tehničar",
            sr: "Medicinska sestra/tehničar",
            me: "Medicinska sestra/tehničar",
            mk: "Медицинска сестра/техничар",
            al: "Infermier"
        },
        category: "healthcare",
        skills: [
            "patient-care",
            "medical-assistance",
            "health-monitoring"
        ]
    },
    {
        id: "caregiver",
        translations: {
            de: "Pflegekraft",
            en: "Caregiver",
            bs: "Njegovatelj",
            hr: "Njegovatelj",
            sr: "Negovatelj",
            me: "Njegovatelj",
            mk: "Негувател",
            al: "Kujdestar"
        },
        category: "healthcare",
        skills: [
            "elderly-care",
            "personal-care",
            "medication-management"
        ]
    },
    {
        id: "driver",
        translations: {
            de: "Fahrer",
            en: "Driver",
            bs: "Vozač",
            hr: "Vozač",
            sr: "Vozač",
            me: "Vozač",
            mk: "Возач",
            al: "Shofer"
        },
        category: "transportation",
        skills: [
            "driving",
            "vehicle-maintenance",
            "route-planning"
        ]
    },
    {
        id: "software-developer",
        translations: {
            de: "Softwareentwickler",
            en: "Software Developer",
            bs: "Programer",
            hr: "Programer",
            sr: "Programer",
            me: "Programer",
            mk: "Програмер",
            al: "Zhvillues Softueri"
        },
        category: "it",
        skills: [
            "programming",
            "software-development",
            "problem-solving"
        ]
    },
    {
        id: "accountant",
        translations: {
            de: "Buchhalter",
            en: "Accountant",
            bs: "Računovođa",
            hr: "Računovođa",
            sr: "Računovođa",
            me: "Računovođa",
            mk: "Сметководител",
            al: "Kontabilist"
        },
        category: "finance",
        skills: [
            "accounting",
            "bookkeeping",
            "financial-reporting"
        ]
    },
    {
        id: "teacher",
        translations: {
            de: "Lehrer",
            en: "Teacher",
            bs: "Nastavnik",
            hr: "Nastavnik",
            sr: "Nastavnik",
            me: "Nastavnik",
            mk: "Наставник",
            al: "Mësues"
        },
        category: "education",
        skills: [
            "teaching",
            "curriculum-development",
            "student-assessment"
        ]
    },
    {
        id: "sales-representative",
        translations: {
            de: "Vertriebsmitarbeiter",
            en: "Sales Representative",
            bs: "Prodajni predstavnik",
            hr: "Prodajni predstavnik",
            sr: "Prodajni predstavnik",
            me: "Prodajni predstavnik",
            mk: "Продажен претставник",
            al: "Përfaqësues Shitjesh"
        },
        category: "sales",
        skills: [
            "sales",
            "customer-relations",
            "negotiation"
        ]
    },
    {
        id: "warehouse-worker",
        translations: {
            de: "Lagerarbeiter",
            en: "Warehouse Worker",
            bs: "Skladištar",
            hr: "Skladištar",
            sr: "Magacioner",
            me: "Magacioner",
            mk: "Магационер",
            al: "Punëtor Magazinimi"
        },
        category: "logistics",
        skills: [
            "inventory-management",
            "forklift-operation",
            "order-picking"
        ]
    },
    {
        id: "cleaner",
        translations: {
            de: "Reinigungskraft",
            en: "Cleaner",
            bs: "Čistač",
            hr: "Čistač",
            sr: "Čistač",
            me: "Čistač",
            mk: "Хигиеничар",
            al: "Pastrues"
        },
        category: "facility-services",
        skills: [
            "cleaning",
            "sanitation",
            "housekeeping"
        ]
    }
];
}}),
"[project]/src/data/studyFields.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "studyFields": (()=>studyFields)
});
const studyFields = [
    {
        id: "electrical-engineering",
        translations: {
            de: "Elektrotechnik",
            en: "Electrical Engineering",
            bs: "Elektrotehnika",
            hr: "Elektrotehnika",
            sr: "Elektrotehnika",
            me: "Elektrotehnika",
            mk: "Електротехника",
            al: "Inxhinieri Elektrike"
        },
        category: "engineering"
    },
    {
        id: "mechanical-engineering",
        translations: {
            de: "Maschinenbau",
            en: "Mechanical Engineering",
            bs: "Mašinstvo",
            hr: "Strojarstvo",
            sr: "Mašinstvo",
            me: "Mašinstvo",
            mk: "Машинство",
            al: "Inxhinieri Mekanike"
        },
        category: "engineering"
    },
    {
        id: "automotive-mechanic",
        translations: {
            de: "Kfz-Mechaniker",
            en: "Automotive Mechanic",
            bs: "Automehaničar",
            hr: "Automehaničar",
            sr: "Automehaničar",
            me: "Automehaničar",
            mk: "Автомеханичар",
            al: "Mekanik Automjetesh"
        },
        category: "vocational"
    },
    {
        id: "automotive-painter",
        translations: {
            de: "Kfz-Lackierer",
            en: "Automotive Painter",
            bs: "Autolakirer",
            hr: "Autolakirer",
            sr: "Autolakirer",
            me: "Autolakirer",
            mk: "Автолакер",
            al: "Bojaxhi Automjetesh"
        },
        category: "vocational"
    },
    {
        id: "carpentry",
        translations: {
            de: "Tischlerei",
            en: "Carpentry",
            bs: "Stolarstvo",
            hr: "Stolarstvo",
            sr: "Stolarstvo",
            me: "Stolarstvo",
            mk: "Столарство",
            al: "Marangozi"
        },
        category: "vocational"
    },
    {
        id: "plumbing",
        translations: {
            de: "Sanitärinstallation",
            en: "Plumbing",
            bs: "Vodoinstalaterstvo",
            hr: "Vodoinstalaterstvo",
            sr: "Vodoinstalaterstvo",
            me: "Vodoinstalaterstvo",
            mk: "Водоинсталатерство",
            al: "Hidraulikë"
        },
        category: "vocational"
    },
    {
        id: "welding",
        translations: {
            de: "Schweißen",
            en: "Welding",
            bs: "Zavarivanje",
            hr: "Zavarivanje",
            sr: "Zavarivanje",
            me: "Zavarivanje",
            mk: "Заварување",
            al: "Saldim"
        },
        category: "vocational"
    },
    {
        id: "culinary-arts",
        translations: {
            de: "Kochkunst",
            en: "Culinary Arts",
            bs: "Kuharstvo",
            hr: "Kuharstvo",
            sr: "Kuvarstvo",
            me: "Kuvarstvo",
            mk: "Готварство",
            al: "Arti Kulinar"
        },
        category: "hospitality"
    },
    {
        id: "nursing",
        translations: {
            de: "Krankenpflege",
            en: "Nursing",
            bs: "Sestrinstvo",
            hr: "Sestrinstvo",
            sr: "Sestrinstvo",
            me: "Sestrinstvo",
            mk: "Медицинска нега",
            al: "Infermieri"
        },
        category: "healthcare"
    },
    {
        id: "general-high-school",
        translations: {
            de: "Allgemeine Hochschulreife",
            en: "General High School",
            bs: "Gimnazija",
            hr: "Gimnazija",
            sr: "Gimnazija",
            me: "Gimnazija",
            mk: "Гимназија",
            al: "Gjimnaz"
        },
        category: "general-education"
    }
];
}}),
"[project]/src/data/driverLicenses.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "driverLicenses": (()=>driverLicenses),
    "driverLicensesByCategory": (()=>driverLicensesByCategory),
    "getHighestLicenseCategory": (()=>getHighestLicenseCategory)
});
const driverLicenses = [
    {
        id: "none",
        name: "No License",
        description: "No driver's license",
        translations: {
            en: {
                name: "No License",
                description: "No driver's license"
            },
            de: {
                name: "Kein Führerschein",
                description: "Kein Führerschein vorhanden"
            },
            bs: {
                name: "Bez vozačke",
                description: "Bez vozačke dozvole"
            }
        },
        category: "none"
    },
    {
        id: "am",
        name: "AM",
        description: "Mopeds and light quadricycles",
        translations: {
            en: {
                name: "AM",
                description: "Mopeds and light quadricycles"
            },
            de: {
                name: "AM",
                description: "Mopeds und leichte Vierradfahrzeuge"
            },
            bs: {
                name: "AM",
                description: "Mopedi i laki četverocikli"
            }
        },
        category: "motorcycle"
    },
    {
        id: "a1",
        name: "A1",
        description: "Light motorcycles",
        translations: {
            en: {
                name: "A1",
                description: "Light motorcycles"
            },
            de: {
                name: "A1",
                description: "Leichte Krafträder"
            },
            bs: {
                name: "A1",
                description: "Laki motocikli"
            }
        },
        category: "motorcycle"
    },
    {
        id: "a2",
        name: "A2",
        description: "Medium motorcycles",
        translations: {
            en: {
                name: "A2",
                description: "Medium motorcycles"
            },
            de: {
                name: "A2",
                description: "Mittlere Krafträder"
            },
            bs: {
                name: "A2",
                description: "Srednji motocikli"
            }
        },
        category: "motorcycle"
    },
    {
        id: "a",
        name: "A",
        description: "Motorcycles",
        translations: {
            en: {
                name: "A",
                description: "Motorcycles"
            },
            de: {
                name: "A",
                description: "Krafträder"
            },
            bs: {
                name: "A",
                description: "Motocikli"
            }
        },
        category: "motorcycle"
    },
    {
        id: "b1",
        name: "B1",
        description: "Quadricycles",
        translations: {
            en: {
                name: "B1",
                description: "Quadricycles"
            },
            de: {
                name: "B1",
                description: "Vierradfahrzeuge"
            },
            bs: {
                name: "B1",
                description: "Četverocikli"
            }
        },
        category: "car"
    },
    {
        id: "b",
        name: "B",
        description: "Cars and light vans",
        translations: {
            en: {
                name: "B",
                description: "Cars and light vans"
            },
            de: {
                name: "B",
                description: "Pkw und leichte Nutzfahrzeuge"
            },
            bs: {
                name: "B",
                description: "Automobili i laka dostavna vozila"
            }
        },
        category: "car"
    },
    {
        id: "be",
        name: "BE",
        description: "Cars with trailer",
        translations: {
            en: {
                name: "BE",
                description: "Cars with trailer"
            },
            de: {
                name: "BE",
                description: "Pkw mit Anhänger"
            },
            bs: {
                name: "BE",
                description: "Automobili s prikolicom"
            }
        },
        category: "car"
    },
    {
        id: "c1",
        name: "C1",
        description: "Medium-sized vehicles",
        translations: {
            en: {
                name: "C1",
                description: "Medium-sized vehicles"
            },
            de: {
                name: "C1",
                description: "Mittelgroße Fahrzeuge"
            },
            bs: {
                name: "C1",
                description: "Vozila srednje veličine"
            }
        },
        category: "truck"
    },
    {
        id: "c1e",
        name: "C1E",
        description: "Medium-sized vehicles with trailer",
        translations: {
            en: {
                name: "C1E",
                description: "Medium-sized vehicles with trailer"
            },
            de: {
                name: "C1E",
                description: "Mittelgroße Fahrzeuge mit Anhänger"
            },
            bs: {
                name: "C1E",
                description: "Vozila srednje veličine s prikolicom"
            }
        },
        category: "truck"
    },
    {
        id: "c",
        name: "C",
        description: "Large goods vehicles",
        translations: {
            en: {
                name: "C",
                description: "Large goods vehicles"
            },
            de: {
                name: "C",
                description: "Schwere Nutzfahrzeuge"
            },
            bs: {
                name: "C",
                description: "Teretna vozila"
            }
        },
        category: "truck"
    },
    {
        id: "ce",
        name: "CE",
        description: "Large goods vehicles with trailer",
        translations: {
            en: {
                name: "CE",
                description: "Large goods vehicles with trailer"
            },
            de: {
                name: "CE",
                description: "Schwere Nutzfahrzeuge mit Anhänger"
            },
            bs: {
                name: "CE",
                description: "Teretna vozila s prikolicom"
            }
        },
        category: "truck"
    },
    {
        id: "d1",
        name: "D1",
        description: "Minibuses",
        translations: {
            en: {
                name: "D1",
                description: "Minibuses"
            },
            de: {
                name: "D1",
                description: "Kleinbusse"
            },
            bs: {
                name: "D1",
                description: "Minibusevi"
            }
        },
        category: "bus"
    },
    {
        id: "d1e",
        name: "D1E",
        description: "Minibuses with trailer",
        translations: {
            en: {
                name: "D1E",
                description: "Minibuses with trailer"
            },
            de: {
                name: "D1E",
                description: "Kleinbusse mit Anhänger"
            },
            bs: {
                name: "D1E",
                description: "Minibusevi s prikolicom"
            }
        },
        category: "bus"
    },
    {
        id: "d",
        name: "D",
        description: "Buses",
        translations: {
            en: {
                name: "D",
                description: "Buses"
            },
            de: {
                name: "D",
                description: "Busse"
            },
            bs: {
                name: "D",
                description: "Autobusi"
            }
        },
        category: "bus"
    },
    {
        id: "de",
        name: "DE",
        description: "Buses with trailer",
        translations: {
            en: {
                name: "DE",
                description: "Buses with trailer"
            },
            de: {
                name: "DE",
                description: "Busse mit Anhänger"
            },
            bs: {
                name: "DE",
                description: "Autobusi s prikolicom"
            }
        },
        category: "bus"
    }
];
const driverLicensesByCategory = {
    none: driverLicenses.filter((license)=>license.category === "none"),
    car: driverLicenses.filter((license)=>license.category === "car"),
    motorcycle: driverLicenses.filter((license)=>license.category === "motorcycle"),
    truck: driverLicenses.filter((license)=>license.category === "truck"),
    bus: driverLicenses.filter((license)=>license.category === "bus"),
    special: driverLicenses.filter((license)=>license.category === "special")
};
function getHighestLicenseCategory(licenses) {
    if (!licenses || licenses.length === 0) return "none";
    // Priority order (highest to lowest)
    const priorityOrder = [
        "de",
        "d",
        "d1e",
        "d1",
        "ce",
        "c",
        "c1e",
        "c1",
        "be",
        "b",
        "b1",
        "a",
        "a2",
        "a1",
        "am",
        "none"
    ];
    // Find the license with the highest priority
    let highestPriority = Number.MAX_SAFE_INTEGER;
    let highestLicense = "none";
    for (const license of licenses){
        const priority = priorityOrder.indexOf(license.toLowerCase());
        if (priority !== -1 && priority < highestPriority) {
            highestPriority = priority;
            highestLicense = license.toLowerCase();
        }
    }
    return highestLicense;
}
}}),
"[project]/src/data/jobBenefits.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
// Job benefits/features that employers can select when creating a job
// and candidates can select as preferences
__turbopack_context__.s({
    "MIN_BENEFITS_REQUIRED": (()=>MIN_BENEFITS_REQUIRED),
    "jobBenefits": (()=>jobBenefits)
});
const jobBenefits = [
    {
        id: "flexible_hours",
        label: "Flexible Working Hours",
        description: "Flexible start and end times or the ability to work remotely"
    },
    {
        id: "remote_work",
        label: "Remote Work Options",
        description: "Possibility to work remotely part-time or full-time"
    },
    {
        id: "health_insurance",
        label: "Health Insurance",
        description: "Comprehensive health insurance coverage"
    },
    {
        id: "paid_vacation",
        label: "Paid Vacation",
        description: "Generous paid time off and vacation days"
    },
    {
        id: "professional_development",
        label: "Professional Development",
        description: "Training, courses, and career advancement opportunities"
    },
    {
        id: "retirement_plan",
        label: "Retirement Plan",
        description: "Company pension or retirement savings plan"
    },
    {
        id: "relocation_assistance",
        label: "Relocation Assistance",
        description: "Help with moving expenses and finding accommodation"
    },
    {
        id: "accommodation_provided_free",
        label: "Free Accommodation Provided",
        description: "Accommodation is provided and fully paid by the employer"
    },
    {
        id: "accommodation_provided_paid",
        label: "Accommodation Provided (Paid)",
        description: "Accommodation is arranged by the employer but paid by the employee"
    },
    {
        id: "accommodation_not_provided",
        label: "Accommodation Not Provided",
        description: "Employee must arrange their own accommodation"
    },
    {
        id: "accommodation_assistance",
        label: "Accommodation Finding Assistance",
        description: "Employer assists in finding accommodation but does not provide it directly"
    },
    {
        id: "language_courses",
        label: "Language Courses",
        description: "Free or subsidized language training"
    },
    {
        id: "childcare",
        label: "Childcare Support",
        description: "On-site childcare or childcare subsidies"
    },
    {
        id: "gym_membership",
        label: "Gym Membership",
        description: "Free or discounted gym or wellness program"
    },
    {
        id: "company_car",
        label: "Company Car",
        description: "Company vehicle or car allowance"
    },
    {
        id: "meal_allowance",
        label: "Meal Allowance",
        description: "Free or subsidized meals or meal vouchers"
    },
    {
        id: "performance_bonus",
        label: "Performance Bonuses",
        description: "Financial bonuses based on performance"
    },
    {
        id: "housing_allowance",
        label: "Housing Allowance",
        description: "Assistance with housing costs"
    },
    {
        id: "public_transport",
        label: "Public Transport Subsidy",
        description: "Free or subsidized public transportation"
    }
];
const MIN_BENEFITS_REQUIRED = 3;
}}),
"[project]/src/data/countries.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "countries": (()=>countries)
});
const countries = [
    {
        id: "de",
        name: "Germany",
        code: "DE",
        flagUrl: "/flags/de.svg",
        region: "eu"
    },
    {
        id: "at",
        name: "Austria",
        code: "AT",
        flagUrl: "/flags/at.svg",
        region: "eu"
    },
    {
        id: "hr",
        name: "Croatia",
        code: "HR",
        flagUrl: "/flags/hr.svg",
        region: "eu"
    },
    {
        id: "si",
        name: "Slovenia",
        code: "SI",
        flagUrl: "/flags/si.svg",
        region: "eu"
    },
    {
        id: "ba",
        name: "Bosnia and Herzegovina",
        code: "BA",
        flagUrl: "/flags/ba.svg",
        region: "balkans"
    },
    {
        id: "rs",
        name: "Serbia",
        code: "RS",
        flagUrl: "/flags/rs.svg",
        region: "balkans"
    },
    {
        id: "me",
        name: "Montenegro",
        code: "ME",
        flagUrl: "/flags/me.svg",
        region: "balkans"
    },
    {
        id: "mk",
        name: "North Macedonia",
        code: "MK",
        flagUrl: "/flags/mk.svg",
        region: "balkans"
    },
    {
        id: "al",
        name: "Albania",
        code: "AL",
        flagUrl: "/flags/al.svg",
        region: "balkans"
    },
    {
        id: "bg",
        name: "Bulgaria",
        code: "BG",
        flagUrl: "/flags/bg.svg",
        region: "balkans"
    },
    {
        id: "ro",
        name: "Romania",
        code: "RO",
        flagUrl: "/flags/ro.svg",
        region: "balkans"
    },
    {
        id: "gr",
        name: "Greece",
        code: "GR",
        flagUrl: "/flags/gr.svg",
        region: "balkans"
    }
];
}}),
"[project]/src/utils/migrateData.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "checkMigrationStatus": (()=>checkMigrationStatus),
    "migrateStaticDataToFirestore": (()=>migrateStaticDataToFirestore)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$firebase$2f$firestore$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/firebase/firestore/dist/index.mjs [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/firestore/dist/index.node.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/firebase.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$positions$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/positions.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$studyFields$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/studyFields.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$driverLicenses$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/driverLicenses.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$jobBenefits$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/jobBenefits.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$countries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/countries.ts [app-ssr] (ecmascript)");
;
;
;
;
;
;
;
async function migrateStaticDataToFirestore() {
    try {
        console.log("Starting data migration to Firestore...");
        // Migrate positions
        await migrateCollection("positions", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$positions$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["positions"]);
        console.log("Positions migrated successfully");
        // Migrate study fields
        await migrateCollection("studyFields", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$studyFields$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["studyFields"]);
        console.log("Study fields migrated successfully");
        // Migrate driver licenses
        await migrateCollection("driverLicenses", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$driverLicenses$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["driverLicenses"]);
        console.log("Driver licenses migrated successfully");
        // Migrate job benefits
        await migrateCollection("benefits", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$jobBenefits$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jobBenefits"]);
        console.log("Job benefits migrated successfully");
        // Migrate countries
        await migrateCollection("countries", __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$countries$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["countries"]);
        console.log("Countries migrated successfully");
        return {
            success: true,
            message: "All data migrated successfully to Firestore"
        };
    } catch (error) {
        console.error("Error during migration:", error);
        return {
            success: false,
            message: "Migration failed",
            details: error
        };
    }
}
async function migrateCollection(collectionName, data) {
    // Check if collection already has data
    const snapshot = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDocs"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], collectionName));
    if (!snapshot.empty) {
        console.log(`Collection ${collectionName} already has data, skipping...`);
        return;
    }
    // Migrate data
    for (const item of data){
        const docRef = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], collectionName, item.id);
        await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setDoc"])(docRef, {
            ...item,
            createdAt: new Date(),
            updatedAt: new Date()
        });
    }
}
async function checkMigrationStatus() {
    const collections = [
        "positions",
        "studyFields",
        "driverLicenses",
        "benefits",
        "countries"
    ];
    const status = {};
    for (const collectionName of collections){
        const snapshot = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDocs"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], collectionName));
        status[collectionName] = snapshot.size;
    }
    return status;
}
}}),
"[project]/src/components/admin/DataMigration.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>DataMigration)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$migrateData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/migrateData.ts [app-ssr] (ecmascript)");
;
;
;
function DataMigration() {
    const [migrating, setMigrating] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [migrationResult, setMigrationResult] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [migrationStatus, setMigrationStatus] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        checkStatus();
    }, []);
    const checkStatus = async ()=>{
        try {
            setLoading(true);
            const status = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$migrateData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["checkMigrationStatus"])();
            setMigrationStatus(status);
        } catch (error) {
            console.error("Error checking migration status:", error);
        } finally{
            setLoading(false);
        }
    };
    const handleMigration = async ()=>{
        setMigrating(true);
        setMigrationResult(null);
        try {
            const result = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$migrateData$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["migrateStaticDataToFirestore"])();
            setMigrationResult(result);
            if (result.success) {
                // Refresh status after successful migration
                await checkStatus();
            }
        } catch (error) {
            setMigrationResult({
                success: false,
                message: "Migration failed with error",
                details: error
            });
        } finally{
            setMigrating(false);
        }
    };
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-pulse",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-4 bg-neutral-200 dark:bg-neutral-700 rounded w-1/4 mb-4"
                    }, void 0, false, {
                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                        lineNumber: 53,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-4 bg-neutral-200 dark:bg-neutral-700 rounded w-3/4 mb-2"
                    }, void 0, false, {
                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                        lineNumber: 54,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "h-4 bg-neutral-200 dark:bg-neutral-700 rounded w-1/2"
                    }, void 0, false, {
                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                        lineNumber: 55,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/admin/DataMigration.tsx",
                lineNumber: 52,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/components/admin/DataMigration.tsx",
            lineNumber: 51,
            columnNumber: 7
        }, this);
    }
    const totalItems = migrationStatus ? Object.values(migrationStatus).reduce((a, b)=>a + b, 0) : 0;
    const hasData = totalItems > 0;
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                className: "text-lg font-medium mb-4",
                children: "Data Migration"
            }, void 0, false, {
                fileName: "[project]/src/components/admin/DataMigration.tsx",
                lineNumber: 66,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                        className: "text-md font-medium mb-2",
                        children: "Current Status"
                    }, void 0, false, {
                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                        lineNumber: 69,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-2 md:grid-cols-3 gap-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-neutral-50 dark:bg-neutral-900 p-3 rounded",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-sm text-neutral-600 dark:text-neutral-400",
                                        children: "Positions"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                                        lineNumber: 72,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-lg font-semibold",
                                        children: migrationStatus?.positions || 0
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                                        lineNumber: 73,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/admin/DataMigration.tsx",
                                lineNumber: 71,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-neutral-50 dark:bg-neutral-900 p-3 rounded",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-sm text-neutral-600 dark:text-neutral-400",
                                        children: "Study Fields"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                                        lineNumber: 76,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-lg font-semibold",
                                        children: migrationStatus?.studyFields || 0
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                                        lineNumber: 77,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/admin/DataMigration.tsx",
                                lineNumber: 75,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-neutral-50 dark:bg-neutral-900 p-3 rounded",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-sm text-neutral-600 dark:text-neutral-400",
                                        children: "Driver Licenses"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                                        lineNumber: 80,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-lg font-semibold",
                                        children: migrationStatus?.driverLicenses || 0
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                                        lineNumber: 81,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/admin/DataMigration.tsx",
                                lineNumber: 79,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-neutral-50 dark:bg-neutral-900 p-3 rounded",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-sm text-neutral-600 dark:text-neutral-400",
                                        children: "Benefits"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                                        lineNumber: 84,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-lg font-semibold",
                                        children: migrationStatus?.benefits || 0
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                                        lineNumber: 85,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/admin/DataMigration.tsx",
                                lineNumber: 83,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-neutral-50 dark:bg-neutral-900 p-3 rounded",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-sm text-neutral-600 dark:text-neutral-400",
                                        children: "Countries"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                                        lineNumber: 88,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-lg font-semibold",
                                        children: migrationStatus?.countries || 0
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                                        lineNumber: 89,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/admin/DataMigration.tsx",
                                lineNumber: 87,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                        lineNumber: 70,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/admin/DataMigration.tsx",
                lineNumber: 68,
                columnNumber: 7
            }, this),
            !hasData && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-md p-4",
                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex-shrink-0",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    className: "h-5 w-5 text-yellow-400",
                                    viewBox: "0 0 20 20",
                                    fill: "currentColor",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        fillRule: "evenodd",
                                        d: "M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z",
                                        clipRule: "evenodd"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                                        lineNumber: 100,
                                        columnNumber: 19
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/admin/DataMigration.tsx",
                                    lineNumber: 99,
                                    columnNumber: 17
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/admin/DataMigration.tsx",
                                lineNumber: 98,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "ml-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-sm font-medium text-yellow-800 dark:text-yellow-200",
                                        children: "No data found in Firestore"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                                        lineNumber: 104,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-2 text-sm text-yellow-700 dark:text-yellow-300",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                            children: "Your application is currently using static data files. To enable full admin panel functionality, you need to migrate this data to Firestore."
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/admin/DataMigration.tsx",
                                            lineNumber: 108,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                                        lineNumber: 107,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/admin/DataMigration.tsx",
                                lineNumber: 103,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                        lineNumber: 97,
                        columnNumber: 13
                    }, this)
                }, void 0, false, {
                    fileName: "[project]/src/components/admin/DataMigration.tsx",
                    lineNumber: 96,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/admin/DataMigration.tsx",
                lineNumber: 95,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex items-center justify-between",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: handleMigration,
                            disabled: migrating,
                            className: `px-4 py-2 rounded-md ${migrating ? "bg-neutral-300 dark:bg-neutral-700 cursor-not-allowed" : "bg-blue-600 hover:bg-blue-700 text-white"}`,
                            children: migrating ? "Migrating..." : hasData ? "Re-migrate Data" : "Migrate Data to Firestore"
                        }, void 0, false, {
                            fileName: "[project]/src/components/admin/DataMigration.tsx",
                            lineNumber: 121,
                            columnNumber: 11
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                            onClick: checkStatus,
                            disabled: loading,
                            className: "ml-3 px-4 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md hover:bg-neutral-50 dark:hover:bg-neutral-700",
                            children: "Refresh Status"
                        }, void 0, false, {
                            fileName: "[project]/src/components/admin/DataMigration.tsx",
                            lineNumber: 133,
                            columnNumber: 11
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/components/admin/DataMigration.tsx",
                    lineNumber: 120,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/components/admin/DataMigration.tsx",
                lineNumber: 119,
                columnNumber: 7
            }, this),
            migrationResult && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: `mt-4 p-4 rounded-md ${migrationResult.success ? "bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-300" : "bg-red-50 dark:bg-red-900/20 text-red-700 dark:text-red-300"}`,
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "font-medium",
                        children: migrationResult.message
                    }, void 0, false, {
                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                        lineNumber: 149,
                        columnNumber: 11
                    }, this),
                    migrationResult.details && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("pre", {
                        className: "mt-2 text-xs overflow-auto",
                        children: JSON.stringify(migrationResult.details, null, 2)
                    }, void 0, false, {
                        fileName: "[project]/src/components/admin/DataMigration.tsx",
                        lineNumber: 151,
                        columnNumber: 13
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/admin/DataMigration.tsx",
                lineNumber: 144,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/admin/DataMigration.tsx",
        lineNumber: 65,
        columnNumber: 5
    }, this);
}
}}),
"[project]/src/app/admin/settings/page.tsx [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>AdminSettingsPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/server/route-modules/app-page/vendored/ssr/react.js [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$firebase$2f$firestore$2f$dist$2f$index$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/firebase/firestore/dist/index.mjs [app-ssr] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/firestore/dist/index.node.mjs [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/firebase.ts [app-ssr] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$admin$2f$DataMigration$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/admin/DataMigration.tsx [app-ssr] (ecmascript)");
"use client";
;
;
;
;
;
const defaultSettings = {
    languages: {
        enabled: [
            "en",
            "de",
            "bs"
        ],
        default: "en"
    },
    registration: {
        employerApprovalRequired: false,
        candidateEmailVerificationRequired: true
    },
    notifications: {
        adminEmail: "",
        newEmployerNotification: true,
        newCandidateNotification: false,
        newJobNotification: false
    },
    premium: {
        enabled: true,
        prices: {
            jobListing: {
                sevenDays: 5,
                fourteenDays: 8,
                thirtyDays: 15
            },
            cvAccess: {
                tenInvites: 5,
                twentyFiveInvites: 10,
                fiftyInvites: 20
            }
        }
    }
};
function AdminSettingsPage() {
    const [settings, setSettings] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(defaultSettings);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(true);
    const [saving, setSaving] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(false);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    const [success, setSuccess] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useState"])(null);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["useEffect"])(()=>{
        const fetchSettings = async ()=>{
            try {
                const settingsDoc = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["getDoc"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], "settings", "platform"));
                if (settingsDoc.exists()) {
                    setSettings(settingsDoc.data());
                } else {
                    // If no settings document exists, create one with defaults
                    await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setDoc"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], "settings", "platform"), defaultSettings);
                }
            } catch (error) {
                console.error("Error fetching settings:", error);
                setError("Failed to load settings. Please try again.");
            } finally{
                setLoading(false);
            }
        };
        fetchSettings();
    }, []);
    const handleSaveSettings = async ()=>{
        setSaving(true);
        setError(null);
        setSuccess(null);
        try {
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["setDoc"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$node$2e$mjs__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["db"], "settings", "platform"), settings);
            setSuccess("Settings saved successfully");
            setTimeout(()=>setSuccess(null), 3000);
        } catch (error) {
            console.error("Error saving settings:", error);
            setError("Failed to save settings. Please try again.");
        } finally{
            setSaving(false);
        }
    };
    const handleLanguageToggle = (language)=>{
        setSettings((prev)=>{
            const enabled = [
                ...prev.languages.enabled
            ];
            const index = enabled.indexOf(language);
            if (index === -1) {
                enabled.push(language);
            } else {
                // Don't allow removing the default language
                if (language === prev.languages.default) {
                    return prev;
                }
                enabled.splice(index, 1);
            }
            return {
                ...prev,
                languages: {
                    ...prev.languages,
                    enabled
                }
            };
        });
    };
    const handleDefaultLanguageChange = (language)=>{
        setSettings((prev)=>{
            // Make sure the default language is also enabled
            let enabled = [
                ...prev.languages.enabled
            ];
            if (!enabled.includes(language)) {
                enabled.push(language);
            }
            return {
                ...prev,
                languages: {
                    enabled,
                    default: language
                }
            };
        });
    };
    if (loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex justify-center items-center h-full",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"
            }, void 0, false, {
                fileName: "[project]/src/app/admin/settings/page.tsx",
                lineNumber: 164,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/admin/settings/page.tsx",
            lineNumber: 163,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-between items-center mb-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-2xl font-bold",
                        children: "Platform Settings"
                    }, void 0, false, {
                        fileName: "[project]/src/app/admin/settings/page.tsx",
                        lineNumber: 172,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                        onClick: handleSaveSettings,
                        disabled: saving,
                        className: `px-4 py-2 rounded-md ${saving ? "bg-neutral-400 cursor-not-allowed" : "bg-blue-600 hover:bg-blue-700"} text-white`,
                        children: saving ? "Saving..." : "Save Settings"
                    }, void 0, false, {
                        fileName: "[project]/src/app/admin/settings/page.tsx",
                        lineNumber: 173,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/admin/settings/page.tsx",
                lineNumber: 171,
                columnNumber: 7
            }, this),
            error && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6",
                children: error
            }, void 0, false, {
                fileName: "[project]/src/app/admin/settings/page.tsx",
                lineNumber: 187,
                columnNumber: 9
            }, this),
            success && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 p-4 rounded-md mb-6",
                children: success
            }, void 0, false, {
                fileName: "[project]/src/app/admin/settings/page.tsx",
                lineNumber: 193,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mb-6",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$admin$2f$DataMigration$2e$tsx__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["default"], {}, void 0, false, {
                    fileName: "[project]/src/app/admin/settings/page.tsx",
                    lineNumber: 200,
                    columnNumber: 9
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/admin/settings/page.tsx",
                lineNumber: 199,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-1 lg:grid-cols-2 gap-6",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-lg font-medium mb-4",
                                children: "Language Settings"
                            }, void 0, false, {
                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                lineNumber: 206,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mb-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2",
                                        children: "Enabled Languages"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                        lineNumber: 209,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "space-y-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                        type: "checkbox",
                                                        id: "lang-en",
                                                        checked: settings.languages.enabled.includes("en"),
                                                        onChange: ()=>handleLanguageToggle("en"),
                                                        className: "mr-2",
                                                        disabled: settings.languages.default === "en"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                                        lineNumber: 214,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                        htmlFor: "lang-en",
                                                        className: "text-sm",
                                                        children: "English"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                                        lineNumber: 222,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                                lineNumber: 213,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                        type: "checkbox",
                                                        id: "lang-de",
                                                        checked: settings.languages.enabled.includes("de"),
                                                        onChange: ()=>handleLanguageToggle("de"),
                                                        className: "mr-2",
                                                        disabled: settings.languages.default === "de"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                                        lineNumber: 225,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                        htmlFor: "lang-de",
                                                        className: "text-sm",
                                                        children: "German"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                                        lineNumber: 233,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                                lineNumber: 224,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                        type: "checkbox",
                                                        id: "lang-bs",
                                                        checked: settings.languages.enabled.includes("bs"),
                                                        onChange: ()=>handleLanguageToggle("bs"),
                                                        className: "mr-2",
                                                        disabled: settings.languages.default === "bs"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                                        lineNumber: 236,
                                                        columnNumber: 17
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                        htmlFor: "lang-bs",
                                                        className: "text-sm",
                                                        children: "Bosnian"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                                        lineNumber: 244,
                                                        columnNumber: 17
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                                lineNumber: 235,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                        lineNumber: 212,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                lineNumber: 208,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        className: "block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-2",
                                        children: "Default Language"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                        lineNumber: 250,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                        value: settings.languages.default,
                                        onChange: (e)=>handleDefaultLanguageChange(e.target.value),
                                        className: "w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md",
                                        children: settings.languages.enabled.map((lang)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                                value: lang,
                                                children: lang === "en" ? "English" : lang === "de" ? "German" : "Bosnian"
                                            }, lang, false, {
                                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                                lineNumber: 259,
                                                columnNumber: 17
                                            }, this))
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                        lineNumber: 253,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                lineNumber: 249,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/admin/settings/page.tsx",
                        lineNumber: 205,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-lg font-medium mb-4",
                                children: "Registration Settings"
                            }, void 0, false, {
                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                lineNumber: 269,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                type: "checkbox",
                                                id: "employer-approval",
                                                checked: settings.registration.employerApprovalRequired,
                                                onChange: (e)=>setSettings({
                                                        ...settings,
                                                        registration: {
                                                            ...settings.registration,
                                                            employerApprovalRequired: e.target.checked
                                                        }
                                                    }),
                                                className: "mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                                lineNumber: 273,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                htmlFor: "employer-approval",
                                                className: "text-sm",
                                                children: "Require manual approval for employer accounts"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                                lineNumber: 286,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                        lineNumber: 272,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                type: "checkbox",
                                                id: "candidate-verification",
                                                checked: settings.registration.candidateEmailVerificationRequired,
                                                onChange: (e)=>setSettings({
                                                        ...settings,
                                                        registration: {
                                                            ...settings.registration,
                                                            candidateEmailVerificationRequired: e.target.checked
                                                        }
                                                    }),
                                                className: "mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                                lineNumber: 292,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                htmlFor: "candidate-verification",
                                                className: "text-sm",
                                                children: "Require email verification for candidate accounts"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                                lineNumber: 305,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                        lineNumber: 291,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                lineNumber: 271,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/admin/settings/page.tsx",
                        lineNumber: 268,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-lg font-medium mb-4",
                                children: "Notification Settings"
                            }, void 0, false, {
                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                lineNumber: 314,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mb-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                        htmlFor: "admin-email",
                                        className: "block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1",
                                        children: "Admin Email for Notifications"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                        lineNumber: 317,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                        type: "email",
                                        id: "admin-email",
                                        value: settings.notifications.adminEmail,
                                        onChange: (e)=>setSettings({
                                                ...settings,
                                                notifications: {
                                                    ...settings.notifications,
                                                    adminEmail: e.target.value
                                                }
                                            }),
                                        className: "w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md",
                                        placeholder: "<EMAIL>"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                        lineNumber: 320,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                lineNumber: 316,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "space-y-3",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                type: "checkbox",
                                                id: "new-employer",
                                                checked: settings.notifications.newEmployerNotification,
                                                onChange: (e)=>setSettings({
                                                        ...settings,
                                                        notifications: {
                                                            ...settings.notifications,
                                                            newEmployerNotification: e.target.checked
                                                        }
                                                    }),
                                                className: "mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                                lineNumber: 338,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                htmlFor: "new-employer",
                                                className: "text-sm",
                                                children: "Notify admin when a new employer registers"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                                lineNumber: 351,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                        lineNumber: 337,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                type: "checkbox",
                                                id: "new-candidate",
                                                checked: settings.notifications.newCandidateNotification,
                                                onChange: (e)=>setSettings({
                                                        ...settings,
                                                        notifications: {
                                                            ...settings.notifications,
                                                            newCandidateNotification: e.target.checked
                                                        }
                                                    }),
                                                className: "mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                                lineNumber: 357,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                htmlFor: "new-candidate",
                                                className: "text-sm",
                                                children: "Notify admin when a new candidate registers"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                                lineNumber: 370,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                        lineNumber: 356,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                type: "checkbox",
                                                id: "new-job",
                                                checked: settings.notifications.newJobNotification,
                                                onChange: (e)=>setSettings({
                                                        ...settings,
                                                        notifications: {
                                                            ...settings.notifications,
                                                            newJobNotification: e.target.checked
                                                        }
                                                    }),
                                                className: "mr-2"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                                lineNumber: 376,
                                                columnNumber: 15
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                htmlFor: "new-job",
                                                className: "text-sm",
                                                children: "Notify admin when a new job is posted"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                                lineNumber: 389,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                        lineNumber: 375,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                lineNumber: 336,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/admin/settings/page.tsx",
                        lineNumber: 313,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                className: "text-lg font-medium mb-4",
                                children: "Premium Features"
                            }, void 0, false, {
                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                lineNumber: 398,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mb-4",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "flex items-center mb-4",
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                            type: "checkbox",
                                            id: "premium-enabled",
                                            checked: settings.premium.enabled,
                                            onChange: (e)=>setSettings({
                                                    ...settings,
                                                    premium: {
                                                        ...settings.premium,
                                                        enabled: e.target.checked
                                                    }
                                                }),
                                            className: "mr-2"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/admin/settings/page.tsx",
                                            lineNumber: 402,
                                            columnNumber: 15
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                            htmlFor: "premium-enabled",
                                            className: "text-sm font-medium",
                                            children: "Enable premium features"
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/admin/settings/page.tsx",
                                            lineNumber: 415,
                                            columnNumber: 15
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/app/admin/settings/page.tsx",
                                    lineNumber: 401,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                lineNumber: 400,
                                columnNumber: 11
                            }, this),
                            settings.premium.enabled && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["Fragment"], {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-md font-medium mb-2",
                                        children: "Job Listing Prices (€)"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                        lineNumber: 423,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-3 gap-4 mb-6",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                        htmlFor: "price-7days",
                                                        className: "block text-sm text-neutral-700 dark:text-neutral-300 mb-1",
                                                        children: "7 Days"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                                        lineNumber: 426,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                        type: "number",
                                                        id: "price-7days",
                                                        value: settings.premium.prices.jobListing.sevenDays,
                                                        onChange: (e)=>setSettings({
                                                                ...settings,
                                                                premium: {
                                                                    ...settings.premium,
                                                                    prices: {
                                                                        ...settings.premium.prices,
                                                                        jobListing: {
                                                                            ...settings.premium.prices.jobListing,
                                                                            sevenDays: Number(e.target.value)
                                                                        }
                                                                    }
                                                                }
                                                            }),
                                                        min: "0",
                                                        step: "1",
                                                        className: "w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                                        lineNumber: 429,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                                lineNumber: 425,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                        htmlFor: "price-14days",
                                                        className: "block text-sm text-neutral-700 dark:text-neutral-300 mb-1",
                                                        children: "14 Days"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                                        lineNumber: 452,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                        type: "number",
                                                        id: "price-14days",
                                                        value: settings.premium.prices.jobListing.fourteenDays,
                                                        onChange: (e)=>setSettings({
                                                                ...settings,
                                                                premium: {
                                                                    ...settings.premium,
                                                                    prices: {
                                                                        ...settings.premium.prices,
                                                                        jobListing: {
                                                                            ...settings.premium.prices.jobListing,
                                                                            fourteenDays: Number(e.target.value)
                                                                        }
                                                                    }
                                                                }
                                                            }),
                                                        min: "0",
                                                        step: "1",
                                                        className: "w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                                        lineNumber: 455,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                                lineNumber: 451,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                        htmlFor: "price-30days",
                                                        className: "block text-sm text-neutral-700 dark:text-neutral-300 mb-1",
                                                        children: "30 Days"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                                        lineNumber: 478,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                        type: "number",
                                                        id: "price-30days",
                                                        value: settings.premium.prices.jobListing.thirtyDays,
                                                        onChange: (e)=>setSettings({
                                                                ...settings,
                                                                premium: {
                                                                    ...settings.premium,
                                                                    prices: {
                                                                        ...settings.premium.prices,
                                                                        jobListing: {
                                                                            ...settings.premium.prices.jobListing,
                                                                            thirtyDays: Number(e.target.value)
                                                                        }
                                                                    }
                                                                }
                                                            }),
                                                        min: "0",
                                                        step: "1",
                                                        className: "w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                                        lineNumber: 481,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                                lineNumber: 477,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                        lineNumber: 424,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                        className: "text-md font-medium mb-2",
                                        children: "CV Access Prices (€)"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                        lineNumber: 505,
                                        columnNumber: 15
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "grid grid-cols-3 gap-4",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                        htmlFor: "price-10invites",
                                                        className: "block text-sm text-neutral-700 dark:text-neutral-300 mb-1",
                                                        children: "10 Invites"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                                        lineNumber: 508,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                        type: "number",
                                                        id: "price-10invites",
                                                        value: settings.premium.prices.cvAccess.tenInvites,
                                                        onChange: (e)=>setSettings({
                                                                ...settings,
                                                                premium: {
                                                                    ...settings.premium,
                                                                    prices: {
                                                                        ...settings.premium.prices,
                                                                        cvAccess: {
                                                                            ...settings.premium.prices.cvAccess,
                                                                            tenInvites: Number(e.target.value)
                                                                        }
                                                                    }
                                                                }
                                                            }),
                                                        min: "0",
                                                        step: "1",
                                                        className: "w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                                        lineNumber: 511,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                                lineNumber: 507,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                        htmlFor: "price-25invites",
                                                        className: "block text-sm text-neutral-700 dark:text-neutral-300 mb-1",
                                                        children: "25 Invites"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                                        lineNumber: 534,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                        type: "number",
                                                        id: "price-25invites",
                                                        value: settings.premium.prices.cvAccess.twentyFiveInvites,
                                                        onChange: (e)=>setSettings({
                                                                ...settings,
                                                                premium: {
                                                                    ...settings.premium,
                                                                    prices: {
                                                                        ...settings.premium.prices,
                                                                        cvAccess: {
                                                                            ...settings.premium.prices.cvAccess,
                                                                            twentyFiveInvites: Number(e.target.value)
                                                                        }
                                                                    }
                                                                }
                                                            }),
                                                        min: "0",
                                                        step: "1",
                                                        className: "w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                                        lineNumber: 537,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                                lineNumber: 533,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                                        htmlFor: "price-50invites",
                                                        className: "block text-sm text-neutral-700 dark:text-neutral-300 mb-1",
                                                        children: "50 Invites"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                                        lineNumber: 560,
                                                        columnNumber: 19
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$server$2f$route$2d$modules$2f$app$2d$page$2f$vendored$2f$ssr$2f$react$2d$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$ssr$5d$__$28$ecmascript$29$__["jsxDEV"])("input", {
                                                        type: "number",
                                                        id: "price-50invites",
                                                        value: settings.premium.prices.cvAccess.fiftyInvites,
                                                        onChange: (e)=>setSettings({
                                                                ...settings,
                                                                premium: {
                                                                    ...settings.premium,
                                                                    prices: {
                                                                        ...settings.premium.prices,
                                                                        cvAccess: {
                                                                            ...settings.premium.prices.cvAccess,
                                                                            fiftyInvites: Number(e.target.value)
                                                                        }
                                                                    }
                                                                }
                                                            }),
                                                        min: "0",
                                                        step: "1",
                                                        className: "w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                                        lineNumber: 563,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/admin/settings/page.tsx",
                                                lineNumber: 559,
                                                columnNumber: 17
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/admin/settings/page.tsx",
                                        lineNumber: 506,
                                        columnNumber: 15
                                    }, this)
                                ]
                            }, void 0, true)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/admin/settings/page.tsx",
                        lineNumber: 397,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/admin/settings/page.tsx",
                lineNumber: 203,
                columnNumber: 7
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/admin/settings/page.tsx",
        lineNumber: 170,
        columnNumber: 5
    }, this);
}
}}),

};

//# sourceMappingURL=src_c67d8bb4._.js.map