"use client";

import Image from "next/image";
import Link from "next/link";
import { useAuth } from "@/contexts/AuthContext";

export default function Home() {
  return (
    <div className="space-y-16">
      {/* Hero Section */}
      <section className="py-20 bg-neutral-50 dark:bg-neutral-900 rounded-lg">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-4xl md:text-6xl font-bold mb-6">
            Connecting Balkan Workers with EU Companies
          </h1>
          <p className="text-xl md:text-2xl text-neutral-600 dark:text-neutral-400 mb-10 max-w-3xl mx-auto">
            Find the perfect job opportunity or hire skilled professionals from the Balkans
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              href="/jobs"
              className="bg-neutral-900 hover:bg-neutral-800 text-white px-8 py-3 rounded-md text-lg font-medium"
            >
              Browse <PERSON>
            </Link>
            <Link
              href="/auth"
              className="bg-white hover:bg-neutral-100 text-neutral-900 border border-neutral-300 px-8 py-3 rounded-md text-lg font-medium"
            >
              Sign Up Now
            </Link>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <h2 className="text-3xl font-bold text-center mb-12">How It Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* For Candidates */}
            <div className="bg-white dark:bg-neutral-800 p-8 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700">
              <div className="bg-neutral-100 dark:bg-neutral-700 w-12 h-12 flex items-center justify-center rounded-full mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-4">For Job Seekers</h3>
              <ul className="space-y-3 text-neutral-600 dark:text-neutral-400">
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  Create an anonymous profile with your skills and experience
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  Browse and apply to jobs across the EU
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  Get matched with employers based on your preferences
                </li>
              </ul>
              <Link href="/auth" className="mt-6 inline-block text-neutral-900 dark:text-white font-medium">
                Sign up as a Candidate →
              </Link>
            </div>

            {/* For Employers */}
            <div className="bg-white dark:bg-neutral-800 p-8 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700">
              <div className="bg-neutral-100 dark:bg-neutral-700 w-12 h-12 flex items-center justify-center rounded-full mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-4">For Employers</h3>
              <ul className="space-y-3 text-neutral-600 dark:text-neutral-400">
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  Post job listings and reach skilled Balkan workers
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  Highlight your opportunities with premium listings
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  Find candidates that match your specific requirements
                </li>
              </ul>
              <Link href="/auth" className="mt-6 inline-block text-neutral-900 dark:text-white font-medium">
                Sign up as an Employer →
              </Link>
            </div>

            {/* Premium Features */}
            <div className="bg-white dark:bg-neutral-800 p-8 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700">
              <div className="bg-neutral-100 dark:bg-neutral-700 w-12 h-12 flex items-center justify-center rounded-full mb-6">
                <svg xmlns="http://www.w3.org/2000/svg" className="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z" />
                </svg>
              </div>
              <h3 className="text-xl font-semibold mb-4">Premium Features</h3>
              <ul className="space-y-3 text-neutral-600 dark:text-neutral-400">
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  Boost visibility with premium job listings
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  Choose from 7, 14, or 30-day premium packages
                </li>
                <li className="flex items-start">
                  <span className="mr-2">•</span>
                  Advanced matching algorithm for better candidate selection
                </li>
              </ul>
              <Link href="/premium" className="mt-6 inline-block text-neutral-900 dark:text-white font-medium">
                Learn About Premium →
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Featured Jobs Section */}
      <section className="py-16">
        <div className="container mx-auto px-4">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-3xl font-bold">Featured Jobs</h2>
            <Link href="/jobs" className="text-neutral-900 dark:text-white font-medium">
              View All Jobs →
            </Link>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* This would be populated with actual jobs in a real implementation */}
            <div className="bg-white dark:bg-neutral-800 p-6 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700">
              <h3 className="text-xl font-semibold mb-2">Browse Our Job Listings</h3>
              <p className="text-neutral-600 dark:text-neutral-400 mb-4">
                Explore hundreds of job opportunities from companies across Europe looking for Balkan talent.
              </p>
              <Link
                href="/jobs"
                className="bg-neutral-900 hover:bg-neutral-800 text-white px-4 py-2 rounded-md inline-block"
              >
                Browse Jobs
              </Link>
            </div>
            <div className="bg-white dark:bg-neutral-800 p-6 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700">
              <h3 className="text-xl font-semibold mb-2">Create Your Profile</h3>
              <p className="text-neutral-600 dark:text-neutral-400 mb-4">
                Sign up and create your profile to apply for jobs or post new opportunities for Balkan workers.
              </p>
              <Link
                href="/auth"
                className="bg-neutral-900 hover:bg-neutral-800 text-white px-4 py-2 rounded-md inline-block"
              >
                Get Started
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* Call to Action */}
      <section className="py-16 bg-neutral-900 text-white rounded-lg">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl font-bold mb-6">Ready to Get Started?</h2>
          <p className="text-xl text-neutral-400 mb-8 max-w-2xl mx-auto">
            Join BalkanWorkForce today and connect with opportunities across Europe
          </p>
          <Link
            href="/auth"
            className="bg-white hover:bg-neutral-100 text-neutral-900 px-8 py-3 rounded-md text-lg font-medium inline-block"
          >
            Sign Up Now
          </Link>
        </div>
      </section>
    </div>
  );
}
