(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/data/positions.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "positions": (()=>positions)
});
const positions = [
    {
        id: "electrician",
        translations: {
            de: "Elektriker",
            en: "Electrician",
            bs: "Električar",
            hr: "Električar",
            sr: "Električar",
            me: "Električar",
            mk: "Електричар",
            al: "Elektricist"
        },
        category: "construction",
        skills: [
            "electrical-wiring",
            "electrical-maintenance",
            "electrical-installation"
        ]
    },
    {
        id: "plumber",
        translations: {
            de: "<PERSON><PERSON><PERSON><PERSON>",
            en: "Plumber",
            bs: "<PERSON>odoi<PERSON>ala<PERSON>",
            hr: "Vodoinstalater",
            sr: "Vodoinstalater",
            me: "Vodoinstalater",
            mk: "Водоводџија",
            al: "Hidraulik"
        },
        category: "construction",
        skills: [
            "plumbing",
            "pipe-fitting",
            "water-systems"
        ]
    },
    {
        id: "carpenter",
        translations: {
            de: "Tischler",
            en: "Carpenter",
            bs: "Stolar",
            hr: "Stolar",
            sr: "Stolar",
            me: "Stolar",
            mk: "Столар",
            al: "Marangoz"
        },
        category: "construction",
        skills: [
            "woodworking",
            "furniture-making",
            "carpentry"
        ]
    },
    {
        id: "welder",
        translations: {
            de: "Schweißer",
            en: "Welder",
            bs: "Zavarivač",
            hr: "Zavarivač",
            sr: "Zavarivač",
            me: "Zavarivač",
            mk: "Заварувач",
            al: "Saldator"
        },
        category: "manufacturing",
        skills: [
            "welding",
            "metal-fabrication",
            "blueprint-reading"
        ]
    },
    {
        id: "chef",
        translations: {
            de: "Koch",
            en: "Chef",
            bs: "Kuhar",
            hr: "Kuhar",
            sr: "Kuvar",
            me: "Kuvar",
            mk: "Готвач",
            al: "Kuzhinier"
        },
        category: "hospitality",
        skills: [
            "cooking",
            "food-preparation",
            "menu-planning"
        ]
    },
    {
        id: "waiter",
        translations: {
            de: "Kellner",
            en: "Waiter",
            bs: "Konobar",
            hr: "Konobar",
            sr: "Konobar",
            me: "Konobar",
            mk: "Келнер",
            al: "Kamarier"
        },
        category: "hospitality",
        skills: [
            "customer-service",
            "food-service",
            "order-taking"
        ]
    },
    {
        id: "nurse",
        translations: {
            de: "Krankenpfleger",
            en: "Nurse",
            bs: "Medicinska sestra/tehničar",
            hr: "Medicinska sestra/tehničar",
            sr: "Medicinska sestra/tehničar",
            me: "Medicinska sestra/tehničar",
            mk: "Медицинска сестра/техничар",
            al: "Infermier"
        },
        category: "healthcare",
        skills: [
            "patient-care",
            "medical-assistance",
            "health-monitoring"
        ]
    },
    {
        id: "caregiver",
        translations: {
            de: "Pflegekraft",
            en: "Caregiver",
            bs: "Njegovatelj",
            hr: "Njegovatelj",
            sr: "Negovatelj",
            me: "Njegovatelj",
            mk: "Негувател",
            al: "Kujdestar"
        },
        category: "healthcare",
        skills: [
            "elderly-care",
            "personal-care",
            "medication-management"
        ]
    },
    {
        id: "driver",
        translations: {
            de: "Fahrer",
            en: "Driver",
            bs: "Vozač",
            hr: "Vozač",
            sr: "Vozač",
            me: "Vozač",
            mk: "Возач",
            al: "Shofer"
        },
        category: "transportation",
        skills: [
            "driving",
            "vehicle-maintenance",
            "route-planning"
        ]
    },
    {
        id: "software-developer",
        translations: {
            de: "Softwareentwickler",
            en: "Software Developer",
            bs: "Programer",
            hr: "Programer",
            sr: "Programer",
            me: "Programer",
            mk: "Програмер",
            al: "Zhvillues Softueri"
        },
        category: "it",
        skills: [
            "programming",
            "software-development",
            "problem-solving"
        ]
    },
    {
        id: "accountant",
        translations: {
            de: "Buchhalter",
            en: "Accountant",
            bs: "Računovođa",
            hr: "Računovođa",
            sr: "Računovođa",
            me: "Računovođa",
            mk: "Сметководител",
            al: "Kontabilist"
        },
        category: "finance",
        skills: [
            "accounting",
            "bookkeeping",
            "financial-reporting"
        ]
    },
    {
        id: "teacher",
        translations: {
            de: "Lehrer",
            en: "Teacher",
            bs: "Nastavnik",
            hr: "Nastavnik",
            sr: "Nastavnik",
            me: "Nastavnik",
            mk: "Наставник",
            al: "Mësues"
        },
        category: "education",
        skills: [
            "teaching",
            "curriculum-development",
            "student-assessment"
        ]
    },
    {
        id: "sales-representative",
        translations: {
            de: "Vertriebsmitarbeiter",
            en: "Sales Representative",
            bs: "Prodajni predstavnik",
            hr: "Prodajni predstavnik",
            sr: "Prodajni predstavnik",
            me: "Prodajni predstavnik",
            mk: "Продажен претставник",
            al: "Përfaqësues Shitjesh"
        },
        category: "sales",
        skills: [
            "sales",
            "customer-relations",
            "negotiation"
        ]
    },
    {
        id: "warehouse-worker",
        translations: {
            de: "Lagerarbeiter",
            en: "Warehouse Worker",
            bs: "Skladištar",
            hr: "Skladištar",
            sr: "Magacioner",
            me: "Magacioner",
            mk: "Магационер",
            al: "Punëtor Magazinimi"
        },
        category: "logistics",
        skills: [
            "inventory-management",
            "forklift-operation",
            "order-picking"
        ]
    },
    {
        id: "cleaner",
        translations: {
            de: "Reinigungskraft",
            en: "Cleaner",
            bs: "Čistač",
            hr: "Čistač",
            sr: "Čistač",
            me: "Čistač",
            mk: "Хигиеничар",
            al: "Pastrues"
        },
        category: "facility-services",
        skills: [
            "cleaning",
            "sanitation",
            "housekeeping"
        ]
    }
];
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_data_positions_ts_55af3cd5._.js.map