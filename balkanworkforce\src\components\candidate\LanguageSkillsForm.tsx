import { useState, useEffect } from "react";
import { LanguageLevel, LanguageSkill } from "@/types";

interface LanguageSkillsFormProps {
  germanLevel: LanguageLevel;
  onGermanLevelChange: (level: LanguageLevel) => void;
  englishLevel: LanguageLevel;
  onEnglishLevelChange: (level: LanguageLevel) => void;
  otherLanguages: LanguageSkill[];
  onOtherLanguagesChange: (languages: LanguageSkill[]) => void;
}

const languageLevels: LanguageLevel[] = ["None", "A1", "A2", "B1", "B2", "C1", "C2", "Native"];

const commonLanguages = [
  "Albanian",
  "Bosnian",
  "Bulgarian",
  "Croatian",
  "Czech",
  "Dutch",
  "French",
  "Greek",
  "Hungarian",
  "Italian",
  "Macedonian",
  "Montenegrin",
  "Polish",
  "Portuguese",
  "Romanian",
  "Russian",
  "Serbian",
  "Slovak",
  "Slovenian",
  "Spanish",
  "Turkish",
  "Ukrainian",
];

export default function LanguageSkillsForm({
  germanLevel,
  onGermanLevelChange,
  englishLevel,
  onEnglishLevelChange,
  otherLanguages,
  onOtherLanguagesChange,
}: LanguageSkillsFormProps) {
  const [languages, setLanguages] = useState<LanguageSkill[]>(otherLanguages || []);

  useEffect(() => {
    onOtherLanguagesChange(languages);
  }, [languages, onOtherLanguagesChange]);

  const addLanguage = () => {
    setLanguages([
      ...languages,
      {
        language: "",
        level: "None",
      },
    ]);
  };

  const updateLanguage = (index: number, field: keyof LanguageSkill, value: any) => {
    const updatedLanguages = [...languages];
    updatedLanguages[index] = {
      ...updatedLanguages[index],
      [field]: value,
    };
    setLanguages(updatedLanguages);
  };

  const removeLanguage = (index: number) => {
    const updatedLanguages = [...languages];
    updatedLanguages.splice(index, 1);
    setLanguages(updatedLanguages);
  };

  return (
    <div className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <label className="block text-sm font-medium mb-1">
            German Language Level
          </label>
          <select
            value={germanLevel}
            onChange={(e) => onGermanLevelChange(e.target.value as LanguageLevel)}
            className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
          >
            {languageLevels.map((level) => (
              <option key={level} value={level}>
                {level === "None" ? "No knowledge" : level} {level !== "None" ? getLevelDescription(level) : ""}
              </option>
            ))}
          </select>
          <p className="mt-1 text-xs text-neutral-500 dark:text-neutral-400">
            German language skills are important for many employers
          </p>
        </div>

        <div>
          <label className="block text-sm font-medium mb-1">
            English Language Level
          </label>
          <select
            value={englishLevel}
            onChange={(e) => onEnglishLevelChange(e.target.value as LanguageLevel)}
            className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
          >
            {languageLevels.map((level) => (
              <option key={level} value={level}>
                {level === "None" ? "No knowledge" : level} {level !== "None" ? getLevelDescription(level) : ""}
              </option>
            ))}
          </select>
        </div>
      </div>

      <div className="mt-6">
        <h3 className="text-lg font-medium mb-4">Other Languages</h3>

        {languages.map((language, index) => (
          <div key={index} className="border border-neutral-200 dark:border-neutral-700 rounded-lg p-4 mb-4">
            <div className="flex justify-between items-center mb-4">
              <h4 className="font-medium">Additional Language #{index + 1}</h4>
              <button
                type="button"
                onClick={() => removeLanguage(index)}
                className="text-red-600 hover:text-red-800 dark:text-red-400 dark:hover:text-red-300"
              >
                Remove
              </button>
            </div>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label className="block text-sm font-medium mb-1">
                  Language
                </label>
                <input
                  type="text"
                  list="common-languages"
                  value={language.language}
                  onChange={(e) => updateLanguage(index, "language", e.target.value)}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                  placeholder="e.g., French, Spanish, Italian"
                />
                <datalist id="common-languages">
                  {commonLanguages.map((lang) => (
                    <option key={lang} value={lang} />
                  ))}
                </datalist>
              </div>

              <div>
                <label className="block text-sm font-medium mb-1">
                  Level
                </label>
                <select
                  value={language.level}
                  onChange={(e) => updateLanguage(index, "level", e.target.value as LanguageLevel)}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                >
                  {languageLevels.map((level) => (
                    <option key={level} value={level}>
                      {level === "None" ? "No knowledge" : level} {level !== "None" ? getLevelDescription(level) : ""}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          </div>
        ))}

        <button
          type="button"
          onClick={addLanguage}
          className="flex items-center text-neutral-700 dark:text-neutral-300 hover:text-neutral-900 dark:hover:text-white"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
            <path fillRule="evenodd" d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z" clipRule="evenodd" />
          </svg>
          Add Another Language
        </button>
      </div>
    </div>
  );
}

function getLevelDescription(level: LanguageLevel): string {
  switch (level) {
    case "None":
      return "";
    case "A1":
      return "(Beginner)";
    case "A2":
      return "(Elementary)";
    case "B1":
      return "(Intermediate)";
    case "B2":
      return "(Upper Intermediate)";
    case "C1":
      return "(Advanced)";
    case "C2":
      return "(Proficient)";
    case "Native":
      return "(Mother Tongue)";
    default:
      return "";
  }
}
