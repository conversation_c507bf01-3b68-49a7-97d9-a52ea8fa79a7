"use client";

import { useState, useEffect } from "react";
import { useParams, useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { doc, getDoc, collection, query, where, getDocs, addDoc, serverTimestamp } from "firebase/firestore";
import { db } from "@/lib/firebase";
import Link from "next/link";
import Image from "next/image";
import { jobBenefits } from "@/data/jobBenefits";
import { CandidateInvite, EmployerSubscription } from "@/types";
import ExperienceDisplay from "@/components/candidate/ExperienceDisplay";
import EducationDisplay from "@/components/candidate/EducationDisplay";
import LanguageSkillsDisplay from "@/components/candidate/LanguageSkillsDisplay";
import { countries } from "@/data/countries";

export default function CandidateDetailPage() {
  const { id } = useParams();
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [candidate, setCandidate] = useState<any>(null);
  const [employerJobs, setEmployerJobs] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [showInviteModal, setShowInviteModal] = useState(false);
  const [selectedJobId, setSelectedJobId] = useState<string>("");
  const [inviteMessage, setInviteMessage] = useState<string>("");
  const [sending, setSending] = useState(false);
  const [remainingInvites, setRemainingInvites] = useState<number>(0);
  const [subscriptions, setSubscriptions] = useState<EmployerSubscription[]>([]);

  useEffect(() => {
    // If not authenticated or not an employer, redirect to auth page
    if (!authLoading && (!user || user.role !== "employer")) {
      router.push("/auth");
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    const fetchData = async () => {
      if (!user || !id || typeof id !== "string") return;

      try {
        // Fetch candidate data
        const candidateDoc = await getDoc(doc(db, "candidates", id));

        if (!candidateDoc.exists()) {
          setError("Candidate not found");
          return;
        }

        const candidateData = candidateDoc.data();
        setCandidate({
          id: candidateDoc.id,
          ...candidateData,
          createdAt: candidateData.createdAt?.toDate(),
          updatedAt: candidateData.updatedAt?.toDate(),
        });

        // Fetch employer's jobs
        const jobsCollection = collection(db, "jobs");
        const jobsQuery = query(
          jobsCollection,
          where("employerId", "==", user.uid)
        );

        const querySnapshot = await getDocs(jobsQuery);
        const jobsList: any[] = [];

        querySnapshot.forEach((doc) => {
          const data = doc.data();
          jobsList.push({
            id: doc.id,
            ...data,
            createdAt: data.createdAt.toDate(),
            updatedAt: data.updatedAt.toDate(),
            premiumUntil: data.premiumUntil ? data.premiumUntil.toDate() : undefined,
          });
        });

        setEmployerJobs(jobsList);

        // Fetch employer's active subscriptions
        const subscriptionsCollection = collection(db, "subscriptions");
        const subscriptionsQuery = query(
          subscriptionsCollection,
          where("employerId", "==", user.uid),
          where("isActive", "==", true)
        );

        const subscriptionsSnapshot = await getDocs(subscriptionsQuery);
        const subscriptionsList: EmployerSubscription[] = [];
        let totalInvites = 0;

        subscriptionsSnapshot.forEach((doc) => {
          const data = doc.data();
          const subscription = {
            id: doc.id,
            ...data,
            startDate: data.startDate.toDate(),
            endDate: data.endDate ? data.endDate.toDate() : undefined,
            createdAt: data.createdAt.toDate(),
            updatedAt: data.updatedAt.toDate(),
          } as EmployerSubscription;

          subscriptionsList.push(subscription);

          if (subscription.packageType === 'cv' || subscription.packageType === 'combined') {
            totalInvites += subscription.invitesRemaining;
          }
        });

        setSubscriptions(subscriptionsList);
        setRemainingInvites(totalInvites);
      } catch (error) {
        console.error("Error fetching data:", error);
        setError("Failed to load candidate details. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    if (user && user.role === "employer") {
      fetchData();
    }
  }, [user, id, router]);

  // Calculate match percentage between candidate and job
  const calculateMatchPercentage = (job: any) => {
    if (!candidate.benefits || !job.benefits) return 0;

    const candidateBenefits = candidate.benefits;
    const jobBenefits = job.benefits;

    // Count matching benefits
    const matchingBenefits = candidateBenefits.filter((benefit: string) =>
      jobBenefits.includes(benefit)
    ).length;

    // Calculate percentage based on candidate's preferences
    return Math.round((matchingBenefits / candidateBenefits.length) * 100);
  };

  const handleInvite = (jobId: string) => {
    if (remainingInvites <= 0) {
      // Redirect to packages page if no invites remaining
      router.push('/employer/packages');
      return;
    }

    setSelectedJobId(jobId);
    setInviteMessage("");
    setShowInviteModal(true);
  };

  const sendInvite = async () => {
    if (!user || !candidate || !selectedJobId) return;

    setSending(true);

    try {
      // Find the job details
      const selectedJob = employerJobs.find(job => job.id === selectedJobId);

      if (!selectedJob) {
        throw new Error("Selected job not found");
      }

      // Create invite document
      const inviteData = {
        employerId: user.uid,
        candidateId: candidate.id,
        jobId: selectedJobId,
        message: inviteMessage.trim() || undefined,
        status: "pending",
        createdAt: new Date(),
        updatedAt: new Date(),
        expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000), // 30 days from now
        jobTitle: selectedJob.title,
        employerCompanyName: user.companyName || "Company",
        candidateProfession: candidate.profession,
        candidateAge: candidate.age
      };

      await addDoc(collection(db, "invites"), inviteData);

      // Find the subscription to deduct an invite from
      // For simplicity, we'll just use the first subscription with remaining invites
      const subscriptionToUpdate = subscriptions.find(sub =>
        (sub.packageType === 'cv' || sub.packageType === 'combined') && sub.invitesRemaining > 0
      );

      if (subscriptionToUpdate) {
        // Update the subscription in Firestore
        await fetch('/api/subscriptions/use-invite', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
          },
          body: JSON.stringify({
            subscriptionId: subscriptionToUpdate.id,
          }),
        });

        // Update local state
        setRemainingInvites(prev => prev - 1);
        setSubscriptions(prev =>
          prev.map(sub =>
            sub.id === subscriptionToUpdate.id
              ? { ...sub, invitesRemaining: sub.invitesRemaining - 1 }
              : sub
          )
        );
      }

      // Close modal and show success message
      setShowInviteModal(false);
      alert("Invitation sent successfully!");
    } catch (error) {
      console.error("Error sending invite:", error);
      alert("Failed to send invitation. Please try again.");
    } finally {
      setSending(false);
    }
  };

  if (authLoading || loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto py-12">
        <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6">
          {error}
        </div>
        <Link
          href="/employer/candidates"
          className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
        >
          Back to Candidates
        </Link>
      </div>
    );
  }

  if (!candidate) {
    return (
      <div className="max-w-4xl mx-auto py-12">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Candidate Not Found</h2>
          <p className="text-neutral-600 dark:text-neutral-400 mb-6">
            The candidate you're looking for doesn't exist or you don't have permission to view it.
          </p>
          <Link
            href="/employer/candidates"
            className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
          >
            Back to Candidates
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto py-12">
      {/* Invite Modal */}
      {showInviteModal && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-lg max-w-md w-full p-6">
            <h3 className="text-xl font-semibold mb-4">Invite Candidate to Apply</h3>

            <div className="mb-4">
              <label className="block text-sm font-medium mb-1">
                Select Job
              </label>
              <select
                value={selectedJobId}
                onChange={(e) => setSelectedJobId(e.target.value)}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md bg-white dark:bg-neutral-800"
              >
                {employerJobs.map((job) => (
                  <option key={job.id} value={job.id}>
                    {job.title}
                  </option>
                ))}
              </select>
            </div>

            <div className="mb-6">
              <label className="block text-sm font-medium mb-1">
                Message (Optional)
              </label>
              <textarea
                value={inviteMessage}
                onChange={(e) => setInviteMessage(e.target.value)}
                rows={4}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                placeholder="Add a personal message to the candidate..."
              ></textarea>
            </div>

            <div className="flex justify-between">
              <button
                onClick={() => setShowInviteModal(false)}
                className="px-4 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-neutral-700 dark:text-neutral-300"
              >
                Cancel
              </button>
              <button
                onClick={sendInvite}
                disabled={sending || !selectedJobId}
                className={`px-4 py-2 rounded-md ${sending ? "bg-neutral-700 text-white" : "bg-neutral-900 hover:bg-neutral-800 text-white"}`}
              >
                {sending ? "Sending..." : "Send Invitation"}
              </button>
            </div>

            <div className="mt-4 text-sm text-neutral-500 dark:text-neutral-400">
              You have {remainingInvites} invite{remainingInvites !== 1 ? "s" : ""} remaining.
            </div>
          </div>
        </div>
      )}
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Candidate Profile</h1>
        <Link
          href="/employer/candidates"
          className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
        >
          Back to Candidates
        </Link>
      </div>

      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8 mb-8">
        <div className="flex items-center mb-6">
          {candidate.photoURL ? (
            <div className="w-24 h-24 rounded-full overflow-hidden mr-6">
              <Image
                src={candidate.photoURL}
                alt="Profile"
                width={96}
                height={96}
                className="w-full h-full object-cover"
              />
            </div>
          ) : (
            <div className="w-24 h-24 bg-neutral-200 dark:bg-neutral-700 rounded-full flex items-center justify-center mr-6">
              <svg xmlns="http://www.w3.org/2000/svg" className="h-12 w-12 text-neutral-500" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
              </svg>
            </div>
          )}
          <div>
            <h2 className="text-2xl font-bold mb-1">
              {candidate.profession}, {candidate.age}
            </h2>
            <div className="flex items-center">
              {candidate.country && (
                <div className="flex items-center mr-3">
                  {countries.find(c => c.id === candidate.country)?.flagUrl && (
                    <div className="w-4 h-3 mr-1 overflow-hidden rounded-sm">
                      <Image
                        src={countries.find(c => c.id === candidate.country)?.flagUrl || ""}
                        alt={countries.find(c => c.id === candidate.country)?.name || ""}
                        width={16}
                        height={12}
                        className="w-full h-full object-cover"
                      />
                    </div>
                  )}
                  <span className="text-neutral-600 dark:text-neutral-400">
                    {countries.find(c => c.id === candidate.country)?.name}
                  </span>
                </div>
              )}
              <div className="flex items-center">
                <span className="text-neutral-600 dark:text-neutral-400">
                  German: {candidate.germanLanguageLevel || "Not specified"},
                  English: {candidate.englishLanguageLevel || "Not specified"}
                </span>
              </div>
            </div>
            <p className="text-sm text-neutral-500 dark:text-neutral-500 mt-1">
              Member since {candidate.createdAt?.toLocaleDateString() || "N/A"}
            </p>
          </div>
        </div>

        <div className="border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6">
          <h3 className="text-xl font-semibold mb-4">Skills</h3>
          {candidate.skills && candidate.skills.length > 0 ? (
            <div className="flex flex-wrap gap-2">
              {candidate.skills.map((skill: string, index: number) => (
                <span
                  key={index}
                  className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 px-3 py-1 rounded-md"
                >
                  {skill}
                </span>
              ))}
            </div>
          ) : (
            <p className="text-neutral-600 dark:text-neutral-400">No skills specified</p>
          )}
        </div>

        <div className="border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6">
          <h3 className="text-xl font-semibold mb-4">Experience</h3>
          <ExperienceDisplay
            experiences={candidate.workExperience || []}
            noWorkExperience={candidate.noWorkExperience}
          />
        </div>

        <div className="border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6">
          <h3 className="text-xl font-semibold mb-4">Education</h3>
          <EducationDisplay educations={candidate.education || []} />
        </div>

        <div className="border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6">
          <h3 className="text-xl font-semibold mb-4">Language Skills</h3>
          <LanguageSkillsDisplay
            germanLevel={candidate.germanLanguageLevel || "A1"}
            englishLevel={candidate.englishLanguageLevel || "A1"}
            otherLanguages={candidate.otherLanguages || []}
          />
        </div>

        {candidate.benefits && candidate.benefits.length > 0 && (
          <div className="border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6">
            <h3 className="text-xl font-semibold mb-4">Job Preferences</h3>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
              {candidate.benefits.map((benefitId: string) => {
                const benefit = jobBenefits.find(b => b.id === benefitId);
                return (
                  <div key={benefitId} className="flex items-center">
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500 mr-2" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                    </svg>
                    <span>{benefit ? benefit.label : benefitId}</span>
                  </div>
                );
              })}
            </div>
          </div>
        )}

        {candidate.desiredPositions && candidate.desiredPositions.length > 0 && (
          <div className="border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6">
            <h3 className="text-xl font-semibold mb-4">Desired Positions</h3>
            <div className="flex flex-wrap gap-2">
              {candidate.desiredPositions.map((position: string, index: number) => (
                <span
                  key={index}
                  className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 px-3 py-1 rounded-md"
                >
                  {position}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>

      {employerJobs.length > 0 && (
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8">
          <h3 className="text-xl font-semibold mb-6">Match with Your Jobs</h3>
          <div className="space-y-4">
            {employerJobs.map((job) => {
              const matchPercentage = calculateMatchPercentage(job);
              return (
                <div
                  key={job.id}
                  className="border border-neutral-200 dark:border-neutral-700 rounded-lg p-4"
                >
                  <div className="flex justify-between items-start">
                    <div>
                      <h4 className="font-medium">{job.title}</h4>
                      <p className="text-sm text-neutral-600 dark:text-neutral-400">
                        {job.location} • {job.employmentType.replace("-", " ")}
                      </p>
                    </div>
                    <div className="flex items-center">
                      <div className="w-12 h-12 rounded-full bg-neutral-100 dark:bg-neutral-800 border-4 border-neutral-200 dark:border-neutral-700 flex items-center justify-center mr-2">
                        <span className="text-sm font-medium">{matchPercentage}%</span>
                      </div>
                      <div className="text-xs">
                        Match
                      </div>
                    </div>
                  </div>
                  <div className="mt-3 flex items-center">
                    <div className="w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2">
                      <div
                        className={`h-2 rounded-full ${
                          matchPercentage >= 80 ? "bg-green-500" :
                          matchPercentage >= 50 ? "bg-yellow-500" :
                          "bg-red-500"
                        }`}
                        style={{ width: `${matchPercentage}%` }}
                      ></div>
                    </div>
                  </div>
                  <div className="mt-4 flex justify-between items-center">
                    <Link
                      href={`/employer/jobs/${job.id}`}
                      className="text-sm text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
                    >
                      View Job
                    </Link>
                    <button
                      className="bg-neutral-900 hover:bg-neutral-800 text-white px-4 py-2 rounded-md text-sm"
                      onClick={() => handleInvite(job.id)}
                    >
                      {remainingInvites > 0 ? "Invite to Apply" : "Buy Invites"}
                    </button>
                  </div>
                </div>
              );
            })}
          </div>
        </div>
      )}
    </div>
  );
}
