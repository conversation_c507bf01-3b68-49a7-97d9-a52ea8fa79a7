import { useState, useEffect } from "react";
import { LanguageLevel, JobRequirements } from "@/types";
import { degrees } from "@/data/degrees";
import { driverLicensesByCategory } from "@/data/driverLicenses";
import Image from "next/image";
import PositionSelect from "@/components/forms/PositionSelect";
import CountrySelect from "@/components/forms/CountrySelect";
import { useFirestoreData } from "@/hooks/useFirestoreData";
import { Position, Country, DriverLicense } from "@/types";

interface StructuredRequirementsFormProps {
  value: JobRequirements;
  onChange: (value: JobRequirements) => void;
}

export default function StructuredRequirementsForm({
  value,
  onChange,
}: StructuredRequirementsFormProps) {
  const { data: positions } = useFirestoreData<Position>("positions");
  const { data: countries } = useFirestoreData<Country>("countries");
  const { data: driverLicenses } = useFirestoreData<DriverLicense>("driverLicenses");

  const [requirements, setRequirements] = useState<JobRequirements>(
    value || {
      germanLanguageLevel: undefined,
      englishLanguageLevel: undefined,
      otherLanguages: [],
      requiredPositions: [],
      minEducationLevel: undefined,
      driverLicense: undefined,
      requiredCountries: [],
    }
  );

  useEffect(() => {
    onChange(requirements);
  }, [requirements, onChange]);

  // Language requirements handlers
  const handleLanguageLevelChange = (
    language: "germanLanguageLevel" | "englishLanguageLevel",
    level: LanguageLevel | undefined
  ) => {
    setRequirements({
      ...requirements,
      [language]: level,
    });
  };

  // Position requirements handlers
  const addPositionRequirement = () => {
    setRequirements({
      ...requirements,
      requiredPositions: [
        ...(requirements.requiredPositions || []),
        { positionId: "", minExperienceMonths: 0 },
      ],
    });
  };

  const updatePositionRequirement = (
    index: number,
    field: "positionId" | "minExperienceMonths",
    value: string | number
  ) => {
    const updatedPositions = [...(requirements.requiredPositions || [])];
    updatedPositions[index] = {
      ...updatedPositions[index],
      [field]: value,
    };
    setRequirements({
      ...requirements,
      requiredPositions: updatedPositions,
    });
  };

  const removePositionRequirement = (index: number) => {
    const updatedPositions = [...(requirements.requiredPositions || [])];
    updatedPositions.splice(index, 1);
    setRequirements({
      ...requirements,
      requiredPositions: updatedPositions,
    });
  };

  // Education requirements handlers
  const handleEducationLevelChange = (level: string | undefined) => {
    setRequirements({
      ...requirements,
      minEducationLevel: level,
    });
  };

  // Driver's license handler
  const handleDriverLicenseChange = (licenseId: string | undefined) => {
    setRequirements({
      ...requirements,
      driverLicense: licenseId,
    });
  };

  // Country requirements handlers
  const handleCountryChange = (countryId: string, checked: boolean) => {
    const currentCountries = [...(requirements.requiredCountries || [])];
    if (checked && !currentCountries.includes(countryId)) {
      setRequirements({
        ...requirements,
        requiredCountries: [...currentCountries, countryId],
      });
    } else if (!checked && currentCountries.includes(countryId)) {
      setRequirements({
        ...requirements,
        requiredCountries: currentCountries.filter((id) => id !== countryId),
      });
    }
  };

  // Group countries by region (with fallback for when data is loading)
  const euCountries = countries?.filter(
    (country) => country.region === "eu" || ["de", "at", "ch", "fr", "it", "nl", "be", "lu", "dk", "se", "fi", "ie", "pt", "es"].includes(country.id)
  ) || [];

  const balkanCountries = countries?.filter(
    (country) => country.region === "balkans" || ["ba", "rs", "hr", "me", "mk", "si", "al", "bg", "ro", "gr"].includes(country.id)
  ) || [];

  // Group degrees by level
  const degreesByLevel: Record<string, typeof degrees> = {
    secondary: degrees.filter((d) => d.level === "secondary"),
    vocational: degrees.filter((d) => d.level === "vocational"),
    tertiary: degrees.filter((d) => d.level === "tertiary"),
    bachelor: degrees.filter((d) => d.level === "bachelor"),
    master: degrees.filter((d) => d.level === "master"),
    doctorate: degrees.filter((d) => d.level === "doctorate"),
  };

  // Define language levels array to match candidate profile
  const languageLevels: LanguageLevel[] = ["None", "A1", "A2", "B1", "B2", "C1", "C2", "Native"];

  // Helper function to get language level description
  function getLevelDescription(level: LanguageLevel): string {
    switch (level) {
      case "None":
        return "";
      case "A1":
        return "(Beginner)";
      case "A2":
        return "(Elementary)";
      case "B1":
        return "(Intermediate)";
      case "B2":
        return "(Upper Intermediate)";
      case "C1":
        return "(Advanced)";
      case "C2":
        return "(Proficient)";
      case "Native":
        return "(Mother Tongue)";
      default:
        return "";
    }
  }

  return (
    <div className="space-y-8">
      {/* Language Requirements */}
      <div>
        <h3 className="text-lg font-medium mb-4">Language Requirements</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <label className="block text-sm font-medium mb-1">
              German Language Level
            </label>
            <select
              value={requirements.germanLanguageLevel || ""}
              onChange={(e) =>
                handleLanguageLevelChange(
                  "germanLanguageLevel",
                  e.target.value ? (e.target.value as LanguageLevel) : undefined
                )
              }
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
            >
              <option value="">Not required</option>
              {languageLevels.map((level) => (
                <option key={level} value={level}>
                  {level === "None" ? "No knowledge" : level} {level !== "None" ? getLevelDescription(level) : ""}
                </option>
              ))}
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-1">
              English Language Level
            </label>
            <select
              value={requirements.englishLanguageLevel || ""}
              onChange={(e) =>
                handleLanguageLevelChange(
                  "englishLanguageLevel",
                  e.target.value ? (e.target.value as LanguageLevel) : undefined
                )
              }
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
            >
              <option value="">Not required</option>
              {languageLevels.map((level) => (
                <option key={level} value={level}>
                  {level === "None" ? "No knowledge" : level} {level !== "None" ? getLevelDescription(level) : ""}
                </option>
              ))}
            </select>
          </div>
        </div>
      </div>

      {/* Work Experience Requirements */}
      <div>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-medium">Work Experience Requirements</h3>
          <button
            type="button"
            onClick={addPositionRequirement}
            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm flex items-center"
          >
            <svg
              xmlns="http://www.w3.org/2000/svg"
              className="h-4 w-4 mr-1"
              viewBox="0 0 20 20"
              fill="currentColor"
            >
              <path
                fillRule="evenodd"
                d="M10 5a1 1 0 011 1v3h3a1 1 0 110 2h-3v3a1 1 0 11-2 0v-3H6a1 1 0 110-2h3V6a1 1 0 011-1z"
                clipRule="evenodd"
              />
            </svg>
            Add Position
          </button>
        </div>
        {requirements.requiredPositions && requirements.requiredPositions.length > 0 ? (
          <div className="space-y-4">
            {requirements.requiredPositions.map((position, index) => (
              <div
                key={index}
                className="grid grid-cols-1 md:grid-cols-3 gap-4 p-4 border border-neutral-200 dark:border-neutral-700 rounded-md"
              >
                <div className="md:col-span-2">
                  <label className="block text-sm font-medium mb-1">
                    Position
                  </label>
                  <PositionSelect
                    value={position.positionId}
                    onChange={(value) =>
                      updatePositionRequirement(index, "positionId", value)
                    }
                    className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                    placeholder="Select a position"
                  />
                </div>
                <div>
                  <label className="block text-sm font-medium mb-1">
                    Min. Experience (months)
                  </label>
                  <div className="flex">
                    <input
                      type="number"
                      min="0"
                      value={position.minExperienceMonths}
                      onChange={(e) =>
                        updatePositionRequirement(
                          index,
                          "minExperienceMonths",
                          parseInt(e.target.value) || 0
                        )
                      }
                      className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-l-md"
                    />
                    <button
                      type="button"
                      onClick={() => removePositionRequirement(index)}
                      className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 px-3 py-2 rounded-r-md border border-red-200 dark:border-red-800"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        className="h-5 w-5"
                        viewBox="0 0 20 20"
                        fill="currentColor"
                      >
                        <path
                          fillRule="evenodd"
                          d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z"
                          clipRule="evenodd"
                        />
                      </svg>
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        ) : (
          <p className="text-neutral-500 dark:text-neutral-400 text-sm">
            No position requirements added. Click "Add Position" to specify required work experience.
          </p>
        )}
      </div>

      {/* Education Requirements */}
      <div>
        <h3 className="text-lg font-medium mb-4">Education Requirements</h3>
        <div className="mb-6">
          <label className="block text-sm font-medium mb-1">
            Minimum Education Level
          </label>
          <select
            value={requirements.minEducationLevel || ""}
            onChange={(e) =>
              handleEducationLevelChange(
                e.target.value || undefined
              )
            }
            className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
          >
            <option value="">Not required</option>
            {degrees.map((degree) => (
              <option key={degree.id} value={degree.id}>
                {degree.translations.en}
              </option>
            ))}
          </select>
        </div>
      </div>

      {/* Driver's License Requirements */}
      <div>
        <h3 className="text-lg font-medium mb-4">Driver's License Requirements</h3>
        <p className="text-sm text-neutral-600 dark:text-neutral-400 mb-4">
          Select the minimum driver's license category required for this job
        </p>

        <div className="mb-6">
          <select
            value={requirements.driverLicense || ""}
            onChange={(e) => handleDriverLicenseChange(e.target.value || undefined)}
            className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
          >
            <option value="">Not required</option>
            <optgroup label="Car Licenses">
              {driverLicensesByCategory.car.map((license) => (
                <option key={license.id} value={license.id}>
                  {license.name} - {license.description}
                </option>
              ))}
            </optgroup>
            <optgroup label="Motorcycle Licenses">
              {driverLicensesByCategory.motorcycle.map((license) => (
                <option key={license.id} value={license.id}>
                  {license.name} - {license.description}
                </option>
              ))}
            </optgroup>
            <optgroup label="Truck Licenses">
              {driverLicensesByCategory.truck.map((license) => (
                <option key={license.id} value={license.id}>
                  {license.name} - {license.description}
                </option>
              ))}
            </optgroup>
            <optgroup label="Bus Licenses">
              {driverLicensesByCategory.bus.map((license) => (
                <option key={license.id} value={license.id}>
                  {license.name} - {license.description}
                </option>
              ))}
            </optgroup>
          </select>
        </div>
      </div>

      {/* Country Requirements */}
      <div>
        <h3 className="text-lg font-medium mb-4">Job Location</h3>
        <p className="text-sm text-neutral-600 dark:text-neutral-400 mb-4">
          Select the country where this job is located
        </p>

        <div className="grid grid-cols-2 gap-4">
          {['de', 'at', 'hr', 'si'].map((countryId) => {
            const country = countries?.find(c => c.id === countryId);
            const isSelected = (requirements.requiredCountries || []).includes(countryId);
            return (
              <div
                key={countryId}
                className={`flex items-center p-4 border rounded-md cursor-pointer ${isSelected ? 'border-blue-500 bg-blue-50 dark:bg-blue-900/20 dark:border-blue-700' : 'border-neutral-200 dark:border-neutral-700'}`}
                onClick={() => {
                  // Set this as the only selected country
                  setRequirements({
                    ...requirements,
                    requiredCountries: [countryId]
                  });
                }}
              >
                {country?.flag && (
                  <span className="text-xl mr-3">{country.flag}</span>
                )}
                <span className="font-medium">{country ? (country.translations?.en || country.name) : countryId}</span>
              </div>
            );
          })}
        </div>
      </div>
    </div>
  );
}
