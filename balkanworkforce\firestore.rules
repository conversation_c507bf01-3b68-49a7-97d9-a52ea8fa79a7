rules_version = '2';

service cloud.firestore {
  match /databases/{database}/documents {
    // Allow anyone to read jobs
    match /jobs/{jobId} {
      allow read: if true;
      allow create, update: if request.auth != null;
      allow delete: if request.auth != null && (
        request.auth.uid == resource.data.employerId ||
        exists(/databases/$(database)/documents/admins/$(request.auth.uid))
      );
    }

    // Users collection - needed for authentication
    match /users/{userId} {
      allow read: if request.auth != null && request.auth.uid == userId;
      allow write: if request.auth != null && request.auth.uid == userId;
    }

    // Employers collection
    match /employers/{employerId} {
      allow read: if true;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null && (
        request.auth.uid == employerId ||
        exists(/databases/$(database)/documents/admins/$(request.auth.uid))
      );
    }

    // Candidates collection
    match /candidates/{candidateId} {
      allow read: if true;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null && (
        request.auth.uid == candidateId ||
        exists(/databases/$(database)/documents/admins/$(request.auth.uid))
      );
    }

    // Applications collection
    match /applications/{applicationId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null;
    }

    // Invitations collection
    match /invitations/{invitationId} {
      allow read: if request.auth != null;
      allow create: if request.auth != null;
      allow update, delete: if request.auth != null;
    }

    // Admins collection
    match /admins/{adminId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null &&
        exists(/databases/$(database)/documents/admins/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/admins/$(request.auth.uid)).data.permissions.superAdmin == true;
    }

    // Admin-managed collections
    match /positions/{positionId} {
      allow read: if true;
      allow write: if request.auth != null && exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }

    match /studyFields/{fieldId} {
      allow read: if true;
      allow write: if request.auth != null && exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }

    match /driverLicenses/{licenseId} {
      allow read: if true;
      allow write: if request.auth != null && exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }

    match /benefits/{benefitId} {
      allow read: if true;
      allow write: if request.auth != null && exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }

    match /countries/{countryId} {
      allow read: if true;
      allow write: if request.auth != null && exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }

    match /settings/{settingId} {
      allow read: if true;
      allow write: if request.auth != null && exists(/databases/$(database)/documents/admins/$(request.auth.uid));
    }

    match /adminActivityLog/{logId} {
      allow read: if request.auth != null && exists(/databases/$(database)/documents/admins/$(request.auth.uid));
      allow create: if request.auth != null && exists(/databases/$(database)/documents/admins/$(request.auth.uid));
      allow update, delete: if request.auth != null &&
        exists(/databases/$(database)/documents/admins/$(request.auth.uid)) &&
        get(/databases/$(database)/documents/admins/$(request.auth.uid)).data.permissions.superAdmin == true;
    }
  }
}
