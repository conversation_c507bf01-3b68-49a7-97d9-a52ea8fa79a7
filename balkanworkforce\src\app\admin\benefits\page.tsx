"use client";

import { useState, useEffect } from "react";
import { collection, query, getDocs, orderBy, doc, updateDoc, deleteDoc, setDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";

interface Benefit {
  id: string;
  label: string;
  translations: {
    [key: string]: string;
  };
  category: string;
  icon?: string;
}

export default function AdminBenefitsPage() {
  const [benefits, setBenefits] = useState<Benefit[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [editingBenefit, setEditingBenefit] = useState<Benefit | null>(null);
  const [newBenefit, setNewBenefit] = useState<Partial<Benefit>>({
    id: "",
    label: "",
    translations: { en: "", de: "", bs: "" },
    category: "accommodation",
    icon: "",
  });
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const supportedLanguages = [
    { code: "en", name: "English" },
    { code: "de", name: "German" },
    { code: "bs", name: "Bosnian" },
  ];

  const categories = [
    "accommodation",
    "transportation",
    "meals",
    "insurance",
    "workHours",
    "vacation",
    "other",
  ];

  useEffect(() => {
    const fetchBenefits = async () => {
      try {
        const benefitsQuery = query(
          collection(db, "benefits"),
          orderBy("category", "asc")
        );
        const snapshot = await getDocs(benefitsQuery);
        const benefitsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        })) as Benefit[];
        setBenefits(benefitsData);
      } catch (error) {
        console.error("Error fetching benefits:", error);
        setError("Failed to load benefits. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchBenefits();
  }, []);

  const filteredBenefits = benefits.filter(benefit => {
    const searchLower = searchTerm.toLowerCase();
    return (
      benefit.id.toLowerCase().includes(searchLower) ||
      benefit.label.toLowerCase().includes(searchLower) ||
      benefit.category.toLowerCase().includes(searchLower) ||
      Object.values(benefit.translations).some(translation => 
        translation.toLowerCase().includes(searchLower)
      )
    );
  });

  const handleEditBenefit = (benefit: Benefit) => {
    setEditingBenefit(benefit);
    setError(null);
  };

  const handleCancelEdit = () => {
    setEditingBenefit(null);
    setError(null);
  };

  const handleUpdateBenefit = async () => {
    if (!editingBenefit) return;

    // Validate
    if (!editingBenefit.label || !editingBenefit.category) {
      setError("Label and category are required");
      return;
    }

    // Check if at least one translation is provided
    const hasTranslation = Object.values(editingBenefit.translations).some(t => t.trim() !== "");
    if (!hasTranslation) {
      setError("At least one translation is required");
      return;
    }

    try {
      await updateDoc(doc(db, "benefits", editingBenefit.id), {
        label: editingBenefit.label,
        translations: editingBenefit.translations,
        category: editingBenefit.category,
        icon: editingBenefit.icon || null,
        updatedAt: new Date(),
      });

      // Update local state
      setBenefits(benefits.map(b => 
        b.id === editingBenefit.id ? editingBenefit : b
      ));
      
      setEditingBenefit(null);
      setSuccess("Benefit updated successfully");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Error updating benefit:", error);
      setError("Failed to update benefit. Please try again.");
    }
  };

  const handleDeleteBenefit = async (benefitId: string) => {
    if (!confirm("Are you sure you want to delete this benefit? This action cannot be undone.")) {
      return;
    }

    try {
      await deleteDoc(doc(db, "benefits", benefitId));
      
      // Update local state
      setBenefits(benefits.filter(b => b.id !== benefitId));
      setSuccess("Benefit deleted successfully");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Error deleting benefit:", error);
      setError("Failed to delete benefit. Please try again.");
    }
  };

  const handleAddNewBenefit = () => {
    setIsAddingNew(true);
    setNewBenefit({
      id: "",
      label: "",
      translations: { en: "", de: "", bs: "" },
      category: "accommodation",
      icon: "",
    });
    setError(null);
  };

  const handleCancelAdd = () => {
    setIsAddingNew(false);
    setError(null);
  };

  const handleCreateBenefit = async () => {
    // Validate
    if (!newBenefit.id || !newBenefit.id.trim()) {
      setError("Benefit ID is required");
      return;
    }

    if (!newBenefit.label || !newBenefit.category) {
      setError("Label and category are required");
      return;
    }

    // Check if at least one translation is provided
    const hasTranslation = Object.values(newBenefit.translations || {}).some(t => t && t.trim() !== "");
    if (!hasTranslation) {
      setError("At least one translation is required");
      return;
    }

    // Check if ID already exists
    if (benefits.some(b => b.id === newBenefit.id)) {
      setError("A benefit with this ID already exists");
      return;
    }

    try {
      const benefitData: Benefit = {
        id: newBenefit.id,
        label: newBenefit.label || "",
        translations: newBenefit.translations || { en: "", de: "", bs: "" },
        category: newBenefit.category || "accommodation",
        icon: newBenefit.icon || undefined,
      };

      await setDoc(doc(db, "benefits", newBenefit.id), {
        ...benefitData,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Update local state
      setBenefits([...benefits, benefitData]);
      setIsAddingNew(false);
      setSuccess("Benefit created successfully");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Error creating benefit:", error);
      setError("Failed to create benefit. Please try again.");
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Job Benefits Management</h1>
        <button
          onClick={handleAddNewBenefit}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
        >
          Add New Benefit
        </button>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 p-4 rounded-md mb-6">
          {success}
        </div>
      )}

      {/* Search */}
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <label htmlFor="search" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Search Benefits
            </label>
            <input
              type="text"
              id="search"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by ID, label, or category"
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
            />
          </div>
        </div>
      </div>

      {/* Add New Benefit Form */}
      {isAddingNew && (
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-lg font-medium mb-4">Add New Benefit</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                Benefit ID <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={newBenefit.id || ""}
                onChange={(e) => setNewBenefit({...newBenefit, id: e.target.value})}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                placeholder="e.g., accommodation-provided"
              />
              <p className="text-xs text-neutral-500 mt-1">
                Use lowercase letters, numbers, and hyphens only. This ID will be used in the database.
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                Category <span className="text-red-500">*</span>
              </label>
              <select
                value={newBenefit.category || "accommodation"}
                onChange={(e) => setNewBenefit({...newBenefit, category: e.target.value})}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              >
                {categories.map(category => (
                  <option key={category} value={category}>
                    {category.charAt(0).toUpperCase() + category.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Label (English) <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={newBenefit.label || ""}
              onChange={(e) => setNewBenefit({...newBenefit, label: e.target.value})}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              placeholder="e.g., Accommodation Provided"
            />
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Icon (Optional)
            </label>
            <input
              type="text"
              value={newBenefit.icon || ""}
              onChange={(e) => setNewBenefit({...newBenefit, icon: e.target.value})}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              placeholder="e.g., home, car, utensils (Font Awesome icon name)"
            />
            <p className="text-xs text-neutral-500 mt-1">
              Enter a Font Awesome icon name without the 'fa-' prefix.
            </p>
          </div>
          
          <h3 className="text-md font-medium mb-2">Translations</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {supportedLanguages.map(language => (
              <div key={language.code}>
                <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                  {language.name}
                </label>
                <input
                  type="text"
                  value={newBenefit.translations?.[language.code] || ""}
                  onChange={(e) => {
                    const updatedTranslations = { 
                      ...newBenefit.translations,
                      [language.code]: e.target.value 
                    };
                    setNewBenefit({...newBenefit, translations: updatedTranslations});
                  }}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                  placeholder={`${language.name} translation`}
                />
              </div>
            ))}
          </div>
          
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleCancelAdd}
              className="px-4 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-700"
            >
              Cancel
            </button>
            <button
              onClick={handleCreateBenefit}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
            >
              Create Benefit
            </button>
          </div>
        </div>
      )}

      {/* Benefits Table */}
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-neutral-200 dark:divide-neutral-700">
            <thead className="bg-neutral-50 dark:bg-neutral-900">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Label
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Category
                </th>
                {supportedLanguages.map(language => (
                  <th key={language.code} className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                    {language.name}
                  </th>
                ))}
                <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-neutral-800 divide-y divide-neutral-200 dark:divide-neutral-700">
              {filteredBenefits.map((benefit) => (
                <tr key={benefit.id}>
                  {editingBenefit && editingBenefit.id === benefit.id ? (
                    // Edit mode
                    <>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-neutral-900 dark:text-white">
                          {benefit.id}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="text"
                          value={editingBenefit.label}
                          onChange={(e) => setEditingBenefit({...editingBenefit, label: e.target.value})}
                          className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-sm"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <select
                          value={editingBenefit.category}
                          onChange={(e) => setEditingBenefit({...editingBenefit, category: e.target.value})}
                          className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-sm"
                        >
                          {categories.map(category => (
                            <option key={category} value={category}>
                              {category.charAt(0).toUpperCase() + category.slice(1)}
                            </option>
                          ))}
                        </select>
                      </td>
                      {supportedLanguages.map(language => (
                        <td key={language.code} className="px-6 py-4 whitespace-nowrap">
                          <input
                            type="text"
                            value={editingBenefit.translations[language.code] || ""}
                            onChange={(e) => {
                              const updatedTranslations = { 
                                ...editingBenefit.translations,
                                [language.code]: e.target.value 
                              };
                              setEditingBenefit({...editingBenefit, translations: updatedTranslations});
                            }}
                            className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-sm"
                          />
                        </td>
                      ))}
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={handleCancelEdit}
                          className="text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white mr-3"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={handleUpdateBenefit}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
                        >
                          Save
                        </button>
                      </td>
                    </>
                  ) : (
                    // View mode
                    <>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-neutral-900 dark:text-white">
                          {benefit.id}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-neutral-500 dark:text-neutral-400">
                          {benefit.label}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-neutral-500 dark:text-neutral-400">
                          {benefit.category.charAt(0).toUpperCase() + benefit.category.slice(1)}
                        </div>
                      </td>
                      {supportedLanguages.map(language => (
                        <td key={language.code} className="px-6 py-4 whitespace-nowrap">
                          <div className="text-sm text-neutral-500 dark:text-neutral-400">
                            {benefit.translations[language.code] || "-"}
                          </div>
                        </td>
                      ))}
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleEditBenefit(benefit)}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-3"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDeleteBenefit(benefit.id)}
                          className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                        >
                          Delete
                        </button>
                      </td>
                    </>
                  )}
                </tr>
              ))}
              {filteredBenefits.length === 0 && (
                <tr>
                  <td colSpan={5 + supportedLanguages.length} className="px-6 py-4 text-center text-sm text-neutral-500 dark:text-neutral-400">
                    No benefits found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
