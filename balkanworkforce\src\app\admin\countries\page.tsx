"use client";

import { useState, useEffect } from "react";
import { collection, query, getDocs, orderBy, doc, updateDoc, deleteDoc, setDoc } from "firebase/firestore";
import { db } from "@/lib/firebase";
import Image from "next/image";

interface Country {
  id: string;
  name: string;
  translations: {
    [key: string]: string;
  };
  flagUrl: string;
  code: string;
  region: string;
  enabled: boolean;
}

export default function AdminCountriesPage() {
  const [countries, setCountries] = useState<Country[]>([]);
  const [loading, setLoading] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [editingCountry, setEditingCountry] = useState<Country | null>(null);
  const [newCountry, setNewCountry] = useState<Partial<Country>>({
    id: "",
    name: "",
    translations: { en: "", de: "", bs: "" },
    flagUrl: "",
    code: "",
    region: "europe",
    enabled: true,
  });
  const [isAddingNew, setIsAddingNew] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState<string | null>(null);

  const supportedLanguages = [
    { code: "en", name: "English" },
    { code: "de", name: "German" },
    { code: "bs", name: "Bosnian" },
  ];

  const regions = [
    "europe",
    "balkans",
    "asia",
    "africa",
    "northAmerica",
    "southAmerica",
    "oceania",
    "other",
  ];

  useEffect(() => {
    const fetchCountries = async () => {
      try {
        const countriesQuery = query(
          collection(db, "countries"),
          orderBy("name", "asc")
        );
        const snapshot = await getDocs(countriesQuery);
        const countriesData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
        })) as Country[];
        setCountries(countriesData);
      } catch (error) {
        console.error("Error fetching countries:", error);
        setError("Failed to load countries. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchCountries();
  }, []);

  const filteredCountries = countries.filter(country => {
    const searchLower = searchTerm.toLowerCase();
    return (
      country.id.toLowerCase().includes(searchLower) ||
      country.name.toLowerCase().includes(searchLower) ||
      country.code.toLowerCase().includes(searchLower) ||
      country.region.toLowerCase().includes(searchLower) ||
      Object.values(country.translations).some(translation => 
        translation.toLowerCase().includes(searchLower)
      )
    );
  });

  const handleEditCountry = (country: Country) => {
    setEditingCountry(country);
    setError(null);
  };

  const handleCancelEdit = () => {
    setEditingCountry(null);
    setError(null);
  };

  const handleUpdateCountry = async () => {
    if (!editingCountry) return;

    // Validate
    if (!editingCountry.name || !editingCountry.code || !editingCountry.flagUrl) {
      setError("Name, code, and flag URL are required");
      return;
    }

    // Check if at least one translation is provided
    const hasTranslation = Object.values(editingCountry.translations).some(t => t.trim() !== "");
    if (!hasTranslation) {
      setError("At least one translation is required");
      return;
    }

    try {
      await updateDoc(doc(db, "countries", editingCountry.id), {
        name: editingCountry.name,
        translations: editingCountry.translations,
        flagUrl: editingCountry.flagUrl,
        code: editingCountry.code,
        region: editingCountry.region,
        enabled: editingCountry.enabled,
        updatedAt: new Date(),
      });

      // Update local state
      setCountries(countries.map(c => 
        c.id === editingCountry.id ? editingCountry : c
      ));
      
      setEditingCountry(null);
      setSuccess("Country updated successfully");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Error updating country:", error);
      setError("Failed to update country. Please try again.");
    }
  };

  const handleDeleteCountry = async (countryId: string) => {
    if (!confirm("Are you sure you want to delete this country? This action cannot be undone.")) {
      return;
    }

    try {
      await deleteDoc(doc(db, "countries", countryId));
      
      // Update local state
      setCountries(countries.filter(c => c.id !== countryId));
      setSuccess("Country deleted successfully");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Error deleting country:", error);
      setError("Failed to delete country. Please try again.");
    }
  };

  const handleAddNewCountry = () => {
    setIsAddingNew(true);
    setNewCountry({
      id: "",
      name: "",
      translations: { en: "", de: "", bs: "" },
      flagUrl: "",
      code: "",
      region: "europe",
      enabled: true,
    });
    setError(null);
  };

  const handleCancelAdd = () => {
    setIsAddingNew(false);
    setError(null);
  };

  const handleCreateCountry = async () => {
    // Validate
    if (!newCountry.id || !newCountry.id.trim()) {
      setError("Country ID is required");
      return;
    }

    if (!newCountry.name || !newCountry.code || !newCountry.flagUrl) {
      setError("Name, code, and flag URL are required");
      return;
    }

    // Check if at least one translation is provided
    const hasTranslation = Object.values(newCountry.translations || {}).some(t => t && t.trim() !== "");
    if (!hasTranslation) {
      setError("At least one translation is required");
      return;
    }

    // Check if ID already exists
    if (countries.some(c => c.id === newCountry.id)) {
      setError("A country with this ID already exists");
      return;
    }

    try {
      const countryData: Country = {
        id: newCountry.id,
        name: newCountry.name || "",
        translations: newCountry.translations || { en: "", de: "", bs: "" },
        flagUrl: newCountry.flagUrl || "",
        code: newCountry.code || "",
        region: newCountry.region || "europe",
        enabled: newCountry.enabled !== undefined ? newCountry.enabled : true,
      };

      await setDoc(doc(db, "countries", newCountry.id), {
        ...countryData,
        createdAt: new Date(),
        updatedAt: new Date(),
      });

      // Update local state
      setCountries([...countries, countryData]);
      setIsAddingNew(false);
      setSuccess("Country created successfully");
      setTimeout(() => setSuccess(null), 3000);
    } catch (error) {
      console.error("Error creating country:", error);
      setError("Failed to create country. Please try again.");
    }
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center h-full">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold">Countries Management</h1>
        <button
          onClick={handleAddNewCountry}
          className="bg-blue-600 hover:bg-blue-700 text-white px-4 py-2 rounded-md"
        >
          Add New Country
        </button>
      </div>

      {error && (
        <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6">
          {error}
        </div>
      )}

      {success && (
        <div className="bg-green-50 dark:bg-green-900/20 text-green-600 dark:text-green-400 p-4 rounded-md mb-6">
          {success}
        </div>
      )}

      {/* Search */}
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-4 mb-6">
        <div className="flex flex-col md:flex-row gap-4">
          <div className="flex-1">
            <label htmlFor="search" className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Search Countries
            </label>
            <input
              type="text"
              id="search"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search by name, code, or region"
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
            />
          </div>
        </div>
      </div>

      {/* Add New Country Form */}
      {isAddingNew && (
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm p-6 mb-6">
          <h2 className="text-lg font-medium mb-4">Add New Country</h2>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                Country ID <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={newCountry.id || ""}
                onChange={(e) => setNewCountry({...newCountry, id: e.target.value})}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                placeholder="e.g., de, hr, ba"
              />
              <p className="text-xs text-neutral-500 mt-1">
                Use lowercase letters only, preferably the country's ISO code. This ID will be used in the database.
              </p>
            </div>
            
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                Country Code <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={newCountry.code || ""}
                onChange={(e) => setNewCountry({...newCountry, code: e.target.value})}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                placeholder="e.g., DE, HR, BA"
              />
              <p className="text-xs text-neutral-500 mt-1">
                Use uppercase letters for the country's ISO code.
              </p>
            </div>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                Name (English) <span className="text-red-500">*</span>
              </label>
              <input
                type="text"
                value={newCountry.name || ""}
                onChange={(e) => setNewCountry({...newCountry, name: e.target.value})}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                placeholder="e.g., Germany, Croatia"
              />
            </div>
            
            <div>
              <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                Region <span className="text-red-500">*</span>
              </label>
              <select
                value={newCountry.region || "europe"}
                onChange={(e) => setNewCountry({...newCountry, region: e.target.value})}
                className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              >
                {regions.map(region => (
                  <option key={region} value={region}>
                    {region.charAt(0).toUpperCase() + region.slice(1)}
                  </option>
                ))}
              </select>
            </div>
          </div>
          
          <div className="mb-4">
            <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
              Flag URL <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={newCountry.flagUrl || ""}
              onChange={(e) => setNewCountry({...newCountry, flagUrl: e.target.value})}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              placeholder="e.g., https://example.com/flags/germany.png"
            />
            <p className="text-xs text-neutral-500 mt-1">
              Enter a URL to the country's flag image.
            </p>
          </div>
          
          <div className="mb-4">
            <div className="flex items-center">
              <input
                type="checkbox"
                id="enabled"
                checked={newCountry.enabled !== undefined ? newCountry.enabled : true}
                onChange={(e) => setNewCountry({...newCountry, enabled: e.target.checked})}
                className="mr-2"
              />
              <label htmlFor="enabled" className="text-sm font-medium text-neutral-700 dark:text-neutral-300">
                Enabled
              </label>
            </div>
            <p className="text-xs text-neutral-500 mt-1">
              If enabled, this country will be available for selection in forms.
            </p>
          </div>
          
          <h3 className="text-md font-medium mb-2">Translations</h3>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
            {supportedLanguages.map(language => (
              <div key={language.code}>
                <label className="block text-sm font-medium text-neutral-700 dark:text-neutral-300 mb-1">
                  {language.name}
                </label>
                <input
                  type="text"
                  value={newCountry.translations?.[language.code] || ""}
                  onChange={(e) => {
                    const updatedTranslations = { 
                      ...newCountry.translations,
                      [language.code]: e.target.value 
                    };
                    setNewCountry({...newCountry, translations: updatedTranslations});
                  }}
                  className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
                  placeholder={`${language.name} translation`}
                />
              </div>
            ))}
          </div>
          
          <div className="flex justify-end space-x-3">
            <button
              onClick={handleCancelAdd}
              className="px-4 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-neutral-700 dark:text-neutral-300 hover:bg-neutral-50 dark:hover:bg-neutral-700"
            >
              Cancel
            </button>
            <button
              onClick={handleCreateCountry}
              className="px-4 py-2 bg-blue-600 hover:bg-blue-700 text-white rounded-md"
            >
              Create Country
            </button>
          </div>
        </div>
      )}

      {/* Countries Table */}
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-neutral-200 dark:divide-neutral-700">
            <thead className="bg-neutral-50 dark:bg-neutral-900">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Flag
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Code
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Region
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-right text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Actions
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-neutral-800 divide-y divide-neutral-200 dark:divide-neutral-700">
              {filteredCountries.map((country) => (
                <tr key={country.id}>
                  {editingCountry && editingCountry.id === country.id ? (
                    // Edit mode
                    <>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="w-8 h-6 overflow-hidden rounded-sm">
                          <Image
                            src={editingCountry.flagUrl}
                            alt={editingCountry.name}
                            width={32}
                            height={24}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-neutral-900 dark:text-white">
                          {country.id}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="text"
                          value={editingCountry.name}
                          onChange={(e) => setEditingCountry({...editingCountry, name: e.target.value})}
                          className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-sm"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <input
                          type="text"
                          value={editingCountry.code}
                          onChange={(e) => setEditingCountry({...editingCountry, code: e.target.value})}
                          className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-sm"
                        />
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <select
                          value={editingCountry.region}
                          onChange={(e) => setEditingCountry({...editingCountry, region: e.target.value})}
                          className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-sm"
                        >
                          {regions.map(region => (
                            <option key={region} value={region}>
                              {region.charAt(0).toUpperCase() + region.slice(1)}
                            </option>
                          ))}
                        </select>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="flex items-center">
                          <input
                            type="checkbox"
                            checked={editingCountry.enabled}
                            onChange={(e) => setEditingCountry({...editingCountry, enabled: e.target.checked})}
                            className="mr-2"
                          />
                          <span className="text-sm">Enabled</span>
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={handleCancelEdit}
                          className="text-neutral-600 dark:text-neutral-400 hover:text-neutral-900 dark:hover:text-white mr-3"
                        >
                          Cancel
                        </button>
                        <button
                          onClick={handleUpdateCountry}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300"
                        >
                          Save
                        </button>
                      </td>
                    </>
                  ) : (
                    // View mode
                    <>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="w-8 h-6 overflow-hidden rounded-sm">
                          <Image
                            src={country.flagUrl}
                            alt={country.name}
                            width={32}
                            height={24}
                            className="w-full h-full object-cover"
                          />
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm font-medium text-neutral-900 dark:text-white">
                          {country.id}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-neutral-500 dark:text-neutral-400">
                          {country.name}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-neutral-500 dark:text-neutral-400">
                          {country.code}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <div className="text-sm text-neutral-500 dark:text-neutral-400">
                          {country.region.charAt(0).toUpperCase() + country.region.slice(1)}
                        </div>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap">
                        <span className={`px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                          country.enabled 
                            ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400" 
                            : "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400"
                        }`}>
                          {country.enabled ? "Enabled" : "Disabled"}
                        </span>
                      </td>
                      <td className="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                        <button
                          onClick={() => handleEditCountry(country)}
                          className="text-blue-600 dark:text-blue-400 hover:text-blue-900 dark:hover:text-blue-300 mr-3"
                        >
                          Edit
                        </button>
                        <button
                          onClick={() => handleDeleteCountry(country.id)}
                          className="text-red-600 dark:text-red-400 hover:text-red-900 dark:hover:text-red-300"
                        >
                          Delete
                        </button>
                      </td>
                    </>
                  )}
                </tr>
              ))}
              {filteredCountries.length === 0 && (
                <tr>
                  <td colSpan={7} className="px-6 py-4 text-center text-sm text-neutral-500 dark:text-neutral-400">
                    No countries found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
