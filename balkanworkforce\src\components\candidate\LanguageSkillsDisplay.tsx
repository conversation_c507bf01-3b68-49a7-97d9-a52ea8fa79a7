import { LanguageLevel, LanguageSkill } from "@/types";

interface LanguageSkillsDisplayProps {
  germanLevel: LanguageLevel;
  englishLevel: LanguageLevel;
  otherLanguages?: LanguageSkill[];
}

export default function LanguageSkillsDisplay({
  germanLevel,
  englishLevel,
  otherLanguages = [],
}: LanguageSkillsDisplayProps) {
  return (
    <div className="space-y-4">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg p-4">
          <div className="flex justify-between items-center mb-2">
            <h4 className="font-medium">German</h4>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300">
              {germanLevel}
            </span>
          </div>
          <div className="w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2.5">
            <div
              className="bg-blue-600 h-2.5 rounded-full"
              style={{ width: `${getLanguageLevelPercentage(germanLevel)}%` }}
            ></div>
          </div>
          <div className="mt-1 text-xs text-neutral-500 dark:text-neutral-400">
            {getLanguageLevelDescription(germanLevel)}
          </div>
        </div>

        <div className="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg p-4">
          <div className="flex justify-between items-center mb-2">
            <h4 className="font-medium">English</h4>
            <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300">
              {englishLevel}
            </span>
          </div>
          <div className="w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2.5">
            <div
              className="bg-green-600 h-2.5 rounded-full"
              style={{ width: `${getLanguageLevelPercentage(englishLevel)}%` }}
            ></div>
          </div>
          <div className="mt-1 text-xs text-neutral-500 dark:text-neutral-400">
            {getLanguageLevelDescription(englishLevel)}
          </div>
        </div>
      </div>

      {otherLanguages.length > 0 && (
        <div className="mt-4">
          <h4 className="font-medium mb-3">Other Languages</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {otherLanguages.map((language, index) => (
              <div
                key={index}
                className="bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg p-4"
              >
                <div className="flex justify-between items-center mb-2">
                  <h4 className="font-medium">{language.language}</h4>
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300">
                    {language.level}
                  </span>
                </div>
                <div className="w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2.5">
                  <div
                    className="bg-purple-600 h-2.5 rounded-full"
                    style={{ width: `${getLanguageLevelPercentage(language.level)}%` }}
                  ></div>
                </div>
                <div className="mt-1 text-xs text-neutral-500 dark:text-neutral-400">
                  {getLanguageLevelDescription(language.level)}
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}

function getLanguageLevelPercentage(level: LanguageLevel): number {
  switch (level) {
    case "A1":
      return 16.7;
    case "A2":
      return 33.3;
    case "B1":
      return 50;
    case "B2":
      return 66.7;
    case "C1":
      return 83.3;
    case "C2":
    case "Native":
      return 100;
    default:
      return 0;
  }
}

function getLanguageLevelDescription(level: LanguageLevel): string {
  switch (level) {
    case "A1":
      return "Beginner - Can understand and use familiar everyday expressions";
    case "A2":
      return "Elementary - Can communicate in simple and routine tasks";
    case "B1":
      return "Intermediate - Can deal with most situations likely to arise while traveling";
    case "B2":
      return "Upper Intermediate - Can interact with a degree of fluency and spontaneity";
    case "C1":
      return "Advanced - Can express ideas fluently and spontaneously without much searching for expressions";
    case "C2":
      return "Proficient - Can understand with ease virtually everything heard or read";
    case "Native":
      return "Native Speaker - Mother tongue proficiency";
    default:
      return "";
  }
}
