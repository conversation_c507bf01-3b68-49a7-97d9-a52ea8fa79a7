import { useState, useEffect } from "react";
import { collection, query, getDocs, orderBy, limit, where, addDoc, serverTimestamp } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { useAuth } from "@/contexts/AuthContext";

interface ActivityLogEntry {
  id: string;
  adminId: string;
  adminEmail: string;
  action: string;
  entityType: string;
  entityId?: string;
  details?: string;
  timestamp: Date;
}

export const logAdminActivity = async (
  adminId: string,
  adminEmail: string,
  action: string,
  entityType: string,
  entityId?: string,
  details?: string
) => {
  try {
    await addDoc(collection(db, "adminActivityLog"), {
      adminId,
      adminEmail,
      action,
      entityType,
      entityId,
      details,
      timestamp: serverTimestamp(),
    });
  } catch (error) {
    console.error("Error logging admin activity:", error);
  }
};

export default function AdminActivityLog() {
  const { user } = useAuth();
  const [activityLogs, setActivityLogs] = useState<ActivityLogEntry[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [filter, setFilter] = useState<"all" | "my" | string>("all");
  const [actionFilter, setActionFilter] = useState<string>("all");
  const [entityFilter, setEntityFilter] = useState<string>("all");

  useEffect(() => {
    const fetchActivityLogs = async () => {
      try {
        setLoading(true);
        
        let activityQuery;
        
        if (filter === "my" && user) {
          activityQuery = query(
            collection(db, "adminActivityLog"),
            where("adminId", "==", user.uid),
            orderBy("timestamp", "desc"),
            limit(100)
          );
        } else if (filter !== "all" && filter !== "my") {
          activityQuery = query(
            collection(db, "adminActivityLog"),
            where("adminId", "==", filter),
            orderBy("timestamp", "desc"),
            limit(100)
          );
        } else {
          activityQuery = query(
            collection(db, "adminActivityLog"),
            orderBy("timestamp", "desc"),
            limit(100)
          );
        }
        
        const snapshot = await getDocs(activityQuery);
        const logsData = snapshot.docs.map(doc => ({
          id: doc.id,
          ...doc.data(),
          timestamp: doc.data().timestamp?.toDate() || new Date(),
        })) as ActivityLogEntry[];
        
        // Apply additional filters
        let filteredLogs = logsData;
        
        if (actionFilter !== "all") {
          filteredLogs = filteredLogs.filter(log => log.action === actionFilter);
        }
        
        if (entityFilter !== "all") {
          filteredLogs = filteredLogs.filter(log => log.entityType === entityFilter);
        }
        
        setActivityLogs(filteredLogs);
      } catch (error) {
        console.error("Error fetching activity logs:", error);
        setError("Failed to load activity logs. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    fetchActivityLogs();
  }, [user, filter, actionFilter, entityFilter]);

  // Extract unique actions and entity types for filters
  const uniqueActions = Array.from(new Set(activityLogs.map(log => log.action)));
  const uniqueEntityTypes = Array.from(new Set(activityLogs.map(log => log.entityType)));

  if (loading) {
    return (
      <div className="flex justify-center items-center h-64">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md">
        {error}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
        <h2 className="text-xl font-bold">Admin Activity Log</h2>
        
        <div className="flex flex-wrap gap-2">
          <select
            value={filter}
            onChange={(e) => setFilter(e.target.value)}
            className="px-3 py-1 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md bg-white dark:bg-neutral-800"
          >
            <option value="all">All Admins</option>
            <option value="my">My Activity</option>
          </select>
          
          <select
            value={actionFilter}
            onChange={(e) => setActionFilter(e.target.value)}
            className="px-3 py-1 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md bg-white dark:bg-neutral-800"
          >
            <option value="all">All Actions</option>
            {uniqueActions.map(action => (
              <option key={action} value={action}>{action}</option>
            ))}
          </select>
          
          <select
            value={entityFilter}
            onChange={(e) => setEntityFilter(e.target.value)}
            className="px-3 py-1 text-sm border border-neutral-300 dark:border-neutral-700 rounded-md bg-white dark:bg-neutral-800"
          >
            <option value="all">All Entities</option>
            {uniqueEntityTypes.map(entityType => (
              <option key={entityType} value={entityType}>{entityType}</option>
            ))}
          </select>
        </div>
      </div>

      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-neutral-200 dark:divide-neutral-700">
            <thead className="bg-neutral-50 dark:bg-neutral-900">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Timestamp
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Admin
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Action
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Entity
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-neutral-500 dark:text-neutral-400 uppercase tracking-wider">
                  Details
                </th>
              </tr>
            </thead>
            <tbody className="bg-white dark:bg-neutral-800 divide-y divide-neutral-200 dark:divide-neutral-700">
              {activityLogs.map((log) => (
                <tr key={log.id}>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-neutral-500 dark:text-neutral-400">
                      {log.timestamp.toLocaleString()}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm">
                      {log.adminEmail}
                      {user && log.adminId === user.uid && (
                        <span className="ml-1 text-xs text-blue-600 dark:text-blue-400">(you)</span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={`px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full ${
                      log.action === "create" ? "bg-green-100 text-green-800 dark:bg-green-900/20 dark:text-green-400" :
                      log.action === "update" ? "bg-blue-100 text-blue-800 dark:bg-blue-900/20 dark:text-blue-400" :
                      log.action === "delete" ? "bg-red-100 text-red-800 dark:bg-red-900/20 dark:text-red-400" :
                      log.action === "login" ? "bg-purple-100 text-purple-800 dark:bg-purple-900/20 dark:text-purple-400" :
                      "bg-neutral-100 text-neutral-800 dark:bg-neutral-900/20 dark:text-neutral-400"
                    }`}>
                      {log.action}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm">
                      <span className="capitalize">{log.entityType}</span>
                      {log.entityId && (
                        <span className="text-xs text-neutral-500 dark:text-neutral-400 ml-1">
                          ({log.entityId})
                        </span>
                      )}
                    </div>
                  </td>
                  <td className="px-6 py-4">
                    <div className="text-sm text-neutral-500 dark:text-neutral-400">
                      {log.details || "-"}
                    </div>
                  </td>
                </tr>
              ))}
              {activityLogs.length === 0 && (
                <tr>
                  <td colSpan={5} className="px-6 py-4 text-center text-sm text-neutral-500 dark:text-neutral-400">
                    No activity logs found
                  </td>
                </tr>
              )}
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
}
