"use client";

import { useState, useEffect } from "react";
import { usePara<PERSON>, useRouter } from "next/navigation";
import { useAuth } from "@/contexts/AuthContext";
import { doc, getDoc, updateDoc, collection, query, where, getDocs } from "firebase/firestore";
import { db } from "@/lib/firebase";
import Link from "next/link";
import { JobApplication, Candidate } from "@/types";
import { jobBenefits } from "@/data/jobBenefits";

export default function ApplicationDetailPage() {
  const { id } = useParams();
  const router = useRouter();
  const { user, loading: authLoading } = useAuth();
  const [application, setApplication] = useState<any>(null);
  const [candidate, setCandidate] = useState<any>(null);
  const [job, setJob] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [updating, setUpdating] = useState(false);
  const [notes, setNotes] = useState("");
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    // If not authenticated or not an employer, redirect to auth page
    if (!authLoading && (!user || user.role !== "employer")) {
      router.push("/auth");
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    const fetchData = async () => {
      if (!user || !id || typeof id !== "string") return;
      
      try {
        // Fetch application data
        const applicationDoc = await getDoc(doc(db, "applications", id));
        
        if (!applicationDoc.exists()) {
          setError("Application not found");
          return;
        }
        
        const applicationData = applicationDoc.data();
        
        // Verify that this application belongs to the current employer
        if (applicationData.employerId !== user.uid) {
          setError("You don't have permission to view this application");
          return;
        }
        
        const applicationWithId = {
          id: applicationDoc.id,
          ...applicationData,
          createdAt: applicationData.createdAt.toDate(),
          updatedAt: applicationData.updatedAt.toDate(),
          lastContactDate: applicationData.lastContactDate ? applicationData.lastContactDate.toDate() : null,
        };
        
        setApplication(applicationWithId);
        setNotes(applicationWithId.notes || "");
        
        // Fetch job data
        const jobDoc = await getDoc(doc(db, "jobs", applicationData.jobId));
        
        if (jobDoc.exists()) {
          const jobData = jobDoc.data();
          setJob({
            id: jobDoc.id,
            ...jobData,
            createdAt: jobData.createdAt.toDate(),
            updatedAt: jobData.updatedAt.toDate(),
            premiumUntil: jobData.premiumUntil ? jobData.premiumUntil.toDate() : undefined,
          });
        }
        
        // Fetch candidate data
        const candidateDoc = await getDoc(doc(db, "candidates", applicationData.candidateId));
        
        if (candidateDoc.exists()) {
          const candidateData = candidateDoc.data();
          setCandidate({
            id: candidateDoc.id,
            ...candidateData,
            createdAt: candidateData.createdAt?.toDate(),
            updatedAt: candidateData.updatedAt?.toDate(),
          });
        }
      } catch (error) {
        console.error("Error fetching data:", error);
        setError("Failed to load application details. Please try again.");
      } finally {
        setLoading(false);
      }
    };

    if (user && user.role === "employer") {
      fetchData();
    }
  }, [user, id, router]);

  const handleStatusChange = async (newStatus: string) => {
    if (!user || !application) return;
    
    setUpdating(true);
    
    try {
      const applicationRef = doc(db, "applications", application.id);
      
      await updateDoc(applicationRef, {
        status: newStatus,
        updatedAt: new Date(),
        notes: notes,
        ...(newStatus === "contacted" ? { lastContactDate: new Date() } : {})
      });
      
      // Update local state
      setApplication(prev => ({
        ...prev,
        status: newStatus,
        updatedAt: new Date(),
        notes: notes,
        ...(newStatus === "contacted" ? { lastContactDate: new Date() } : {})
      }));
      
      alert("Application status updated successfully");
    } catch (error) {
      console.error("Error updating application status:", error);
      alert("Failed to update application status. Please try again.");
    } finally {
      setUpdating(false);
    }
  };

  const handleSaveNotes = async () => {
    if (!user || !application) return;
    
    setUpdating(true);
    
    try {
      const applicationRef = doc(db, "applications", application.id);
      
      await updateDoc(applicationRef, {
        notes: notes,
        updatedAt: new Date()
      });
      
      // Update local state
      setApplication(prev => ({
        ...prev,
        notes: notes,
        updatedAt: new Date()
      }));
      
      alert("Notes saved successfully");
    } catch (error) {
      console.error("Error saving notes:", error);
      alert("Failed to save notes. Please try again.");
    } finally {
      setUpdating(false);
    }
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "pending":
        return (
          <span className="bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 text-xs px-2 py-1 rounded-full">
            Pending
          </span>
        );
      case "reviewed":
        return (
          <span className="bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300 text-xs px-2 py-1 rounded-full">
            Reviewed
          </span>
        );
      case "contacted":
        return (
          <span className="bg-yellow-100 dark:bg-yellow-900/20 text-yellow-800 dark:text-yellow-300 text-xs px-2 py-1 rounded-full">
            Contacted
          </span>
        );
      case "rejected":
        return (
          <span className="bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300 text-xs px-2 py-1 rounded-full">
            Rejected
          </span>
        );
      case "hired":
        return (
          <span className="bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300 text-xs px-2 py-1 rounded-full">
            Hired
          </span>
        );
      default:
        return (
          <span className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 text-xs px-2 py-1 rounded-full">
            {status}
          </span>
        );
    }
  };

  if (authLoading || loading) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="max-w-4xl mx-auto py-12">
        <div className="bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6">
          {error}
        </div>
        <Link
          href="/employer/applications"
          className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
        >
          Back to Applications
        </Link>
      </div>
    );
  }

  if (!application || !candidate || !job) {
    return (
      <div className="max-w-4xl mx-auto py-12">
        <div className="text-center">
          <h2 className="text-2xl font-bold mb-4">Application Not Found</h2>
          <p className="text-neutral-600 dark:text-neutral-400 mb-6">
            The application you're looking for doesn't exist or you don't have permission to view it.
          </p>
          <Link
            href="/employer/applications"
            className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
          >
            Back to Applications
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="max-w-4xl mx-auto py-12">
      <div className="flex justify-between items-center mb-8">
        <h1 className="text-3xl font-bold">Application Details</h1>
        <Link
          href="/employer/applications"
          className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
        >
          Back to Applications
        </Link>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6">
          <h2 className="text-lg font-semibold mb-4">Application Status</h2>
          <div className="mb-4">
            <div className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">Current Status</div>
            <div className="flex items-center">
              {getStatusBadge(application.status)}
            </div>
          </div>
          <div className="mb-4">
            <div className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">Applied On</div>
            <div>{application.createdAt.toLocaleDateString()}</div>
          </div>
          <div className="mb-4">
            <div className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">Last Updated</div>
            <div>{application.updatedAt.toLocaleDateString()}</div>
          </div>
          {application.lastContactDate && (
            <div className="mb-4">
              <div className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">Last Contacted</div>
              <div>{application.lastContactDate.toLocaleDateString()}</div>
            </div>
          )}
          {application.matchPercentage && (
            <div className="mb-4">
              <div className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">Match Percentage</div>
              <div className="flex items-center">
                <div className="w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2 mr-2 max-w-[100px]">
                  <div
                    className={`h-2 rounded-full ${
                      application.matchPercentage >= 80 ? "bg-green-500" :
                      application.matchPercentage >= 50 ? "bg-yellow-500" :
                      "bg-red-500"
                    }`}
                    style={{ width: `${application.matchPercentage}%` }}
                  ></div>
                </div>
                <span>{application.matchPercentage}%</span>
              </div>
            </div>
          )}
          <div className="mt-6">
            <label htmlFor="status" className="block text-sm font-medium mb-2">
              Update Status
            </label>
            <select
              id="status"
              value={application.status}
              onChange={(e) => handleStatusChange(e.target.value)}
              disabled={updating}
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md bg-white dark:bg-neutral-800 mb-4"
            >
              <option value="pending">Pending</option>
              <option value="reviewed">Reviewed</option>
              <option value="contacted">Contacted</option>
              <option value="rejected">Rejected</option>
              <option value="hired">Hired</option>
            </select>
          </div>
        </div>
        
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6 md:col-span-2">
          <h2 className="text-lg font-semibold mb-4">Candidate Information</h2>
          <div className="mb-4">
            <div className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">Candidate</div>
            <div className="font-medium">{candidate.profession}, {candidate.age}</div>
          </div>
          
          {candidate.skills && candidate.skills.length > 0 && (
            <div className="mb-4">
              <div className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">Skills</div>
              <div className="flex flex-wrap gap-2">
                {candidate.skills.map((skill: string, index: number) => (
                  <span
                    key={index}
                    className="bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 text-xs px-2 py-1 rounded"
                  >
                    {skill}
                  </span>
                ))}
              </div>
            </div>
          )}
          
          {candidate.germanLanguageLevel && (
            <div className="mb-4">
              <div className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">German Language Level</div>
              <div>{candidate.germanLanguageLevel}</div>
            </div>
          )}
          
          {candidate.englishLanguageLevel && (
            <div className="mb-4">
              <div className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">English Language Level</div>
              <div>{candidate.englishLanguageLevel}</div>
            </div>
          )}
          
          {candidate.benefits && candidate.benefits.length > 0 && (
            <div className="mb-4">
              <div className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">Job Preferences</div>
              <div className="flex flex-wrap gap-2">
                {candidate.benefits.map((benefitId: string) => {
                  const benefit = jobBenefits.find(b => b.id === benefitId);
                  return (
                    <span
                      key={benefitId}
                      className="bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 text-xs px-2 py-1 rounded-full flex items-center"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                        <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                      </svg>
                      {benefit ? benefit.label : benefitId}
                    </span>
                  );
                })}
              </div>
            </div>
          )}
          
          <Link
            href={`/employer/candidates/${candidate.id}`}
            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          >
            View Full Candidate Profile
          </Link>
        </div>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6">
          <h2 className="text-lg font-semibold mb-4">Job Details</h2>
          <div className="mb-4">
            <div className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">Job Title</div>
            <div className="font-medium">{job.title}</div>
          </div>
          <div className="mb-4">
            <div className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">Location</div>
            <div>{job.location}</div>
          </div>
          <div className="mb-4">
            <div className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">Employment Type</div>
            <div>{job.employmentType.replace("-", " ")}</div>
          </div>
          {job.salary && (
            <div className="mb-4">
              <div className="text-sm text-neutral-500 dark:text-neutral-400 mb-1">Salary</div>
              <div>{job.salary.min} - {job.salary.max} {job.salary.currency}</div>
            </div>
          )}
          <Link
            href={`/employer/jobs/${job.id}`}
            className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300"
          >
            View Full Job Details
          </Link>
        </div>
        
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6">
          <h2 className="text-lg font-semibold mb-4">Cover Letter</h2>
          <div className="whitespace-pre-line text-neutral-600 dark:text-neutral-400">
            {application.coverLetter || "No cover letter provided"}
          </div>
        </div>
      </div>
      
      <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6">
        <h2 className="text-lg font-semibold mb-4">Notes</h2>
        <textarea
          value={notes}
          onChange={(e) => setNotes(e.target.value)}
          rows={4}
          className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md mb-4"
          placeholder="Add private notes about this candidate..."
        ></textarea>
        <div className="flex justify-end">
          <button
            onClick={handleSaveNotes}
            disabled={updating}
            className={`px-4 py-2 rounded-md ${
              updating
                ? "bg-neutral-700 text-white cursor-wait"
                : "bg-neutral-900 hover:bg-neutral-800 text-white"
            }`}
          >
            {updating ? "Saving..." : "Save Notes"}
          </button>
        </div>
      </div>
    </div>
  );
}
