"use client";

import { useState, useEffect } from "react";
import { useAuth } from "@/contexts/AuthContext";
import { useRouter } from "next/navigation";
import Link from "next/link";
import { collection, query, where, getDocs, orderBy } from "firebase/firestore";
import { db } from "@/lib/firebase";
import { JobApplication, Candidate } from "@/types";
import { jobBenefits } from "@/data/jobBenefits";
import { isCandidateProfileComplete, hasMinimumProfileInfo } from "@/utils/profileUtils";
import { countries } from "@/data/countries";
import InitialProfileSetup from "@/components/candidate/InitialProfileSetup";

export default function CandidateDashboard() {
  const { user, loading: authLoading } = useAuth();
  const router = useRouter();
  const [applications, setApplications] = useState<any[]>([]);
  const [loading, setLoading] = useState(true);
  const [profileComplete, setProfileComplete] = useState(false);
  const [hasMinimumProfile, setHasMinimumProfile] = useState(false);
  const [candidateData, setCandidateData] = useState<any>(null);

  useEffect(() => {
    // If not authenticated or not a candidate, redirect to auth page
    if (!authLoading && (!user || user.role !== "candidate")) {
      router.push("/auth");
    }
  }, [user, authLoading, router]);

  useEffect(() => {
    const fetchCandidateData = async () => {
      if (!user) return;

      try {
        // Check if profile is complete
        const candidateDoc = await getDocs(
          query(collection(db, "candidates"), where("uid", "==", user.uid))
        );

        if (!candidateDoc.empty) {
          const data = candidateDoc.docs[0].data() as Candidate;
          setCandidateData(data);
          setHasMinimumProfile(hasMinimumProfileInfo(data));
          setProfileComplete(isCandidateProfileComplete(data));
        }

        // Fetch applications
        const applicationsCollection = collection(db, "applications");
        const applicationsQuery = query(
          applicationsCollection,
          where("candidateId", "==", user.uid),
          orderBy("createdAt", "desc")
        );

        const querySnapshot = await getDocs(applicationsQuery);
        const applicationsList: any[] = [];

        querySnapshot.forEach((doc) => {
          const data = doc.data();
          applicationsList.push({
            id: doc.id,
            ...data,
            createdAt: data.createdAt.toDate(),
            updatedAt: data.updatedAt.toDate(),
          });
        });

        setApplications(applicationsList);
      } catch (error) {
        console.error("Error fetching candidate data:", error);
      } finally {
        setLoading(false);
      }
    };

    if (user && user.role === "candidate") {
      fetchCandidateData();
    }
  }, [user]);

  if (authLoading || (user && user.role !== "candidate")) {
    return (
      <div className="flex justify-center items-center min-h-[60vh]">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"></div>
      </div>
    );
  }

  // If the user doesn't have the minimum profile info (profession and age)
  if (!loading && !hasMinimumProfile) {
    return (
      <div className="max-w-4xl mx-auto py-12">
        <h1 className="text-3xl font-bold mb-8 text-center">Welcome to BalkanWorkForce</h1>
        <p className="text-center text-neutral-600 dark:text-neutral-400 mb-8">
          Please complete your basic profile information to get started.
        </p>
        <InitialProfileSetup
          onComplete={() => {
            setHasMinimumProfile(true);
            window.location.reload(); // Reload to fetch updated profile data
          }}
        />
      </div>
    );
  }

  return (
    <div className="max-w-6xl mx-auto py-12">
      <h1 className="text-3xl font-bold mb-8">Candidate Dashboard</h1>

      {!profileComplete && (
        <div className="bg-amber-50 dark:bg-amber-900/20 border border-amber-200 dark:border-amber-800 rounded-lg p-6 mb-8">
          <h3 className="text-lg font-medium text-amber-800 dark:text-amber-400 mb-2">
            Complete Your Profile
          </h3>
          <p className="text-amber-700 dark:text-amber-500 mb-4">
            Your profile is incomplete. Complete your profile to increase your chances of getting hired.
          </p>
          <Link
            href="/candidate/profile"
            className="bg-amber-600 hover:bg-amber-700 text-white px-4 py-2 rounded-md inline-block"
          >
            Complete Profile
          </Link>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-10">
        {/* Stats Cards */}
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6">
          <h3 className="text-lg font-medium mb-2">Applications</h3>
          <p className="text-3xl font-bold">{applications.length}</p>
          <div className="mt-4">
            <Link
              href="/candidate/applications"
              className="text-blue-600 hover:text-blue-800 dark:text-blue-400 dark:hover:text-blue-300 text-sm"
            >
              View All Applications
            </Link>
          </div>
        </div>
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6">
          <h3 className="text-lg font-medium mb-2">Profile Views</h3>
          <p className="text-3xl font-bold">0</p>
        </div>
        <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6">
          <h3 className="text-lg font-medium mb-2">Matches</h3>
          <p className="text-3xl font-bold">0</p>
        </div>
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        <div className="md:col-span-2">
          <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6 mb-8">
            <div className="flex justify-between items-center mb-6">
              <h2 className="text-xl font-semibold">Your Applications</h2>
              <Link
                href="/jobs"
                className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white text-sm"
              >
                Browse More Jobs
              </Link>
            </div>

            {loading ? (
              <div className="flex justify-center py-8">
                <div className="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2 border-neutral-900"></div>
              </div>
            ) : applications.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-neutral-600 dark:text-neutral-400 mb-4">
                  You haven't applied to any jobs yet.
                </p>
                <Link
                  href="/jobs"
                  className="bg-neutral-900 hover:bg-neutral-800 text-white px-4 py-2 rounded-md"
                >
                  Browse Jobs
                </Link>
              </div>
            ) : (
              <div className="space-y-4">
                {applications.map((application) => (
                  <div
                    key={application.id}
                    className="border border-neutral-200 dark:border-neutral-700 rounded-lg p-4"
                  >
                    <div className="flex justify-between items-start">
                      <div>
                        <h3 className="font-medium">{application.jobTitle}</h3>
                        <p className="text-sm text-neutral-600 dark:text-neutral-400">
                          Applied on {new Date(application.createdAt).toLocaleDateString()}
                        </p>
                      </div>
                      <span className={`text-xs px-2 py-1 rounded-full ${
                        application.status === "pending"
                          ? "bg-blue-100 dark:bg-blue-900/30 text-blue-800 dark:text-blue-300"
                          : application.status === "reviewed"
                          ? "bg-purple-100 dark:bg-purple-900/30 text-purple-800 dark:text-purple-300"
                          : application.status === "contacted"
                          ? "bg-green-100 dark:bg-green-900/30 text-green-800 dark:text-green-300"
                          : application.status === "rejected"
                          ? "bg-red-100 dark:bg-red-900/30 text-red-800 dark:text-red-300"
                          : "bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300"
                      }`}>
                        {application.status.charAt(0).toUpperCase() + application.status.slice(1)}
                      </span>
                    </div>
                    {application.matchPercentage && (
                      <div className="mt-3 flex items-center">
                        <span className="text-xs text-neutral-600 dark:text-neutral-400 mr-2">
                          Match:
                        </span>
                        <div className="w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2">
                          <div
                            className="bg-green-500 h-2 rounded-full"
                            style={{ width: `${application.matchPercentage}%` }}
                          ></div>
                        </div>
                        <span className="ml-2 text-xs font-medium">
                          {application.matchPercentage}%
                        </span>
                      </div>
                    )}
                    <div className="mt-3">
                      <Link
                        href={`/jobs/${application.jobId}`}
                        className="text-sm text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white"
                      >
                        View Job
                      </Link>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </div>
        </div>

        <div>
          <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6 mb-8">
            <h2 className="text-xl font-semibold mb-6">Your Profile</h2>

            {profileComplete ? (
              <div>
                <div className="mb-4">
                  <h3 className="text-sm font-medium text-neutral-500 dark:text-neutral-400 mb-1">
                    Public Display
                  </h3>
                  <p className="text-neutral-600 dark:text-neutral-400">
                    Shown as: <span className="font-medium">{candidateData?.profession}, {candidateData?.age}</span>
                  </p>
                </div>

                <div className="mb-4">
                  <h3 className="text-sm font-medium text-neutral-500 dark:text-neutral-400 mb-1">
                    Languages
                  </h3>
                  <div className="space-y-1">
                    <p className="text-sm">German: <span className="font-medium">{candidateData?.germanLanguageLevel || "Not set"}</span></p>
                    <p className="text-sm">English: <span className="font-medium">{candidateData?.englishLanguageLevel || "Not set"}</span></p>
                    {candidateData?.otherLanguages && candidateData.otherLanguages.length > 0 && (
                      <p className="text-sm">Other: <span className="font-medium">
                        {candidateData.otherLanguages.map(lang => `${lang.language} (${lang.level})`).join(", ")}
                      </span></p>
                    )}
                  </div>
                </div>

                {candidateData?.desiredCountries && candidateData.desiredCountries.length > 0 && (
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-neutral-500 dark:text-neutral-400 mb-1">
                      Desired Countries
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {candidateData.desiredCountries.slice(0, 5).map((countryId: string) => {
                        const country = countries.find(c => c.id === countryId);
                        return (
                          <span key={countryId} className="bg-blue-50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-400 text-xs px-2 py-1 rounded flex items-center">
                            {country ? country.name : countryId}
                          </span>
                        );
                      })}
                      {candidateData.desiredCountries.length > 5 && (
                        <span className="text-blue-600 dark:text-blue-400 text-xs">
                          +{candidateData.desiredCountries.length - 5} more
                        </span>
                      )}
                    </div>
                  </div>
                )}

                <div className="mb-4">
                  <h3 className="text-sm font-medium text-neutral-500 dark:text-neutral-400 mb-1">
                    Profile Status
                  </h3>
                  <div className="space-y-2">
                    <div className="flex items-center">
                      <div className={`w-4 h-4 rounded-full mr-2 ${candidateData?.workExperience && (candidateData.workExperience.length > 0 || candidateData.noWorkExperience) ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <span className="text-sm">Work Experience: {candidateData?.workExperience && (candidateData.workExperience.length > 0 || candidateData.noWorkExperience) ? 'Completed' : 'Incomplete'}</span>
                    </div>
                    <div className="flex items-center">
                      <div className={`w-4 h-4 rounded-full mr-2 ${candidateData?.education && candidateData.education.length > 0 ? 'bg-green-500' : 'bg-red-500'}`}></div>
                      <span className="text-sm">Education: {candidateData?.education && candidateData.education.length > 0 ? 'Completed' : 'Incomplete'}</span>
                    </div>
                  </div>
                </div>

                {candidateData?.benefits?.length > 0 && (
                  <div className="mb-4">
                    <h3 className="text-sm font-medium text-neutral-500 dark:text-neutral-400 mb-1">
                      Job Preferences
                    </h3>
                    <div className="flex flex-wrap gap-2">
                      {candidateData.benefits.slice(0, 3).map((benefitId: string) => {
                        const benefit = jobBenefits.find(b => b.id === benefitId);
                        return (
                          <span key={benefitId} className="bg-green-50 dark:bg-green-900/20 text-green-700 dark:text-green-400 text-xs px-2 py-1 rounded-full flex items-center">
                            <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                              <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                            </svg>
                            {benefit ? benefit.label : benefitId}
                          </span>
                        );
                      })}
                      {candidateData.benefits.length > 3 && (
                        <span className="text-green-600 dark:text-green-400 text-xs">
                          +{candidateData.benefits.length - 3} more
                        </span>
                      )}
                    </div>
                  </div>
                )}

                <Link
                  href="/candidate/profile"
                  className="text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white text-sm"
                >
                  Edit Profile
                </Link>
              </div>
            ) : (
              <div className="text-center py-4">
                <p className="text-neutral-600 dark:text-neutral-400 mb-4">
                  Complete your profile to increase your chances of getting hired.
                </p>
                <Link
                  href="/candidate/profile"
                  className="bg-neutral-900 hover:bg-neutral-800 text-white px-4 py-2 rounded-md"
                >
                  Complete Profile
                </Link>
              </div>
            )}
          </div>

          <div className="bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-6">
            <h2 className="text-xl font-semibold mb-6">Recommended Jobs</h2>

            <div className="text-center py-4">
              <p className="text-neutral-600 dark:text-neutral-400">
                Complete your profile to see job recommendations.
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
