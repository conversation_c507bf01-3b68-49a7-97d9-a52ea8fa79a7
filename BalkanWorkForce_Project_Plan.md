# Balkan Work Force - Project Plan

## Project Overview
A recruitment platform connecting Balkan workers with German and EU companies. The platform will have separate sections for employers and job candidates, with features for job posting, premium listings, profile management, and matching algorithms.

## Tech Stack
- **Frontend**: Next.js with shadcn/ui components
- **Authentication**: Firebase Authentication with Google Sign-in
- **Database**: Firebase Firestore
- **Storage**: Firebase Storage (for profile images, documents)
- **Hosting**: Firebase Hosting

## Phase 1: Project Setup and Authentication

- [ ] Initialize Next.js project
- [ ] Set up shadcn/ui components
- [ ] Configure Firebase project
- [ ] Implement authentication system
  - [ ] Google Sign-in
  - [ ] Email/Password authentication as fallback
- [ ] Create user roles (employer/candidate)
- [ ] Design and implement basic layout
  - [ ] Navigation
  - [ ] Footer
  - [ ] Responsive design

## Phase 2: User Profiles

- [ ] Employer Profile
  - [ ] Company information
  - [ ] Contact details
  - [ ] Company description
  - [ ] Industry/sector
  - [ ] Location
  - [ ] Preference tags for matching algorithm
- [ ] Candidate Profile
  - [ ] Personal information
  - [ ] Work experience
  - [ ] Education
  - [ ] Skills
  - [ ] Languages
  - [ ] Desired positions
  - [ ] Preference tags for matching algorithm
- [ ] Profile editing functionality
- [ ] Profile visibility settings

## Phase 3: Job Posting System

- [ ] Job creation form for employers
  - [ ] Job title
  - [ ] Description
  - [ ] Requirements
  - [ ] Location
  - [ ] Salary range
  - [ ] Employment type
  - [ ] Tags/categories
- [ ] Regular vs Premium job distinction
- [ ] Premium package system
  - [ ] 7-day package
  - [ ] 14-day package
  - [ ] 30-day package
- [ ] Premium slot limitation (max 10)
  - [ ] Notification system for when slots become available
- [ ] Job listing page with filtering options
- [ ] Job detail page

## Phase 4: Application System

- [ ] Application form for candidates
- [ ] Application tracking for candidates
- [ ] Application management for employers
- [ ] Email notifications for new applications
- [ ] Application status updates

## Phase 5: Matching Algorithm

- [ ] Define 10-15 matching criteria
- [ ] Implement preference selection for employers
- [ ] Implement preference selection for candidates
- [ ] Create matching percentage calculation
- [ ] Display matching percentage on job listings/applications
- [ ] Recommendation system based on matching

## Phase 6: Payment Integration

- [ ] Research and select payment gateway
- [ ] Implement payment system for premium packages
- [ ] Create admin dashboard for payment tracking
- [ ] Email receipts and invoices

## Phase 7: Testing and Optimization

- [ ] User testing with employers
- [ ] User testing with candidates
- [ ] Performance optimization
- [ ] Mobile responsiveness testing
- [ ] Browser compatibility testing

## Phase 8: Deployment and Launch

- [ ] Final testing
- [ ] Deploy to production
- [ ] Monitoring setup
- [ ] Analytics integration
- [ ] Launch marketing

## Future Enhancements

- [ ] Advanced notification system
- [ ] Chat functionality between employers and candidates
- [ ] AI-powered job matching
- [ ] Resume parsing
- [ ] Integration with job boards
- [ ] Multilingual support (German, English, Balkan languages)
- [ ] Reporting and analytics for employers

## Questions to Clarify

1. What specific Balkan countries are you targeting?
- the companies are german 95% and the workers are from the Balkan countries like Bosnia, Serbia, Croatia, Slovenia, Montenegro, Macedonia, Albania, Kosovo
2. Do you need multilingual support from the beginning?
- the companies should have german and english, when they register we should serve the language from the country they are from , the candidates should have their own language and english.
3. Are there specific industries you want to focus on initially?
-no.
4. Do you have any design preferences or brand guidelines?
-i think standard dark blue.
5. What is the timeline for the initial launch?
6. Do you want to implement any specific verification process for employers?
- later yes, for the beginning no.
7. How do you want to handle the payment system for premium listings?
- paddle and paypal, but we can do that at later date.
8. What specific matching criteria do you have in mind?
- the companies posts a job and they select what they offer in the role, what specific benefits they are offering. like "home office possible", "flexible working hours", "free drinks" something like that. the candidate can select what he is looking for. we should calculate the matching percentage. and show the candidates the matching procentage for the jobs.
9. Do you want to include any additional features for the initial version?
- no
10. Are there any legal considerations (GDPR, etc.) that need to be addressed?
- no


additional info, the candidate names should be hidden. no one can see the names of the candidates. the candidates will be named like their proffession, like Electrician, 29 or Car Mechanic, 22 or in German Elektriker, 29 and KFZ-Mechaniker, 22.


// Import the functions you need from the SDKs you need
import { initializeApp } from "firebase/app";
// TODO: Add SDKs for Firebase products that you want to use
// https://firebase.google.com/docs/web/setup#available-libraries

// Your web app's Firebase configuration
const firebaseConfig = {
  apiKey: "AIzaSyCVp32H24SO7K8sG-NdFkYqU0i1sbdXSLo",
  authDomain: "invoicing-790df.firebaseapp.com",
  projectId: "invoicing-790df",
  storageBucket: "invoicing-790df.firebasestorage.app",
  messagingSenderId: "866703604451",
  appId: "1:866703604451:web:ce15af82ca220c110363d6"
};

// Initialize Firebase
const app = initializeApp(firebaseConfig);