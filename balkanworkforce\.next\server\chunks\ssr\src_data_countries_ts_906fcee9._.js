module.exports = {

"[project]/src/data/countries.ts [app-ssr] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.s({
    "countries": (()=>countries)
});
const countries = [
    {
        id: "de",
        name: "Germany",
        code: "DE",
        flagUrl: "/flags/de.svg",
        region: "eu"
    },
    {
        id: "at",
        name: "Austria",
        code: "AT",
        flagUrl: "/flags/at.svg",
        region: "eu"
    },
    {
        id: "hr",
        name: "Croatia",
        code: "HR",
        flagUrl: "/flags/hr.svg",
        region: "eu"
    },
    {
        id: "si",
        name: "Slovenia",
        code: "SI",
        flagUrl: "/flags/si.svg",
        region: "eu"
    },
    {
        id: "ba",
        name: "Bosnia and Herzegovina",
        code: "BA",
        flagUrl: "/flags/ba.svg",
        region: "balkans"
    },
    {
        id: "rs",
        name: "Serbia",
        code: "RS",
        flagUrl: "/flags/rs.svg",
        region: "balkans"
    },
    {
        id: "me",
        name: "Montenegro",
        code: "ME",
        flagUrl: "/flags/me.svg",
        region: "balkans"
    },
    {
        id: "mk",
        name: "North Macedonia",
        code: "MK",
        flagUrl: "/flags/mk.svg",
        region: "balkans"
    },
    {
        id: "al",
        name: "Albania",
        code: "AL",
        flagUrl: "/flags/al.svg",
        region: "balkans"
    },
    {
        id: "bg",
        name: "Bulgaria",
        code: "BG",
        flagUrl: "/flags/bg.svg",
        region: "balkans"
    },
    {
        id: "ro",
        name: "Romania",
        code: "RO",
        flagUrl: "/flags/ro.svg",
        region: "balkans"
    },
    {
        id: "gr",
        name: "Greece",
        code: "GR",
        flagUrl: "/flags/gr.svg",
        region: "balkans"
    }
];
}}),

};

//# sourceMappingURL=src_data_countries_ts_906fcee9._.js.map