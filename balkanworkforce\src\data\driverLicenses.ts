export interface DriverLicense {
  id: string;
  name: string;
  description: string;
  translations: {
    [languageCode: string]: {
      name: string;
      description: string;
    };
  };
  category: "car" | "motorcycle" | "truck" | "bus" | "special" | "none";
}

export const driverLicenses: DriverLicense[] = [
  {
    id: "none",
    name: "No License",
    description: "No driver's license",
    translations: {
      en: {
        name: "No License",
        description: "No driver's license"
      },
      de: {
        name: "Kein Führerschein",
        description: "Kein Führerschein vorhanden"
      },
      bs: {
        name: "<PERSON><PERSON> vozačke",
        description: "Bez vozačke dozvole"
      }
    },
    category: "none"
  },
  {
    id: "am",
    name: "AM",
    description: "Mopeds and light quadricycles",
    translations: {
      en: {
        name: "AM",
        description: "Mopeds and light quadricycles"
      },
      de: {
        name: "AM",
        description: "Mopeds und leichte Vierradfahrzeuge"
      },
      bs: {
        name: "AM",
        description: "Mopedi i laki četverocikli"
      }
    },
    category: "motorcycle"
  },
  {
    id: "a1",
    name: "<PERSON>",
    description: "Light motorcycles",
    translations: {
      en: {
        name: "A1",
        description: "Light motorcycles"
      },
      de: {
        name: "A1",
        description: "Leichte Krafträder"
      },
      bs: {
        name: "A1",
        description: "Laki motocikli"
      }
    },
    category: "motorcycle"
  },
  {
    id: "a2",
    name: "A2",
    description: "Medium motorcycles",
    translations: {
      en: {
        name: "A2",
        description: "Medium motorcycles"
      },
      de: {
        name: "A2",
        description: "Mittlere Krafträder"
      },
      bs: {
        name: "A2",
        description: "Srednji motocikli"
      }
    },
    category: "motorcycle"
  },
  {
    id: "a",
    name: "A",
    description: "Motorcycles",
    translations: {
      en: {
        name: "A",
        description: "Motorcycles"
      },
      de: {
        name: "A",
        description: "Krafträder"
      },
      bs: {
        name: "A",
        description: "Motocikli"
      }
    },
    category: "motorcycle"
  },
  {
    id: "b1",
    name: "B1",
    description: "Quadricycles",
    translations: {
      en: {
        name: "B1",
        description: "Quadricycles"
      },
      de: {
        name: "B1",
        description: "Vierradfahrzeuge"
      },
      bs: {
        name: "B1",
        description: "Četverocikli"
      }
    },
    category: "car"
  },
  {
    id: "b",
    name: "B",
    description: "Cars and light vans",
    translations: {
      en: {
        name: "B",
        description: "Cars and light vans"
      },
      de: {
        name: "B",
        description: "Pkw und leichte Nutzfahrzeuge"
      },
      bs: {
        name: "B",
        description: "Automobili i laka dostavna vozila"
      }
    },
    category: "car"
  },
  {
    id: "be",
    name: "BE",
    description: "Cars with trailer",
    translations: {
      en: {
        name: "BE",
        description: "Cars with trailer"
      },
      de: {
        name: "BE",
        description: "Pkw mit Anhänger"
      },
      bs: {
        name: "BE",
        description: "Automobili s prikolicom"
      }
    },
    category: "car"
  },
  {
    id: "c1",
    name: "C1",
    description: "Medium-sized vehicles",
    translations: {
      en: {
        name: "C1",
        description: "Medium-sized vehicles"
      },
      de: {
        name: "C1",
        description: "Mittelgroße Fahrzeuge"
      },
      bs: {
        name: "C1",
        description: "Vozila srednje veličine"
      }
    },
    category: "truck"
  },
  {
    id: "c1e",
    name: "C1E",
    description: "Medium-sized vehicles with trailer",
    translations: {
      en: {
        name: "C1E",
        description: "Medium-sized vehicles with trailer"
      },
      de: {
        name: "C1E",
        description: "Mittelgroße Fahrzeuge mit Anhänger"
      },
      bs: {
        name: "C1E",
        description: "Vozila srednje veličine s prikolicom"
      }
    },
    category: "truck"
  },
  {
    id: "c",
    name: "C",
    description: "Large goods vehicles",
    translations: {
      en: {
        name: "C",
        description: "Large goods vehicles"
      },
      de: {
        name: "C",
        description: "Schwere Nutzfahrzeuge"
      },
      bs: {
        name: "C",
        description: "Teretna vozila"
      }
    },
    category: "truck"
  },
  {
    id: "ce",
    name: "CE",
    description: "Large goods vehicles with trailer",
    translations: {
      en: {
        name: "CE",
        description: "Large goods vehicles with trailer"
      },
      de: {
        name: "CE",
        description: "Schwere Nutzfahrzeuge mit Anhänger"
      },
      bs: {
        name: "CE",
        description: "Teretna vozila s prikolicom"
      }
    },
    category: "truck"
  },
  {
    id: "d1",
    name: "D1",
    description: "Minibuses",
    translations: {
      en: {
        name: "D1",
        description: "Minibuses"
      },
      de: {
        name: "D1",
        description: "Kleinbusse"
      },
      bs: {
        name: "D1",
        description: "Minibusevi"
      }
    },
    category: "bus"
  },
  {
    id: "d1e",
    name: "D1E",
    description: "Minibuses with trailer",
    translations: {
      en: {
        name: "D1E",
        description: "Minibuses with trailer"
      },
      de: {
        name: "D1E",
        description: "Kleinbusse mit Anhänger"
      },
      bs: {
        name: "D1E",
        description: "Minibusevi s prikolicom"
      }
    },
    category: "bus"
  },
  {
    id: "d",
    name: "D",
    description: "Buses",
    translations: {
      en: {
        name: "D",
        description: "Buses"
      },
      de: {
        name: "D",
        description: "Busse"
      },
      bs: {
        name: "D",
        description: "Autobusi"
      }
    },
    category: "bus"
  },
  {
    id: "de",
    name: "DE",
    description: "Buses with trailer",
    translations: {
      en: {
        name: "DE",
        description: "Buses with trailer"
      },
      de: {
        name: "DE",
        description: "Busse mit Anhänger"
      },
      bs: {
        name: "DE",
        description: "Autobusi s prikolicom"
      }
    },
    category: "bus"
  }
];

// Group licenses by category for easier display
export const driverLicensesByCategory = {
  none: driverLicenses.filter(license => license.category === "none"),
  car: driverLicenses.filter(license => license.category === "car"),
  motorcycle: driverLicenses.filter(license => license.category === "motorcycle"),
  truck: driverLicenses.filter(license => license.category === "truck"),
  bus: driverLicenses.filter(license => license.category === "bus"),
  special: driverLicenses.filter(license => license.category === "special"),
};

// Helper function to get the highest license category
export function getHighestLicenseCategory(licenses: string[]): string {
  if (!licenses || licenses.length === 0) return "none";
  
  // Priority order (highest to lowest)
  const priorityOrder = [
    "de", "d", "d1e", "d1",
    "ce", "c", "c1e", "c1",
    "be", "b", "b1",
    "a", "a2", "a1", "am",
    "none"
  ];
  
  // Find the license with the highest priority
  let highestPriority = Number.MAX_SAFE_INTEGER;
  let highestLicense = "none";
  
  for (const license of licenses) {
    const priority = priorityOrder.indexOf(license.toLowerCase());
    if (priority !== -1 && priority < highestPriority) {
      highestPriority = priority;
      highestLicense = license.toLowerCase();
    }
  }
  
  return highestLicense;
}
