# BalkanWorkForce Project Status

## Project Overview
BalkanWorkForce is a recruitment platform connecting Balkan workers with German and EU companies. The platform has separate sections for employers and job candidates, with features for job posting, premium listings, profile management, and matching algorithms.

The platform allows public access to job listings without requiring login, but requires registration for applying to jobs or accessing premium features. This approach maximizes visibility while encouraging user registration.

## Tech Stack
- **Frontend**: Next.js with TypeScript
- **UI Components**: shadcn/ui
- **Authentication**: Firebase Authentication with Google Sign-in
- **Database**: Firebase Firestore
- **Storage**: Firebase Storage (for profile images, documents)

## ✅ Completed Features

### 1. Project Setup and Authentication
- [x] Initialized Next.js project with TypeScript
- [x] Set up shadcn/ui components
- [x] Configured Firebase project
- [x] Implemented authentication system
  - [x] Google Sign-in
  - [x] User roles (employer/candidate)
  - [x] Authentication context
  - [x] Protected routes

### 2. User Profiles
- [x] Candidate Profile
  - [x] Anonymous display (showing only profession and age)
  - [x] Profile picture upload
  - [x] Skills, experience, education fields
  - [x] Languages and preferences
  - [x] Form validation
  - [x] Firestore integration
  - [x] Structured work experience with date ranges
  - [x] Structured education history with date ranges
  - [x] Standardized language proficiency levels (A1-C2, None)
  - [x] Country of origin selection
  - [x] Desired countries selection (Germany, Austria, Croatia, Slovenia)
  - [x] Optional fields for profile saving (only profession and age required)
  - [x] Complete profile required for job applications
- [x] Employer Profile
  - [x] Company information
  - [x] Company logo upload
  - [x] Contact details
  - [x] Industry and location
  - [x] Preferences for matching
  - [x] Form validation
  - [x] Firestore integration
- [x] Dashboard Integration
  - [x] Profile completion status
  - [x] Profile summary sections
  - [x] Links to profile pages
  - [x] Initial profile setup flow

### 3. Basic UI and Navigation
- [x] Home page
- [x] Authentication page
- [x] Navigation bar with role-specific links
- [x] Responsive design
- [x] Dashboard layouts for both user types
- [x] Mobile-first approach for all views

## ✅ Completed Features (continued)

### 4. Job Management
- [x] Job creation form for employers
  - [x] Job benefits/features selection (minimum 3 from 10-15 options)
  - [x] Fields for job details, requirements, and compensation
  - [x] Salary range with min/max values in Euro
  - [x] Country selection for job location
  - [x] Accommodation options
- [x] Job editing functionality
- [x] Job deletion functionality
- [x] Job detail page for employers
- [x] Enhanced job listing page with benefits display
- [x] Public job listings (visible without login)

### 5. Candidate Preferences
- [x] Job benefits selection for candidates (same options as employers)
- [x] Minimum required benefits selection (3 options)
- [x] Display of selected benefits on candidate dashboard
- [x] Storage of preferences in Firestore for matching
- [x] Desired countries selection

### 6. Candidate Browsing for Employers
- [x] Browse candidates page for employers
- [x] Filter candidates by profession, skills, and benefits
- [x] Candidate detail page with profile information
- [x] Match percentage calculation between candidates and employer's jobs
- [x] Anonymous candidate profiles (showing only profession and age)

## 🔄 In Progress Features

### 1. Multi-language Support
- [x] User interface translation framework
- [x] Language selection component
- [x] Language context for the entire application
- [x] Translation files for English, German, and Bosnian
- [x] User language preference storage
- [x] Settings page for language selection
- [ ] Complete translation of all UI components

### 2. Application System
- [x] Application form for candidates
- [x] Application tracking for candidates
- [x] Application management for employers
- [x] Profile completion check before application
- [ ] Email notifications for new applications

### 3. Driver's License Feature
- [ ] Add driver's license field to candidate profile
  - [ ] Dropdown with predefined license categories (None, B, C, CE, C1, B1, A, etc.)
  - [ ] "No license" option as default
  - [ ] Instruction text: "Select your highest driver's license category"
  - [ ] Store in candidate profile data
- [ ] Add driver's license requirements to job creation
  - [ ] Add driver's license field to structured requirements form
  - [ ] Allow employers to specify required license category for the job
  - [ ] Make it optional but visible in job listings
- [ ] Add driver's license filter to employer candidate search
  - [ ] Filter candidates by license category
  - [ ] Include in advanced search options
  - [ ] Update matching algorithm to consider license requirements

### 4. Admin Panel
- [ ] Create admin authentication and authorization system
  - [ ] Admin user role in Firebase
  - [ ] Admin-specific routes with protection
  - [ ] Admin dashboard layout
- [ ] User management features
  - [ ] View and manage employers
  - [ ] View and manage candidates
  - [ ] Enable/disable user accounts
  - [ ] View user statistics
- [ ] Content management features
  - [ ] Manage positions/professions with translations
  - [ ] Manage education fields with translations
  - [ ] Manage driver's license categories
  - [ ] Manage job benefits
  - [ ] Manage countries and locations
- [ ] Platform settings
  - [ ] Language management
  - [ ] Email templates
  - [ ] Platform configuration options

## 📋 Planned Features

### 1. Premium Features
- [ ] Premium job listings
- [ ] Package system (7, 14, 30 days)
- [ ] Country-specific premium visibility
- [ ] Per-country pricing model
- [ ] Payment integration with Paddle
- [ ] Premium slot limitation (max 10)
- [x] CV packages with limited invites (10, 25, 50 invites)
- [x] Combined packages (premium job + CV invites)
- [x] Invite tracking and management

### 2. Matching Algorithm
- [x] Preference selection for employers (implemented in job creation)
- [x] Preference selection for candidates (implemented in candidate profile)
- [x] Matching percentage calculation (implemented for candidate-job matching)
- [ ] Recommendation system
- [x] Invite candidates to apply for jobs
- [ ] Driver's license matching between job requirements and candidate profiles

### 3. Advanced Features
- [ ] Notifications system
- [ ] Analytics for employers
- [x] Multi-language support
  - [x] User interface translation framework
  - [x] Language selection and storage
  - [ ] Complete translation of all UI components
  - [ ] Country-specific content
  - [x] Profession/position mapping across languages
- [ ] Mobile app (mobile-first approach already implemented)
- [ ] Translation services for job listings
  - [ ] Automatic translation of job listings to multiple languages
  - [x] Plan for automatic translation implementation
  - [ ] Translation credits system as premium feature
- [ ] Real-time chat translation
  - [ ] Message translation between different languages
  - [ ] Support for common language pairs (German-Balkan languages)

## 🔑 Key Business Logic

### Candidate Privacy
- **Anonymous Profiles**: Candidate names are hidden. They are displayed only as "Profession, Age" (e.g., "Electrician, 29" or "KFZ-Mechaniker, 22").
- **Contact Protection**: Employers cannot directly contact candidates until an application is accepted.
- **Data Control**: Candidates control what information is visible to employers.

### Employer Features
- **Company Profiles**: Employers can create detailed company profiles.
- **Job Posting**: Employers can post job listings with detailed requirements.
- **Application Management**: Review and manage applications from candidates.
- **Premium Listings**: Option to promote job listings for better visibility.
- **Premium Candidate Access**: Browsing and filtering candidates is a premium feature that employers can purchase separately.
- **Candidate Invitations**: Premium employers can invite candidates to apply for specific jobs.
- **Direct Messaging**: Premium employers can initiate chat with candidates they're interested in.

### Candidate Features
- **Anonymous Applications**: Apply to jobs without revealing personal identity.
- **Skill Showcase**: Highlight skills, experience, and qualifications.
- **Structured Work Experience**: Track work history with detailed date ranges and positions.
- **Education History**: Record educational background with degrees, fields of study, and date ranges.
- **Language Proficiency**: Specify language skills with standardized proficiency levels (None, A1-C2, Native).
- **Country of Origin**: Specify home country for better job matching and language preferences.
- **Desired Countries**: Select countries where the candidate wants to work.
- **Driver's License**: Specify highest driver's license category for job matching.
- **Application Tracking**: Track status of job applications.
- **Job Recommendations**: Receive job recommendations based on profile.
- **Messaging**: Chat with employers after applying to jobs or receiving invitations.
- **Minimal Registration**: Only profession and age required at registration, with full profile completion before job applications.

### Premium System
- **Limited Premium Slots**: Maximum of 10 premium job listings at any time.
- **Duration Options**: Premium packages for 7, 14, or 30 days.
- **Country-Specific Premium**: Employers can select specific countries for premium visibility.
- **Per-Country Pricing**: Each selected country adds to the premium price (e.g., 5€/country for 7 days).
- **Tiered Duration Pricing**:
  - 5€ per country for 7 days
  - 8€ per country for 14 days
  - 15€ per country for 30 days
- **Notification System**: Employers are notified when premium slots become available.
- **Candidate Access Tier**: Separate premium tier for browsing, filtering, and contacting candidates.
- **Communication Features**: Premium employers can message candidates and invite them to apply for jobs.
- **CV Packages**: Tiered invite packages (10 invites for 5€, 25 invites for 10€, 50 invites for 20€).
- **Invite Tracking**: System tracks remaining invites per employer.
- **Combined Packages**: Special offers combining job listings and CV invites (e.g., 30-day premium job + 5 invites).
- **Translation Services**: Premium feature for translating job listings to multiple languages.
- **Chat Translation**: Paid feature for real-time translation of messages between employers and candidates.

### Matching Algorithm
- **Job-Candidate Matching**: Matching happens between specific jobs and candidates, not companies and candidates.
- **Job Benefits Selection**: Employers must select at least 3 benefits/features when creating a job posting.
- **Candidate Preferences**: Candidates select their preferred job benefits/features using the same options.
- **Percentage Calculation**: Shows how well a candidate's preferences match a job's offered benefits.

## 📝 Next Steps

### 1. Testing and Stabilization
- Implement comprehensive testing for existing features
- Fix any bugs or issues in the current implementation
- Optimize performance and user experience
- Ensure responsive design works on all devices
- Test multilingual support and translations

### 2. Driver's License Feature Implementation
- Create driver's license data structure
- Add driver's license field to candidate profile form
- Implement dropdown with predefined license categories
- Add driver's license filter to employer candidate search
- Update matching algorithm to consider license requirements
- Add license requirements to job creation form

### 3. Admin Panel Implementation
- Set up admin authentication and authorization
- Create admin dashboard layout and navigation
- Implement user management features
  - View and manage employers and candidates
  - Enable/disable user accounts
- Implement content management features
  - Create forms for managing positions/professions with translations
  - Create forms for managing education fields with translations
  - Create forms for managing driver's license categories
- Implement platform settings management
  - Language management
  - Email templates
  - Platform configuration

### 4. Job Management Enhancements
- Implement advanced filtering and search for job listings
- Add sorting options for job listings
- Improve job detail page with more interactive elements
- ✅ Add job application button and flow
- Add driver's license requirements to job listings

### 5. Application System
- ✅ Create application form for candidates
- ✅ Implement application tracking for candidates
- ✅ Add application management for employers
- ✅ Set up status updates for applications
- ✅ Implement profile completion check before application
- Add email notifications for new applications

### 6. Premium Features
- Implement premium job listings
- Create package system with different durations (7, 14, 30 days)
- Implement country-specific premium visibility
- Create per-country pricing model (5€/7 days, 8€/14 days, 15€/30 days per country)
- Add payment integration
- Set up premium slot limitation
- Implement premium candidate browsing feature for employers
- Create separate payment options for candidate access
- ✅ Add candidate invitation system for premium employers
- ✅ Implement CV packages with tiered invite limits (10/25/50 invites)
- ✅ Create invite tracking and management system
- ✅ Develop combined packages (premium job + CV invites)
- ✅ Add purchase flow for additional invites when depleted

### 5. Messaging System
- Implement chat functionality between employers and candidates
- Set up chat access rules (only after application or for premium employers)
- Create notification system for new messages
- Add message history and management
- Implement read receipts and typing indicators

### 6. Matching Algorithm
- [x] Define 10-15 job benefits/features for selection
- [x] Implement job benefits selection during job creation (minimum 3)
- [x] Add candidate preference selection using the same options
- [x] Create matching percentage calculation
- [ ] Build recommendation system

### 7. Translation Services
- [x] Create plan for automatic translation implementation
  - [x] Define data structure for multilingual job listings
  - [x] Plan background translation process
  - [x] Outline OpenAI API integration approach
- [ ] Implement job listing translation to multiple languages using OpenAI API
  - [ ] Create translation service for job titles, descriptions, and requirements
  - [ ] Implement automatic detection of source language
  - [ ] Support translation between German, English, and Balkan languages
- [ ] Create UI for employers to select target languages for translation
  - [ ] Add language selection in job creation/edit forms
  - [ ] Show translation preview before publishing
  - [ ] Display translation status indicators
- [ ] Develop real-time chat translation between employers and candidates
  - [ ] Implement message translation in chat interface
  - [ ] Add language indicators for original message language
  - [ ] Support translation memory for common phrases
- [ ] Add translation credits system as a premium feature
  - [ ] Create translation credit packages
  - [ ] Implement usage tracking and limits
  - [ ] Add automatic renewal options
- [ ] Implement caching for translated content to optimize API usage
  - [ ] Store translations in database for reuse
  - [ ] Implement invalidation strategy for updated content
  - [ ] Add batch translation processing for efficiency

## 🔧 Technical Notes

### Firebase Structure
- **Collections**:
  - `users`: Basic user information and authentication
  - `employers`: Employer profiles
  - `candidates`: Candidate profiles
    - Include `country` field for candidate's country of origin
    - Include `desiredCountries` array for countries where candidate wants to work
    - Structured `workExperience` array with detailed date ranges
    - Structured `education` array with detailed date ranges
    - Standardized language proficiency levels (None, A1-C2, Native)
    - Include `driverLicense` field for highest license category
    - Include `benefits` array for job preferences
    - Include `profileCompleted` boolean flag
  - `jobs`: Job listings
    - Support for multiple language versions of job content
    - Include `originalLanguage` field to track source language
    - Include `benefits` array for job features
    - Include `premiumCountries` array for premium visibility
    - Include `accommodation` field for housing options
  - `applications`: Job applications
    - Include match percentage between candidate and job
    - Include status tracking fields
  - `messages`: Chat messages between employers and candidates
    - Include `originalLanguage` and `translatedVersions` fields
  - `conversations`: Metadata for chat conversations
  - `invites`: Candidate invitations from employers
  - `packages`: Premium packages purchased by employers
  - `transactions`: Payment and subscription records
  - `translations`: Cached translations and usage tracking
    - Store original content hash for efficient lookups
    - Track usage statistics per employer
  - `positions`: Standardized job positions/professions in multiple languages
    - Map equivalent positions across languages (e.g., "Elektriker" = "Electrician")
  - `degrees`: Standardized educational degrees in multiple languages
    - Map equivalent degrees across languages
  - `studyFields`: Standardized fields of study in multiple languages
  - `driverLicenses`: Standardized driver's license categories with descriptions

### Authentication Flow
1. User signs in with Google
2. User selects role (employer/candidate)
3. User is redirected to appropriate dashboard
4. Profile completion is checked and prompted if incomplete

### File Storage
- Profile pictures stored in Firebase Storage
- Company logos stored in Firebase Storage
- Path format: `profile-images/{userId}` and `company-logos/{userId}`

### Form Validation
- Using Zod for schema validation
- React Hook Form for form state management
- Client-side validation with server-side checks

### Translation API Integration
- OpenAI API for translation services
  - Use GPT models for high-quality translations
  - Implement context-aware translation for job-specific terminology
  - Support for technical and industry-specific vocabulary
- Custom assistants for specific language pairs
  - Create specialized assistants for German-Bosnian, German-Serbian, etc.
  - Fine-tune for recruitment and job-related content
  - Preserve formatting and structure in translations
- Caching translated content to minimize API calls
  - Implement Redis or Firebase caching layer
  - Use content hashing for efficient cache invalidation
  - Set appropriate TTL for cached translations
- Tracking translation usage per employer
  - Create usage dashboard for employers
  - Implement quota management system
  - Send notifications for low translation credits
- Database schema for translations
  - Store original and translated content
  - Track translation metadata (language, timestamp, model used)
  - Implement versioning for content updates

## 🚀 Getting Started for Development

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up Firebase project and add credentials to `.env.local`
4. Run development server: `npm run dev`
5. Access the application at `http://localhost:3000`
