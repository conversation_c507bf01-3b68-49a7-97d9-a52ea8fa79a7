import { Candidate } from "@/types";
import { MIN_BENEFITS_REQUIRED } from "@/data/jobBenefits";

/**
 * Checks if a candidate profile has the minimum required fields for registration
 * @param candidate The candidate profile to check
 * @returns Boolean indicating if the profile has minimum required fields
 */
export function hasMinimumProfileInfo(candidate: Partial<Candidate>): boolean {
  // Only profession and age are required at registration
  return !!(candidate.profession && candidate.age);
}

/**
 * Checks if a candidate profile is complete with all required fields for job applications
 * @param candidate The candidate profile to check
 * @returns Boolean indicating if the profile is complete
 */
export function isCandidateProfileComplete(candidate: Partial<Candidate>): boolean {
  // Check required basic information
  if (!candidate.profession || !candidate.age || !candidate.gender) {
    return false;
  }

  // Check desired countries
  if (!candidate.desiredCountries || candidate.desiredCountries.length === 0) {
    return false;
  }

  // Check work experience
  if (!candidate.workExperience ||
      (candidate.workExperience.length === 0 && !candidate.noWorkExperience)) {
    return false;
  }

  // If they have work experience, check that it's properly filled out
  if (candidate.workExperience && candidate.workExperience.length > 0) {
    const isValidWorkExperience = candidate.workExperience.every(exp =>
      exp.company && exp.position && exp.startDate
    );
    if (!isValidWorkExperience) {
      return false;
    }
  }

  // Check education
  if (!candidate.education || candidate.education.length === 0) {
    return false;
  }

  // Check that education is properly filled out
  const isValidEducation = candidate.education.every(edu =>
    edu.degree && edu.field && edu.startDate && edu.endDate
  );
  if (!isValidEducation) {
    return false;
  }

  // Check language skills - only for job applications, not for saving profile
  // We allow "None" as a valid value

  // Check benefits for matching
  if (!candidate.benefits || candidate.benefits.length < MIN_BENEFITS_REQUIRED) {
    return false;
  }

  return true;
}

/**
 * Gets a list of missing profile items
 * @param candidate The candidate profile to check
 * @returns Array of strings describing missing profile items
 */
export function getMissingProfileItems(candidate: Partial<Candidate>): string[] {
  const missingItems: string[] = [];

  if (!candidate.profession) {
    missingItems.push("Profession");
  }

  if (!candidate.age) {
    missingItems.push("Age");
  }

  if (!candidate.gender) {
    missingItems.push("Gender");
  }

  if (!candidate.desiredCountries || candidate.desiredCountries.length === 0) {
    missingItems.push("Desired Countries");
  }

  if (!candidate.workExperience ||
      (candidate.workExperience.length === 0 && !candidate.noWorkExperience)) {
    missingItems.push("Work experience (or indication of no experience)");
  }

  if (!candidate.education || candidate.education.length === 0) {
    missingItems.push("Education");
  }

  // Language levels are optional for saving profile
  // We only check them for job applications

  if (!candidate.benefits || candidate.benefits.length < MIN_BENEFITS_REQUIRED) {
    missingItems.push(`At least ${MIN_BENEFITS_REQUIRED} job preferences`);
  }

  return missingItems;
}
