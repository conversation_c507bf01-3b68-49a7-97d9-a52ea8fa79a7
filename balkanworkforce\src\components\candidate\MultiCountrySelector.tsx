import { useState, useEffect, useRef } from "react";
import Image from "next/image";
import { countries } from "@/data/countries";

interface MultiCountrySelectorProps {
  value: string[];
  onChange: (value: string[]) => void;
  required?: boolean;
  label?: string;
}

export default function MultiCountrySelector({ 
  value, 
  onChange, 
  required = false,
  label = "Desired Countries"
}: MultiCountrySelectorProps) {
  const [selectedCountries, setSelectedCountries] = useState<string[]>(value || []);
  const [isOpen, setIsOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState("");
  const dropdownRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    setSelectedCountries(value || []);
  }, [value]);

  useEffect(() => {
    onChange(selectedCountries);
  }, [selectedCountries, onChange]);

  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setIsOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const toggleCountry = (countryId: string) => {
    setSelectedCountries(prev => {
      if (prev.includes(countryId)) {
        return prev.filter(id => id !== countryId);
      } else {
        return [...prev, countryId];
      }
    });
  };

  const filteredCountries = searchTerm
    ? countries.filter(country => 
        country.name.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : countries;

  // Group countries by region (if region property exists)
  const euCountries = filteredCountries.filter(country => 
    country.region === "eu" || 
    ["de", "at", "ch", "fr", "it", "nl", "be", "lu", "dk", "se", "fi", "ie", "pt", "es"].includes(country.id)
  );
  
  const balkanCountries = filteredCountries.filter(country => 
    country.region === "balkans" || 
    ["ba", "rs", "hr", "me", "mk", "si", "al", "bg", "ro", "gr"].includes(country.id)
  );
  
  const otherCountries = filteredCountries.filter(country => 
    !euCountries.includes(country) && !balkanCountries.includes(country)
  );

  // Get selected country details
  const selectedCountryObjects = countries.filter(country => selectedCountries.includes(country.id));

  return (
    <div className="relative" ref={dropdownRef}>
      <label className="block text-sm font-medium mb-1">
        {label} {required && <span className="text-red-500">*</span>}
      </label>
      
      <button
        type="button"
        onClick={() => setIsOpen(!isOpen)}
        className="w-full flex items-center justify-between px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md bg-white dark:bg-neutral-800"
      >
        <div className="flex items-center flex-wrap gap-1">
          {selectedCountryObjects.length > 0 ? (
            selectedCountryObjects.map(country => (
              <div 
                key={country.id} 
                className="flex items-center bg-neutral-100 dark:bg-neutral-700 px-2 py-1 rounded-md mr-1 mb-1"
              >
                {country.flagUrl && (
                  <div className="w-4 h-3 mr-1 overflow-hidden rounded-sm">
                    <Image
                      src={country.flagUrl}
                      alt={country.name}
                      width={16}
                      height={12}
                      className="w-full h-full object-cover"
                    />
                  </div>
                )}
                <span className="text-sm">{country.name}</span>
                <button
                  type="button"
                  onClick={(e) => {
                    e.stopPropagation();
                    toggleCountry(country.id);
                  }}
                  className="ml-1 text-neutral-500 hover:text-neutral-700 dark:text-neutral-400 dark:hover:text-neutral-200"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" className="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            ))
          ) : (
            <span>Select countries where you want to work</span>
          )}
        </div>
        <svg
          xmlns="http://www.w3.org/2000/svg"
          className={`h-5 w-5 transition-transform ${isOpen ? "transform rotate-180" : ""}`}
          viewBox="0 0 20 20"
          fill="currentColor"
        >
          <path
            fillRule="evenodd"
            d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
            clipRule="evenodd"
          />
        </svg>
      </button>

      {isOpen && (
        <div className="absolute z-10 mt-1 w-full bg-white dark:bg-neutral-800 border border-neutral-300 dark:border-neutral-700 rounded-md shadow-lg max-h-60 overflow-auto">
          <div className="sticky top-0 bg-white dark:bg-neutral-800 p-2 border-b border-neutral-300 dark:border-neutral-700">
            <input
              type="text"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              placeholder="Search countries..."
              className="w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md"
              onClick={(e) => e.stopPropagation()}
            />
          </div>

          {euCountries.length > 0 && (
            <div>
              <div className="px-3 py-1 text-xs font-semibold text-neutral-500 dark:text-neutral-400 bg-neutral-100 dark:bg-neutral-700">
                European Union
              </div>
              {euCountries.map((country) => (
                <button
                  key={country.id}
                  type="button"
                  onClick={(e) => {
                    e.preventDefault();
                    toggleCountry(country.id);
                  }}
                  className={`w-full text-left px-3 py-2 flex items-center hover:bg-neutral-100 dark:hover:bg-neutral-700 ${
                    selectedCountries.includes(country.id) ? "bg-neutral-100 dark:bg-neutral-700" : ""
                  }`}
                >
                  <div className="flex items-center flex-1">
                    {country.flagUrl && (
                      <div className="w-6 h-4 mr-2 overflow-hidden rounded-sm">
                        <Image
                          src={country.flagUrl}
                          alt={country.name}
                          width={24}
                          height={16}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                    {country.name}
                  </div>
                  {selectedCountries.includes(country.id) && (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </button>
              ))}
            </div>
          )}

          {balkanCountries.length > 0 && (
            <div>
              <div className="px-3 py-1 text-xs font-semibold text-neutral-500 dark:text-neutral-400 bg-neutral-100 dark:bg-neutral-700">
                Balkan Countries
              </div>
              {balkanCountries.map((country) => (
                <button
                  key={country.id}
                  type="button"
                  onClick={(e) => {
                    e.preventDefault();
                    toggleCountry(country.id);
                  }}
                  className={`w-full text-left px-3 py-2 flex items-center hover:bg-neutral-100 dark:hover:bg-neutral-700 ${
                    selectedCountries.includes(country.id) ? "bg-neutral-100 dark:bg-neutral-700" : ""
                  }`}
                >
                  <div className="flex items-center flex-1">
                    {country.flagUrl && (
                      <div className="w-6 h-4 mr-2 overflow-hidden rounded-sm">
                        <Image
                          src={country.flagUrl}
                          alt={country.name}
                          width={24}
                          height={16}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                    {country.name}
                  </div>
                  {selectedCountries.includes(country.id) && (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </button>
              ))}
            </div>
          )}

          {otherCountries.length > 0 && (
            <div>
              <div className="px-3 py-1 text-xs font-semibold text-neutral-500 dark:text-neutral-400 bg-neutral-100 dark:bg-neutral-700">
                Other Countries
              </div>
              {otherCountries.map((country) => (
                <button
                  key={country.id}
                  type="button"
                  onClick={(e) => {
                    e.preventDefault();
                    toggleCountry(country.id);
                  }}
                  className={`w-full text-left px-3 py-2 flex items-center hover:bg-neutral-100 dark:hover:bg-neutral-700 ${
                    selectedCountries.includes(country.id) ? "bg-neutral-100 dark:bg-neutral-700" : ""
                  }`}
                >
                  <div className="flex items-center flex-1">
                    {country.flagUrl && (
                      <div className="w-6 h-4 mr-2 overflow-hidden rounded-sm">
                        <Image
                          src={country.flagUrl}
                          alt={country.name}
                          width={24}
                          height={16}
                          className="w-full h-full object-cover"
                        />
                      </div>
                    )}
                    {country.name}
                  </div>
                  {selectedCountries.includes(country.id) && (
                    <svg xmlns="http://www.w3.org/2000/svg" className="h-5 w-5 text-green-500" viewBox="0 0 20 20" fill="currentColor">
                      <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                    </svg>
                  )}
                </button>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
}
