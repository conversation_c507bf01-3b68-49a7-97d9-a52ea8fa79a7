{"/_not-found/page": "app/_not-found/page.js", "/admin/admins/page": "app/admin/admins/page.js", "/admin/applications/page": "app/admin/applications/page.js", "/admin/benefits/page": "app/admin/benefits/page.js", "/admin/candidates/page": "app/admin/candidates/page.js", "/admin/countries/page": "app/admin/countries/page.js", "/admin/education/page": "app/admin/education/page.js", "/admin/employers/page": "app/admin/employers/page.js", "/admin/jobs/page": "app/admin/jobs/page.js", "/admin/licenses/page": "app/admin/licenses/page.js", "/admin/page": "app/admin/page.js", "/admin/positions/page": "app/admin/positions/page.js", "/admin/settings/page": "app/admin/settings/page.js", "/auth/page": "app/auth/page.js", "/candidate/applications/page": "app/candidate/applications/page.js", "/candidate/dashboard/page": "app/candidate/dashboard/page.js", "/candidate/profile/page": "app/candidate/profile/page.js", "/employer/applications/page": "app/employer/applications/page.js", "/employer/candidates/[id]/page": "app/employer/candidates/[id]/page.js", "/employer/candidates/page": "app/employer/candidates/page.js", "/employer/dashboard/page": "app/employer/dashboard/page.js", "/employer/jobs/[id]/page": "app/employer/jobs/[id]/page.js", "/employer/jobs/new/page": "app/employer/jobs/new/page.js", "/employer/profile/page": "app/employer/profile/page.js", "/favicon.ico/route": "app/favicon.ico/route.js", "/jobs/[id]/page": "app/jobs/[id]/page.js", "/jobs/page": "app/jobs/page.js", "/page": "app/page.js", "/settings/page": "app/settings/page.js"}