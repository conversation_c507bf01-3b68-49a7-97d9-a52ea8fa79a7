import { WorkExperience } from "@/types";
import { calculateExperienceByPosition, formatExperienceDuration } from "@/utils/experienceCalculator";
import { positions } from "@/data/positions";
import { countries } from "@/data/countries";

interface ExperienceDisplayProps {
  experiences: WorkExperience[];
  noWorkExperience?: boolean;
}

export default function ExperienceDisplay({ experiences, noWorkExperience }: ExperienceDisplayProps) {
  if (noWorkExperience) {
    return (
      <div className="text-neutral-600 dark:text-neutral-400 italic">
        No work experience
      </div>
    );
  }

  if (!experiences || experiences.length === 0) {
    return (
      <div className="text-neutral-600 dark:text-neutral-400 italic">
        No work experience provided
      </div>
    );
  }

  // Sort experiences by date (most recent first)
  const sortedExperiences = [...experiences].sort((a, b) => {
    const dateA = a.current ? new Date() : new Date(a.endDate!);
    const dateB = b.current ? new Date() : new Date(b.endDate!);
    return dateB.getTime() - dateA.getTime();
  });

  // Calculate experience by position
  const experienceByPosition = calculateExperienceByPosition(experiences);

  return (
    <div className="space-y-6">
      <div className="bg-neutral-50 dark:bg-neutral-800 p-4 rounded-lg border border-neutral-200 dark:border-neutral-700">
        <h4 className="font-medium mb-2">Total Experience</h4>
        <div className="text-lg font-bold">{experienceByPosition.total}</div>
        
        {Object.entries(experienceByPosition)
          .filter(([key]) => key !== 'total')
          .map(([positionId, duration]) => {
            const position = positions.find(p => p.id === positionId);
            if (!position) return null;
            
            return (
              <div key={positionId} className="mt-2 flex items-center justify-between">
                <span>{position.translations.en}</span>
                <span className="text-neutral-600 dark:text-neutral-400">{duration}</span>
              </div>
            );
          })}
      </div>

      {sortedExperiences.map((experience, index) => {
        const startDate = new Date(experience.startDate);
        const endDate = experience.current ? new Date() : new Date(experience.endDate!);
        
        const formattedStartDate = `${startDate.toLocaleString('default', { month: 'short' })} ${startDate.getFullYear()}`;
        const formattedEndDate = experience.current 
          ? 'Present' 
          : `${endDate.toLocaleString('default', { month: 'short' })} ${endDate.getFullYear()}`;
        
        const position = experience.positionId 
          ? positions.find(p => p.id === experience.positionId) 
          : null;
          
        const country = experience.country 
          ? countries.find(c => c.id === experience.country) 
          : null;
        
        return (
          <div key={index} className="border-l-2 border-neutral-300 dark:border-neutral-700 pl-4 pb-6 relative">
            <div className="absolute w-3 h-3 bg-neutral-300 dark:bg-neutral-700 rounded-full -left-[7px] top-1"></div>
            
            <div className="flex flex-col md:flex-row md:justify-between md:items-start">
              <div>
                <h4 className="font-medium">{experience.position}</h4>
                <div className="text-neutral-600 dark:text-neutral-400">{experience.company}</div>
              </div>
              <div className="text-sm text-neutral-500 dark:text-neutral-400 mt-1 md:mt-0 md:text-right">
                {formattedStartDate} - {formattedEndDate}
              </div>
            </div>
            
            {(position || country || experience.industry) && (
              <div className="flex flex-wrap gap-2 mt-2">
                {position && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300">
                    {position.translations.en}
                  </span>
                )}
                
                {country && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300">
                    {country.name}
                  </span>
                )}
                
                {experience.industry && (
                  <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300">
                    {experience.industry.charAt(0).toUpperCase() + experience.industry.slice(1)}
                  </span>
                )}
              </div>
            )}
            
            {experience.description && (
              <div className="mt-2 text-sm text-neutral-600 dark:text-neutral-400">
                {experience.description}
              </div>
            )}
          </div>
        );
      })}
    </div>
  );
}
