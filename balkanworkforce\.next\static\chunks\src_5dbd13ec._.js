(globalThis.TURBOPACK = globalThis.TURBOPACK || []).push([typeof document === "object" ? document.currentScript : undefined, {

"[project]/src/data/jobBenefits.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
// Job benefits/features that employers can select when creating a job
// and candidates can select as preferences
__turbopack_context__.s({
    "MIN_BENEFITS_REQUIRED": (()=>MIN_BENEFITS_REQUIRED),
    "jobBenefits": (()=>jobBenefits)
});
const jobBenefits = [
    {
        id: "flexible_hours",
        label: "Flexible Working Hours",
        description: "Flexible start and end times or the ability to work remotely"
    },
    {
        id: "remote_work",
        label: "Remote Work Options",
        description: "Possibility to work remotely part-time or full-time"
    },
    {
        id: "health_insurance",
        label: "Health Insurance",
        description: "Comprehensive health insurance coverage"
    },
    {
        id: "paid_vacation",
        label: "Paid Vacation",
        description: "Generous paid time off and vacation days"
    },
    {
        id: "professional_development",
        label: "Professional Development",
        description: "Training, courses, and career advancement opportunities"
    },
    {
        id: "retirement_plan",
        label: "Retirement Plan",
        description: "Company pension or retirement savings plan"
    },
    {
        id: "relocation_assistance",
        label: "Relocation Assistance",
        description: "Help with moving expenses and finding accommodation"
    },
    {
        id: "accommodation_provided_free",
        label: "Free Accommodation Provided",
        description: "Accommodation is provided and fully paid by the employer"
    },
    {
        id: "accommodation_provided_paid",
        label: "Accommodation Provided (Paid)",
        description: "Accommodation is arranged by the employer but paid by the employee"
    },
    {
        id: "accommodation_not_provided",
        label: "Accommodation Not Provided",
        description: "Employee must arrange their own accommodation"
    },
    {
        id: "accommodation_assistance",
        label: "Accommodation Finding Assistance",
        description: "Employer assists in finding accommodation but does not provide it directly"
    },
    {
        id: "language_courses",
        label: "Language Courses",
        description: "Free or subsidized language training"
    },
    {
        id: "childcare",
        label: "Childcare Support",
        description: "On-site childcare or childcare subsidies"
    },
    {
        id: "gym_membership",
        label: "Gym Membership",
        description: "Free or discounted gym or wellness program"
    },
    {
        id: "company_car",
        label: "Company Car",
        description: "Company vehicle or car allowance"
    },
    {
        id: "meal_allowance",
        label: "Meal Allowance",
        description: "Free or subsidized meals or meal vouchers"
    },
    {
        id: "performance_bonus",
        label: "Performance Bonuses",
        description: "Financial bonuses based on performance"
    },
    {
        id: "housing_allowance",
        label: "Housing Allowance",
        description: "Assistance with housing costs"
    },
    {
        id: "public_transport",
        label: "Public Transport Subsidy",
        description: "Free or subsidized public transportation"
    }
];
const MIN_BENEFITS_REQUIRED = 3;
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/utils/experienceCalculator.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "calculateExperienceByPosition": (()=>calculateExperienceByPosition),
    "calculateTotalExperience": (()=>calculateTotalExperience),
    "formatExperienceDuration": (()=>formatExperienceDuration)
});
function calculateTotalExperience(experiences, positionId) {
    // Filter by position if specified
    const relevantExperiences = positionId ? experiences.filter((exp)=>exp.positionId === positionId) : experiences;
    // Calculate total months
    let totalMonths = 0;
    for (const exp of relevantExperiences){
        const startDate = new Date(exp.startDate);
        const endDate = exp.current ? new Date() : new Date(exp.endDate);
        // Calculate difference in months
        const months = (endDate.getFullYear() - startDate.getFullYear()) * 12 + (endDate.getMonth() - startDate.getMonth());
        totalMonths += Math.max(0, months); // Ensure we don't add negative months
    }
    return totalMonths;
}
function formatExperienceDuration(months) {
    if (months <= 0) {
        return "No experience";
    }
    const years = Math.floor(months / 12);
    const remainingMonths = months % 12;
    if (years === 0) {
        return `${remainingMonths} month${remainingMonths !== 1 ? 's' : ''}`;
    } else if (remainingMonths === 0) {
        return `${years} year${years !== 1 ? 's' : ''}`;
    } else {
        return `${years} year${years !== 1 ? 's' : ''}, ${remainingMonths} month${remainingMonths !== 1 ? 's' : ''}`;
    }
}
function calculateExperienceByPosition(experiences) {
    const result = {};
    // Get unique position IDs
    const positionIds = [
        ...new Set(experiences.filter((exp)=>exp.positionId).map((exp)=>exp.positionId))
    ];
    // Calculate experience for each position
    for (const positionId of positionIds){
        const months = calculateTotalExperience(experiences, positionId);
        result[positionId] = formatExperienceDuration(months);
    }
    // Add total experience
    const totalMonths = calculateTotalExperience(experiences);
    result.total = formatExperienceDuration(totalMonths);
    return result;
}
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/data/positions.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "positions": (()=>positions)
});
const positions = [
    {
        id: "electrician",
        translations: {
            de: "Elektriker",
            en: "Electrician",
            bs: "Električar",
            hr: "Električar",
            sr: "Električar",
            me: "Električar",
            mk: "Електричар",
            al: "Elektricist"
        },
        category: "construction",
        skills: [
            "electrical-wiring",
            "electrical-maintenance",
            "electrical-installation"
        ]
    },
    {
        id: "plumber",
        translations: {
            de: "Klempner",
            en: "Plumber",
            bs: "Vodoinstalater",
            hr: "Vodoinstalater",
            sr: "Vodoinstalater",
            me: "Vodoinstalater",
            mk: "Водоводџија",
            al: "Hidraulik"
        },
        category: "construction",
        skills: [
            "plumbing",
            "pipe-fitting",
            "water-systems"
        ]
    },
    {
        id: "carpenter",
        translations: {
            de: "Tischler",
            en: "Carpenter",
            bs: "Stolar",
            hr: "Stolar",
            sr: "Stolar",
            me: "Stolar",
            mk: "Столар",
            al: "Marangoz"
        },
        category: "construction",
        skills: [
            "woodworking",
            "furniture-making",
            "carpentry"
        ]
    },
    {
        id: "welder",
        translations: {
            de: "Schweißer",
            en: "Welder",
            bs: "Zavarivač",
            hr: "Zavarivač",
            sr: "Zavarivač",
            me: "Zavarivač",
            mk: "Заварувач",
            al: "Saldator"
        },
        category: "manufacturing",
        skills: [
            "welding",
            "metal-fabrication",
            "blueprint-reading"
        ]
    },
    {
        id: "chef",
        translations: {
            de: "Koch",
            en: "Chef",
            bs: "Kuhar",
            hr: "Kuhar",
            sr: "Kuvar",
            me: "Kuvar",
            mk: "Готвач",
            al: "Kuzhinier"
        },
        category: "hospitality",
        skills: [
            "cooking",
            "food-preparation",
            "menu-planning"
        ]
    },
    {
        id: "waiter",
        translations: {
            de: "Kellner",
            en: "Waiter",
            bs: "Konobar",
            hr: "Konobar",
            sr: "Konobar",
            me: "Konobar",
            mk: "Келнер",
            al: "Kamarier"
        },
        category: "hospitality",
        skills: [
            "customer-service",
            "food-service",
            "order-taking"
        ]
    },
    {
        id: "nurse",
        translations: {
            de: "Krankenpfleger",
            en: "Nurse",
            bs: "Medicinska sestra/tehničar",
            hr: "Medicinska sestra/tehničar",
            sr: "Medicinska sestra/tehničar",
            me: "Medicinska sestra/tehničar",
            mk: "Медицинска сестра/техничар",
            al: "Infermier"
        },
        category: "healthcare",
        skills: [
            "patient-care",
            "medical-assistance",
            "health-monitoring"
        ]
    },
    {
        id: "caregiver",
        translations: {
            de: "Pflegekraft",
            en: "Caregiver",
            bs: "Njegovatelj",
            hr: "Njegovatelj",
            sr: "Negovatelj",
            me: "Njegovatelj",
            mk: "Негувател",
            al: "Kujdestar"
        },
        category: "healthcare",
        skills: [
            "elderly-care",
            "personal-care",
            "medication-management"
        ]
    },
    {
        id: "driver",
        translations: {
            de: "Fahrer",
            en: "Driver",
            bs: "Vozač",
            hr: "Vozač",
            sr: "Vozač",
            me: "Vozač",
            mk: "Возач",
            al: "Shofer"
        },
        category: "transportation",
        skills: [
            "driving",
            "vehicle-maintenance",
            "route-planning"
        ]
    },
    {
        id: "software-developer",
        translations: {
            de: "Softwareentwickler",
            en: "Software Developer",
            bs: "Programer",
            hr: "Programer",
            sr: "Programer",
            me: "Programer",
            mk: "Програмер",
            al: "Zhvillues Softueri"
        },
        category: "it",
        skills: [
            "programming",
            "software-development",
            "problem-solving"
        ]
    },
    {
        id: "accountant",
        translations: {
            de: "Buchhalter",
            en: "Accountant",
            bs: "Računovođa",
            hr: "Računovođa",
            sr: "Računovođa",
            me: "Računovođa",
            mk: "Сметководител",
            al: "Kontabilist"
        },
        category: "finance",
        skills: [
            "accounting",
            "bookkeeping",
            "financial-reporting"
        ]
    },
    {
        id: "teacher",
        translations: {
            de: "Lehrer",
            en: "Teacher",
            bs: "Nastavnik",
            hr: "Nastavnik",
            sr: "Nastavnik",
            me: "Nastavnik",
            mk: "Наставник",
            al: "Mësues"
        },
        category: "education",
        skills: [
            "teaching",
            "curriculum-development",
            "student-assessment"
        ]
    },
    {
        id: "sales-representative",
        translations: {
            de: "Vertriebsmitarbeiter",
            en: "Sales Representative",
            bs: "Prodajni predstavnik",
            hr: "Prodajni predstavnik",
            sr: "Prodajni predstavnik",
            me: "Prodajni predstavnik",
            mk: "Продажен претставник",
            al: "Përfaqësues Shitjesh"
        },
        category: "sales",
        skills: [
            "sales",
            "customer-relations",
            "negotiation"
        ]
    },
    {
        id: "warehouse-worker",
        translations: {
            de: "Lagerarbeiter",
            en: "Warehouse Worker",
            bs: "Skladištar",
            hr: "Skladištar",
            sr: "Magacioner",
            me: "Magacioner",
            mk: "Магационер",
            al: "Punëtor Magazinimi"
        },
        category: "logistics",
        skills: [
            "inventory-management",
            "forklift-operation",
            "order-picking"
        ]
    },
    {
        id: "cleaner",
        translations: {
            de: "Reinigungskraft",
            en: "Cleaner",
            bs: "Čistač",
            hr: "Čistač",
            sr: "Čistač",
            me: "Čistač",
            mk: "Хигиеничар",
            al: "Pastrues"
        },
        category: "facility-services",
        skills: [
            "cleaning",
            "sanitation",
            "housekeeping"
        ]
    }
];
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/data/countries.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "countries": (()=>countries)
});
const countries = [
    {
        id: "de",
        name: "Germany",
        code: "DE",
        flagUrl: "/flags/de.svg",
        region: "eu"
    },
    {
        id: "at",
        name: "Austria",
        code: "AT",
        flagUrl: "/flags/at.svg",
        region: "eu"
    },
    {
        id: "hr",
        name: "Croatia",
        code: "HR",
        flagUrl: "/flags/hr.svg",
        region: "eu"
    },
    {
        id: "si",
        name: "Slovenia",
        code: "SI",
        flagUrl: "/flags/si.svg",
        region: "eu"
    },
    {
        id: "ba",
        name: "Bosnia and Herzegovina",
        code: "BA",
        flagUrl: "/flags/ba.svg",
        region: "balkans"
    },
    {
        id: "rs",
        name: "Serbia",
        code: "RS",
        flagUrl: "/flags/rs.svg",
        region: "balkans"
    },
    {
        id: "me",
        name: "Montenegro",
        code: "ME",
        flagUrl: "/flags/me.svg",
        region: "balkans"
    },
    {
        id: "mk",
        name: "North Macedonia",
        code: "MK",
        flagUrl: "/flags/mk.svg",
        region: "balkans"
    },
    {
        id: "al",
        name: "Albania",
        code: "AL",
        flagUrl: "/flags/al.svg",
        region: "balkans"
    },
    {
        id: "bg",
        name: "Bulgaria",
        code: "BG",
        flagUrl: "/flags/bg.svg",
        region: "balkans"
    },
    {
        id: "ro",
        name: "Romania",
        code: "RO",
        flagUrl: "/flags/ro.svg",
        region: "balkans"
    },
    {
        id: "gr",
        name: "Greece",
        code: "GR",
        flagUrl: "/flags/gr.svg",
        region: "balkans"
    }
];
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/candidate/ExperienceDisplay.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>ExperienceDisplay)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$experienceCalculator$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/utils/experienceCalculator.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$positions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/positions.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$countries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/countries.ts [app-client] (ecmascript)");
;
;
;
;
function ExperienceDisplay({ experiences, noWorkExperience }) {
    if (noWorkExperience) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "text-neutral-600 dark:text-neutral-400 italic",
            children: "No work experience"
        }, void 0, false, {
            fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
            lineNumber: 14,
            columnNumber: 7
        }, this);
    }
    if (!experiences || experiences.length === 0) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "text-neutral-600 dark:text-neutral-400 italic",
            children: "No work experience provided"
        }, void 0, false, {
            fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
            lineNumber: 22,
            columnNumber: 7
        }, this);
    }
    // Sort experiences by date (most recent first)
    const sortedExperiences = [
        ...experiences
    ].sort((a, b)=>{
        const dateA = a.current ? new Date() : new Date(a.endDate);
        const dateB = b.current ? new Date() : new Date(b.endDate);
        return dateB.getTime() - dateA.getTime();
    });
    // Calculate experience by position
    const experienceByPosition = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$utils$2f$experienceCalculator$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["calculateExperienceByPosition"])(experiences);
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-neutral-50 dark:bg-neutral-800 p-4 rounded-lg border border-neutral-200 dark:border-neutral-700",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                        className: "font-medium mb-2",
                        children: "Total Experience"
                    }, void 0, false, {
                        fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
                        lineNumber: 41,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "text-lg font-bold",
                        children: experienceByPosition.total
                    }, void 0, false, {
                        fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
                        lineNumber: 42,
                        columnNumber: 9
                    }, this),
                    Object.entries(experienceByPosition).filter(([key])=>key !== 'total').map(([positionId, duration])=>{
                        const position = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$positions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["positions"].find((p)=>p.id === positionId);
                        if (!position) return null;
                        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-2 flex items-center justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    children: position.translations.en
                                }, void 0, false, {
                                    fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
                                    lineNumber: 52,
                                    columnNumber: 17
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "text-neutral-600 dark:text-neutral-400",
                                    children: duration
                                }, void 0, false, {
                                    fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
                                    lineNumber: 53,
                                    columnNumber: 17
                                }, this)
                            ]
                        }, positionId, true, {
                            fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
                            lineNumber: 51,
                            columnNumber: 15
                        }, this);
                    })
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
                lineNumber: 40,
                columnNumber: 7
            }, this),
            sortedExperiences.map((experience, index)=>{
                const startDate = new Date(experience.startDate);
                const endDate = experience.current ? new Date() : new Date(experience.endDate);
                const formattedStartDate = `${startDate.toLocaleString('default', {
                    month: 'short'
                })} ${startDate.getFullYear()}`;
                const formattedEndDate = experience.current ? 'Present' : `${endDate.toLocaleString('default', {
                    month: 'short'
                })} ${endDate.getFullYear()}`;
                const position = experience.positionId ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$positions$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["positions"].find((p)=>p.id === experience.positionId) : null;
                const country = experience.country ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$countries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["countries"].find((c)=>c.id === experience.country) : null;
                return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "border-l-2 border-neutral-300 dark:border-neutral-700 pl-4 pb-6 relative",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "absolute w-3 h-3 bg-neutral-300 dark:bg-neutral-700 rounded-full -left-[7px] top-1"
                        }, void 0, false, {
                            fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
                            lineNumber: 78,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-col md:flex-row md:justify-between md:items-start",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    children: [
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                            className: "font-medium",
                                            children: experience.position
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
                                            lineNumber: 82,
                                            columnNumber: 17
                                        }, this),
                                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "text-neutral-600 dark:text-neutral-400",
                                            children: experience.company
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
                                            lineNumber: 83,
                                            columnNumber: 17
                                        }, this)
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
                                    lineNumber: 81,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "text-sm text-neutral-500 dark:text-neutral-400 mt-1 md:mt-0 md:text-right",
                                    children: [
                                        formattedStartDate,
                                        " - ",
                                        formattedEndDate
                                    ]
                                }, void 0, true, {
                                    fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
                                    lineNumber: 85,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
                            lineNumber: 80,
                            columnNumber: 13
                        }, this),
                        (position || country || experience.industry) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex flex-wrap gap-2 mt-2",
                            children: [
                                position && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300",
                                    children: position.translations.en
                                }, void 0, false, {
                                    fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
                                    lineNumber: 93,
                                    columnNumber: 19
                                }, this),
                                country && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300",
                                    children: country.name
                                }, void 0, false, {
                                    fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
                                    lineNumber: 99,
                                    columnNumber: 19
                                }, this),
                                experience.industry && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                    className: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300",
                                    children: experience.industry.charAt(0).toUpperCase() + experience.industry.slice(1)
                                }, void 0, false, {
                                    fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
                                    lineNumber: 105,
                                    columnNumber: 19
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
                            lineNumber: 91,
                            columnNumber: 15
                        }, this),
                        experience.description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-2 text-sm text-neutral-600 dark:text-neutral-400",
                            children: experience.description
                        }, void 0, false, {
                            fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
                            lineNumber: 113,
                            columnNumber: 15
                        }, this)
                    ]
                }, index, true, {
                    fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
                    lineNumber: 77,
                    columnNumber: 11
                }, this);
            })
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/candidate/ExperienceDisplay.tsx",
        lineNumber: 39,
        columnNumber: 5
    }, this);
}
_c = ExperienceDisplay;
var _c;
__turbopack_context__.k.register(_c, "ExperienceDisplay");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/data/degrees.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "degrees": (()=>degrees)
});
const degrees = [
    {
        id: "primary-school",
        translations: {
            de: "Grundschule",
            en: "Primary School",
            bs: "Osnovna škola",
            hr: "Osnovna škola",
            sr: "Osnovna škola",
            me: "Osnovna škola",
            mk: "Основно училиште",
            al: "Shkollë fillore"
        },
        level: "primary"
    },
    {
        id: "high-school",
        translations: {
            de: "Mittelschule",
            en: "High School",
            bs: "Srednja škola",
            hr: "Srednja škola",
            sr: "Srednja škola",
            me: "Srednja škola",
            mk: "Средно училиште",
            al: "Shkollë e mesme"
        },
        level: "secondary"
    },
    {
        id: "bachelors-degree",
        translations: {
            de: "Bachelor",
            en: "Bachelor's Degree",
            bs: "Bakalaureat",
            hr: "Prvostupnik",
            sr: "Osnovne akademske studije",
            me: "Osnovne akademske studije",
            mk: "Додипломски студии",
            al: "Diplomë Bachelor"
        },
        level: "bachelor"
    },
    {
        id: "masters-degree",
        translations: {
            de: "Master",
            en: "Master's Degree",
            bs: "Magistar",
            hr: "Magistar",
            sr: "Master",
            me: "Master",
            mk: "Магистерски студии",
            al: "Diplomë Master"
        },
        level: "master"
    },
    {
        id: "doctorate",
        translations: {
            de: "Doktor",
            en: "Doctorate",
            bs: "Doktorat",
            hr: "Doktorat",
            sr: "Doktorat",
            me: "Doktorat",
            mk: "Докторат",
            al: "Doktoraturë"
        },
        level: "doctorate"
    }
];
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/data/studyFields.ts [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "studyFields": (()=>studyFields)
});
const studyFields = [
    {
        id: "electrical-engineering",
        translations: {
            de: "Elektrotechnik",
            en: "Electrical Engineering",
            bs: "Elektrotehnika",
            hr: "Elektrotehnika",
            sr: "Elektrotehnika",
            me: "Elektrotehnika",
            mk: "Електротехника",
            al: "Inxhinieri Elektrike"
        },
        category: "engineering"
    },
    {
        id: "mechanical-engineering",
        translations: {
            de: "Maschinenbau",
            en: "Mechanical Engineering",
            bs: "Mašinstvo",
            hr: "Strojarstvo",
            sr: "Mašinstvo",
            me: "Mašinstvo",
            mk: "Машинство",
            al: "Inxhinieri Mekanike"
        },
        category: "engineering"
    },
    {
        id: "automotive-mechanic",
        translations: {
            de: "Kfz-Mechaniker",
            en: "Automotive Mechanic",
            bs: "Automehaničar",
            hr: "Automehaničar",
            sr: "Automehaničar",
            me: "Automehaničar",
            mk: "Автомеханичар",
            al: "Mekanik Automjetesh"
        },
        category: "vocational"
    },
    {
        id: "automotive-painter",
        translations: {
            de: "Kfz-Lackierer",
            en: "Automotive Painter",
            bs: "Autolakirer",
            hr: "Autolakirer",
            sr: "Autolakirer",
            me: "Autolakirer",
            mk: "Автолакер",
            al: "Bojaxhi Automjetesh"
        },
        category: "vocational"
    },
    {
        id: "carpentry",
        translations: {
            de: "Tischlerei",
            en: "Carpentry",
            bs: "Stolarstvo",
            hr: "Stolarstvo",
            sr: "Stolarstvo",
            me: "Stolarstvo",
            mk: "Столарство",
            al: "Marangozi"
        },
        category: "vocational"
    },
    {
        id: "plumbing",
        translations: {
            de: "Sanitärinstallation",
            en: "Plumbing",
            bs: "Vodoinstalaterstvo",
            hr: "Vodoinstalaterstvo",
            sr: "Vodoinstalaterstvo",
            me: "Vodoinstalaterstvo",
            mk: "Водоинсталатерство",
            al: "Hidraulikë"
        },
        category: "vocational"
    },
    {
        id: "welding",
        translations: {
            de: "Schweißen",
            en: "Welding",
            bs: "Zavarivanje",
            hr: "Zavarivanje",
            sr: "Zavarivanje",
            me: "Zavarivanje",
            mk: "Заварување",
            al: "Saldim"
        },
        category: "vocational"
    },
    {
        id: "culinary-arts",
        translations: {
            de: "Kochkunst",
            en: "Culinary Arts",
            bs: "Kuharstvo",
            hr: "Kuharstvo",
            sr: "Kuvarstvo",
            me: "Kuvarstvo",
            mk: "Готварство",
            al: "Arti Kulinar"
        },
        category: "hospitality"
    },
    {
        id: "nursing",
        translations: {
            de: "Krankenpflege",
            en: "Nursing",
            bs: "Sestrinstvo",
            hr: "Sestrinstvo",
            sr: "Sestrinstvo",
            me: "Sestrinstvo",
            mk: "Медицинска нега",
            al: "Infermieri"
        },
        category: "healthcare"
    },
    {
        id: "general-high-school",
        translations: {
            de: "Allgemeine Hochschulreife",
            en: "General High School",
            bs: "Gimnazija",
            hr: "Gimnazija",
            sr: "Gimnazija",
            me: "Gimnazija",
            mk: "Гимназија",
            al: "Gjimnaz"
        },
        category: "general-education"
    }
];
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/candidate/EducationDisplay.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>EducationDisplay)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$degrees$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/degrees.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$studyFields$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/studyFields.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$countries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/countries.ts [app-client] (ecmascript)");
;
;
;
;
function EducationDisplay({ educations }) {
    if (!educations || !Array.isArray(educations) || educations.length === 0) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "text-neutral-600 dark:text-neutral-400 italic",
            children: "No education provided"
        }, void 0, false, {
            fileName: "[project]/src/components/candidate/EducationDisplay.tsx",
            lineNumber: 13,
            columnNumber: 7
        }, this);
    }
    // Sort educations by date (most recent first)
    const sortedEducations = Array.isArray(educations) ? [
        ...educations
    ].sort((a, b)=>{
        const dateA = new Date(a.endDate);
        const dateB = new Date(b.endDate);
        return dateB.getTime() - dateA.getTime();
    }) : [];
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-6",
        children: sortedEducations.map((education, index)=>{
            const startDate = new Date(education.startDate);
            const endDate = new Date(education.endDate);
            const formattedStartDate = `${startDate.toLocaleString('default', {
                month: 'short'
            })} ${startDate.getFullYear()}`;
            const formattedEndDate = `${endDate.toLocaleString('default', {
                month: 'short'
            })} ${endDate.getFullYear()}`;
            const degree = education.degreeId ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$degrees$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["degrees"].find((d)=>d.id === education.degreeId) : null;
            const field = education.fieldId ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$studyFields$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["studyFields"].find((f)=>f.id === education.fieldId) : null;
            const country = education.country ? __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$countries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["countries"].find((c)=>c.id === education.country) : null;
            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "border-l-2 border-neutral-300 dark:border-neutral-700 pl-4 pb-6 relative",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "absolute w-3 h-3 bg-neutral-300 dark:bg-neutral-700 rounded-full -left-[7px] top-1"
                    }, void 0, false, {
                        fileName: "[project]/src/components/candidate/EducationDisplay.tsx",
                        lineNumber: 49,
                        columnNumber: 13
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-col md:flex-row md:justify-between md:items-start",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        className: "font-medium",
                                        children: education.degree
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/candidate/EducationDisplay.tsx",
                                        lineNumber: 53,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "text-neutral-600 dark:text-neutral-400",
                                        children: education.field
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/candidate/EducationDisplay.tsx",
                                        lineNumber: 54,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/candidate/EducationDisplay.tsx",
                                lineNumber: 52,
                                columnNumber: 15
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "text-sm text-neutral-500 dark:text-neutral-400 mt-1 md:mt-0 md:text-right",
                                children: [
                                    formattedStartDate,
                                    " - ",
                                    formattedEndDate
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/candidate/EducationDisplay.tsx",
                                lineNumber: 56,
                                columnNumber: 15
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/candidate/EducationDisplay.tsx",
                        lineNumber: 51,
                        columnNumber: 13
                    }, this),
                    (degree || field || country) && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex flex-wrap gap-2 mt-2",
                        children: [
                            degree && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300",
                                children: degree.translations.en
                            }, void 0, false, {
                                fileName: "[project]/src/components/candidate/EducationDisplay.tsx",
                                lineNumber: 64,
                                columnNumber: 19
                            }, this),
                            field && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300",
                                children: field.translations.en
                            }, void 0, false, {
                                fileName: "[project]/src/components/candidate/EducationDisplay.tsx",
                                lineNumber: 70,
                                columnNumber: 19
                            }, this),
                            country && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                className: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300",
                                children: country.name
                            }, void 0, false, {
                                fileName: "[project]/src/components/candidate/EducationDisplay.tsx",
                                lineNumber: 76,
                                columnNumber: 19
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/candidate/EducationDisplay.tsx",
                        lineNumber: 62,
                        columnNumber: 15
                    }, this),
                    education.description && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "mt-2 text-sm text-neutral-600 dark:text-neutral-400",
                        children: education.description
                    }, void 0, false, {
                        fileName: "[project]/src/components/candidate/EducationDisplay.tsx",
                        lineNumber: 84,
                        columnNumber: 15
                    }, this)
                ]
            }, index, true, {
                fileName: "[project]/src/components/candidate/EducationDisplay.tsx",
                lineNumber: 48,
                columnNumber: 11
            }, this);
        })
    }, void 0, false, {
        fileName: "[project]/src/components/candidate/EducationDisplay.tsx",
        lineNumber: 27,
        columnNumber: 5
    }, this);
}
_c = EducationDisplay;
var _c;
__turbopack_context__.k.register(_c, "EducationDisplay");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/components/candidate/LanguageSkillsDisplay.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>LanguageSkillsDisplay)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
;
function LanguageSkillsDisplay({ germanLevel, englishLevel, otherLanguages = [] }) {
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "space-y-4",
        children: [
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "grid grid-cols-1 md:grid-cols-2 gap-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg p-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between items-center mb-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        className: "font-medium",
                                        children: "German"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                                        lineNumber: 19,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300",
                                        children: germanLevel
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                                        lineNumber: 20,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                                lineNumber: 18,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2.5",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-blue-600 h-2.5 rounded-full",
                                    style: {
                                        width: `${getLanguageLevelPercentage(germanLevel)}%`
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                                    lineNumber: 25,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                                lineNumber: 24,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-1 text-xs text-neutral-500 dark:text-neutral-400",
                                children: getLanguageLevelDescription(germanLevel)
                            }, void 0, false, {
                                fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                                lineNumber: 30,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                        lineNumber: 17,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg p-4",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex justify-between items-center mb-2",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                        className: "font-medium",
                                        children: "English"
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                                        lineNumber: 37,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-green-100 dark:bg-green-900/20 text-green-800 dark:text-green-300",
                                        children: englishLevel
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                                        lineNumber: 38,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                                lineNumber: 36,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2.5",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                    className: "bg-green-600 h-2.5 rounded-full",
                                    style: {
                                        width: `${getLanguageLevelPercentage(englishLevel)}%`
                                    }
                                }, void 0, false, {
                                    fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                                    lineNumber: 43,
                                    columnNumber: 13
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                                lineNumber: 42,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "mt-1 text-xs text-neutral-500 dark:text-neutral-400",
                                children: getLanguageLevelDescription(englishLevel)
                            }, void 0, false, {
                                fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                                lineNumber: 48,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                        lineNumber: 35,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                lineNumber: 16,
                columnNumber: 7
            }, this),
            otherLanguages.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "mt-4",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                        className: "font-medium mb-3",
                        children: "Other Languages"
                    }, void 0, false, {
                        fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                        lineNumber: 56,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "grid grid-cols-1 md:grid-cols-2 gap-4",
                        children: otherLanguages.map((language, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "bg-white dark:bg-neutral-800 border border-neutral-200 dark:border-neutral-700 rounded-lg p-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex justify-between items-center mb-2",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                className: "font-medium",
                                                children: language.language
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                                                lineNumber: 64,
                                                columnNumber: 19
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                className: "inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-purple-100 dark:bg-purple-900/20 text-purple-800 dark:text-purple-300",
                                                children: language.level
                                            }, void 0, false, {
                                                fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                                                lineNumber: 65,
                                                columnNumber: 19
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                                        lineNumber: 63,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2.5",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "bg-purple-600 h-2.5 rounded-full",
                                            style: {
                                                width: `${getLanguageLevelPercentage(language.level)}%`
                                            }
                                        }, void 0, false, {
                                            fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                                            lineNumber: 70,
                                            columnNumber: 19
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                                        lineNumber: 69,
                                        columnNumber: 17
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-1 text-xs text-neutral-500 dark:text-neutral-400",
                                        children: getLanguageLevelDescription(language.level)
                                    }, void 0, false, {
                                        fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                                        lineNumber: 75,
                                        columnNumber: 17
                                    }, this)
                                ]
                            }, index, true, {
                                fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                                lineNumber: 59,
                                columnNumber: 15
                            }, this))
                    }, void 0, false, {
                        fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                        lineNumber: 57,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
                lineNumber: 55,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/components/candidate/LanguageSkillsDisplay.tsx",
        lineNumber: 15,
        columnNumber: 5
    }, this);
}
_c = LanguageSkillsDisplay;
function getLanguageLevelPercentage(level) {
    switch(level){
        case "A1":
            return 16.7;
        case "A2":
            return 33.3;
        case "B1":
            return 50;
        case "B2":
            return 66.7;
        case "C1":
            return 83.3;
        case "C2":
        case "Native":
            return 100;
        default:
            return 0;
    }
}
function getLanguageLevelDescription(level) {
    switch(level){
        case "A1":
            return "Beginner - Can understand and use familiar everyday expressions";
        case "A2":
            return "Elementary - Can communicate in simple and routine tasks";
        case "B1":
            return "Intermediate - Can deal with most situations likely to arise while traveling";
        case "B2":
            return "Upper Intermediate - Can interact with a degree of fluency and spontaneity";
        case "C1":
            return "Advanced - Can express ideas fluently and spontaneously without much searching for expressions";
        case "C2":
            return "Proficient - Can understand with ease virtually everything heard or read";
        case "Native":
            return "Native Speaker - Mother tongue proficiency";
        default:
            return "";
    }
}
var _c;
__turbopack_context__.k.register(_c, "LanguageSkillsDisplay");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
"[project]/src/app/employer/candidates/[id]/page.tsx [app-client] (ecmascript)": ((__turbopack_context__) => {
"use strict";

var { g: global, __dirname, k: __turbopack_refresh__, m: module } = __turbopack_context__;
{
__turbopack_context__.s({
    "default": (()=>CandidateDetailPage)
});
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/jsx-dev-runtime.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/compiled/react/index.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/navigation.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/contexts/AuthContext.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$firebase$2f$firestore$2f$dist$2f$esm$2f$index$2e$esm$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__$3c$module__evaluation$3e$__ = __turbopack_context__.i("[project]/node_modules/firebase/firestore/dist/esm/index.esm.js [app-client] (ecmascript) <module evaluation>");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/@firebase/firestore/dist/index.esm2017.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/lib/firebase.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/dist/client/app-dir/link.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/node_modules/next/image.js [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$jobBenefits$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/jobBenefits.ts [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$candidate$2f$ExperienceDisplay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/candidate/ExperienceDisplay.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$candidate$2f$EducationDisplay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/candidate/EducationDisplay.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$candidate$2f$LanguageSkillsDisplay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/components/candidate/LanguageSkillsDisplay.tsx [app-client] (ecmascript)");
var __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$countries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__ = __turbopack_context__.i("[project]/src/data/countries.ts [app-client] (ecmascript)");
;
var _s = __turbopack_context__.k.signature();
"use client";
;
;
;
;
;
;
;
;
;
;
;
;
function CandidateDetailPage() {
    _s();
    const { id } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useParams"])();
    const router = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"])();
    const { user, loading: authLoading } = (0, __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"])();
    const [candidate, setCandidate] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [employerJobs, setEmployerJobs] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    const [loading, setLoading] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(true);
    const [error, setError] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(null);
    const [showInviteModal, setShowInviteModal] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [selectedJobId, setSelectedJobId] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [inviteMessage, setInviteMessage] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])("");
    const [sending, setSending] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(false);
    const [remainingInvites, setRemainingInvites] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])(0);
    const [subscriptions, setSubscriptions] = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useState"])([]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CandidateDetailPage.useEffect": ()=>{
            // If not authenticated or not an employer, redirect to auth page
            if (!authLoading && (!user || user.role !== "employer")) {
                router.push("/auth");
            }
        }
    }["CandidateDetailPage.useEffect"], [
        user,
        authLoading,
        router
    ]);
    (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$index$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useEffect"])({
        "CandidateDetailPage.useEffect": ()=>{
            const fetchData = {
                "CandidateDetailPage.useEffect.fetchData": async ()=>{
                    if (!user || !id || typeof id !== "string") return;
                    try {
                        // Fetch candidate data
                        const candidateDoc = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDoc"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["doc"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["db"], "candidates", id));
                        if (!candidateDoc.exists()) {
                            setError("Candidate not found");
                            return;
                        }
                        const candidateData = candidateDoc.data();
                        setCandidate({
                            id: candidateDoc.id,
                            ...candidateData,
                            createdAt: candidateData.createdAt?.toDate(),
                            updatedAt: candidateData.updatedAt?.toDate()
                        });
                        // Fetch employer's jobs
                        const jobsCollection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["db"], "jobs");
                        const jobsQuery = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["query"])(jobsCollection, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["where"])("employerId", "==", user.uid));
                        const querySnapshot = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDocs"])(jobsQuery);
                        const jobsList = [];
                        querySnapshot.forEach({
                            "CandidateDetailPage.useEffect.fetchData": (doc)=>{
                                const data = doc.data();
                                jobsList.push({
                                    id: doc.id,
                                    ...data,
                                    createdAt: data.createdAt.toDate(),
                                    updatedAt: data.updatedAt.toDate(),
                                    premiumUntil: data.premiumUntil ? data.premiumUntil.toDate() : undefined
                                });
                            }
                        }["CandidateDetailPage.useEffect.fetchData"]);
                        setEmployerJobs(jobsList);
                        // Fetch employer's active subscriptions
                        const subscriptionsCollection = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["db"], "subscriptions");
                        const subscriptionsQuery = (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["query"])(subscriptionsCollection, (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["where"])("employerId", "==", user.uid), (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["where"])("isActive", "==", true));
                        const subscriptionsSnapshot = await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["getDocs"])(subscriptionsQuery);
                        const subscriptionsList = [];
                        let totalInvites = 0;
                        subscriptionsSnapshot.forEach({
                            "CandidateDetailPage.useEffect.fetchData": (doc)=>{
                                const data = doc.data();
                                const subscription = {
                                    id: doc.id,
                                    ...data,
                                    startDate: data.startDate.toDate(),
                                    endDate: data.endDate ? data.endDate.toDate() : undefined,
                                    createdAt: data.createdAt.toDate(),
                                    updatedAt: data.updatedAt.toDate()
                                };
                                subscriptionsList.push(subscription);
                                if (subscription.packageType === 'cv' || subscription.packageType === 'combined') {
                                    totalInvites += subscription.invitesRemaining;
                                }
                            }
                        }["CandidateDetailPage.useEffect.fetchData"]);
                        setSubscriptions(subscriptionsList);
                        setRemainingInvites(totalInvites);
                    } catch (error) {
                        console.error("Error fetching data:", error);
                        setError("Failed to load candidate details. Please try again.");
                    } finally{
                        setLoading(false);
                    }
                }
            }["CandidateDetailPage.useEffect.fetchData"];
            if (user && user.role === "employer") {
                fetchData();
            }
        }
    }["CandidateDetailPage.useEffect"], [
        user,
        id,
        router
    ]);
    // Calculate match percentage between candidate and job
    const calculateMatchPercentage = (job)=>{
        if (!candidate.benefits || !job.benefits) return 0;
        const candidateBenefits = candidate.benefits;
        const jobBenefits = job.benefits;
        // Count matching benefits
        const matchingBenefits = candidateBenefits.filter((benefit)=>jobBenefits.includes(benefit)).length;
        // Calculate percentage based on candidate's preferences
        return Math.round(matchingBenefits / candidateBenefits.length * 100);
    };
    const handleInvite = (jobId)=>{
        if (remainingInvites <= 0) {
            // Redirect to packages page if no invites remaining
            router.push('/employer/packages');
            return;
        }
        setSelectedJobId(jobId);
        setInviteMessage("");
        setShowInviteModal(true);
    };
    const sendInvite = async ()=>{
        if (!user || !candidate || !selectedJobId) return;
        setSending(true);
        try {
            // Find the job details
            const selectedJob = employerJobs.find((job)=>job.id === selectedJobId);
            if (!selectedJob) {
                throw new Error("Selected job not found");
            }
            // Create invite document
            const inviteData = {
                employerId: user.uid,
                candidateId: candidate.id,
                jobId: selectedJobId,
                message: inviteMessage.trim() || undefined,
                status: "pending",
                createdAt: new Date(),
                updatedAt: new Date(),
                expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000),
                jobTitle: selectedJob.title,
                employerCompanyName: user.companyName || "Company",
                candidateProfession: candidate.profession,
                candidateAge: candidate.age
            };
            await (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["addDoc"])((0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f40$firebase$2f$firestore$2f$dist$2f$index$2e$esm2017$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["collection"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$lib$2f$firebase$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["db"], "invites"), inviteData);
            // Find the subscription to deduct an invite from
            // For simplicity, we'll just use the first subscription with remaining invites
            const subscriptionToUpdate = subscriptions.find((sub)=>(sub.packageType === 'cv' || sub.packageType === 'combined') && sub.invitesRemaining > 0);
            if (subscriptionToUpdate) {
                // Update the subscription in Firestore
                await fetch('/api/subscriptions/use-invite', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        subscriptionId: subscriptionToUpdate.id
                    })
                });
                // Update local state
                setRemainingInvites((prev)=>prev - 1);
                setSubscriptions((prev)=>prev.map((sub)=>sub.id === subscriptionToUpdate.id ? {
                            ...sub,
                            invitesRemaining: sub.invitesRemaining - 1
                        } : sub));
            }
            // Close modal and show success message
            setShowInviteModal(false);
            alert("Invitation sent successfully!");
        } catch (error) {
            console.error("Error sending invite:", error);
            alert("Failed to send invitation. Please try again.");
        } finally{
            setSending(false);
        }
    };
    if (authLoading || loading) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "flex justify-center items-center min-h-[60vh]",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-neutral-900"
            }, void 0, false, {
                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                lineNumber: 230,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
            lineNumber: 229,
            columnNumber: 7
        }, this);
    }
    if (error) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-4xl mx-auto py-12",
            children: [
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-red-50 dark:bg-red-900/20 text-red-600 dark:text-red-400 p-4 rounded-md mb-6",
                    children: error
                }, void 0, false, {
                    fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                    lineNumber: 238,
                    columnNumber: 9
                }, this),
                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                    href: "/employer/candidates",
                    className: "text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white",
                    children: "Back to Candidates"
                }, void 0, false, {
                    fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                    lineNumber: 241,
                    columnNumber: 9
                }, this)
            ]
        }, void 0, true, {
            fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
            lineNumber: 237,
            columnNumber: 7
        }, this);
    }
    if (!candidate) {
        return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
            className: "max-w-4xl mx-auto py-12",
            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "text-center",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                        className: "text-2xl font-bold mb-4",
                        children: "Candidate Not Found"
                    }, void 0, false, {
                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                        lineNumber: 255,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                        className: "text-neutral-600 dark:text-neutral-400 mb-6",
                        children: "The candidate you're looking for doesn't exist or you don't have permission to view it."
                    }, void 0, false, {
                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                        lineNumber: 256,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        href: "/employer/candidates",
                        className: "text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white",
                        children: "Back to Candidates"
                    }, void 0, false, {
                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                        lineNumber: 259,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                lineNumber: 254,
                columnNumber: 9
            }, this)
        }, void 0, false, {
            fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
            lineNumber: 253,
            columnNumber: 7
        }, this);
    }
    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
        className: "max-w-4xl mx-auto py-12",
        children: [
            showInviteModal && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50",
                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                    className: "bg-white dark:bg-neutral-800 rounded-lg shadow-lg max-w-md w-full p-6",
                    children: [
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                            className: "text-xl font-semibold mb-4",
                            children: "Invite Candidate to Apply"
                        }, void 0, false, {
                            fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                            lineNumber: 276,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mb-4",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    className: "block text-sm font-medium mb-1",
                                    children: "Select Job"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                    lineNumber: 279,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("select", {
                                    value: selectedJobId,
                                    onChange: (e)=>setSelectedJobId(e.target.value),
                                    className: "w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md bg-white dark:bg-neutral-800",
                                    children: employerJobs.map((job)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("option", {
                                            value: job.id,
                                            children: job.title
                                        }, job.id, false, {
                                            fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                            lineNumber: 288,
                                            columnNumber: 19
                                        }, this))
                                }, void 0, false, {
                                    fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                    lineNumber: 282,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                            lineNumber: 278,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mb-6",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("label", {
                                    className: "block text-sm font-medium mb-1",
                                    children: "Message (Optional)"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                    lineNumber: 296,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("textarea", {
                                    value: inviteMessage,
                                    onChange: (e)=>setInviteMessage(e.target.value),
                                    rows: 4,
                                    className: "w-full px-3 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md",
                                    placeholder: "Add a personal message to the candidate..."
                                }, void 0, false, {
                                    fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                    lineNumber: 299,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                            lineNumber: 295,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "flex justify-between",
                            children: [
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: ()=>setShowInviteModal(false),
                                    className: "px-4 py-2 border border-neutral-300 dark:border-neutral-700 rounded-md text-neutral-700 dark:text-neutral-300",
                                    children: "Cancel"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                    lineNumber: 309,
                                    columnNumber: 15
                                }, this),
                                /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                    onClick: sendInvite,
                                    disabled: sending || !selectedJobId,
                                    className: `px-4 py-2 rounded-md ${sending ? "bg-neutral-700 text-white" : "bg-neutral-900 hover:bg-neutral-800 text-white"}`,
                                    children: sending ? "Sending..." : "Send Invitation"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                    lineNumber: 315,
                                    columnNumber: 15
                                }, this)
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                            lineNumber: 308,
                            columnNumber: 13
                        }, this),
                        /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                            className: "mt-4 text-sm text-neutral-500 dark:text-neutral-400",
                            children: [
                                "You have ",
                                remainingInvites,
                                " invite",
                                remainingInvites !== 1 ? "s" : "",
                                " remaining."
                            ]
                        }, void 0, true, {
                            fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                            lineNumber: 324,
                            columnNumber: 13
                        }, this)
                    ]
                }, void 0, true, {
                    fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                    lineNumber: 275,
                    columnNumber: 11
                }, this)
            }, void 0, false, {
                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                lineNumber: 274,
                columnNumber: 9
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "flex justify-between items-center mb-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h1", {
                        className: "text-3xl font-bold",
                        children: "Candidate Profile"
                    }, void 0, false, {
                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                        lineNumber: 331,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                        href: "/employer/candidates",
                        className: "text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white",
                        children: "Back to Candidates"
                    }, void 0, false, {
                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                        lineNumber: 332,
                        columnNumber: 9
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                lineNumber: 330,
                columnNumber: 7
            }, this),
            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8 mb-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "flex items-center mb-6",
                        children: [
                            candidate.photoURL ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-24 h-24 rounded-full overflow-hidden mr-6",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                    src: candidate.photoURL,
                                    alt: "Profile",
                                    width: 96,
                                    height: 96,
                                    className: "w-full h-full object-cover"
                                }, void 0, false, {
                                    fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                    lineNumber: 344,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                lineNumber: 343,
                                columnNumber: 13
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "w-24 h-24 bg-neutral-200 dark:bg-neutral-700 rounded-full flex items-center justify-center mr-6",
                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                    xmlns: "http://www.w3.org/2000/svg",
                                    className: "h-12 w-12 text-neutral-500",
                                    fill: "none",
                                    viewBox: "0 0 24 24",
                                    stroke: "currentColor",
                                    children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                        strokeLinecap: "round",
                                        strokeLinejoin: "round",
                                        strokeWidth: 2,
                                        d: "M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                        lineNumber: 355,
                                        columnNumber: 17
                                    }, this)
                                }, void 0, false, {
                                    fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                    lineNumber: 354,
                                    columnNumber: 15
                                }, this)
                            }, void 0, false, {
                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                lineNumber: 353,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h2", {
                                        className: "text-2xl font-bold mb-1",
                                        children: [
                                            candidate.profession,
                                            ", ",
                                            candidate.age
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                        lineNumber: 360,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center",
                                        children: [
                                            candidate.country && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center mr-3",
                                                children: [
                                                    __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$countries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["countries"].find((c)=>c.id === candidate.country)?.flagUrl && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "w-4 h-3 mr-1 overflow-hidden rounded-sm",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$image$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                            src: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$countries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["countries"].find((c)=>c.id === candidate.country)?.flagUrl || "",
                                                            alt: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$countries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["countries"].find((c)=>c.id === candidate.country)?.name || "",
                                                            width: 16,
                                                            height: 12,
                                                            className: "w-full h-full object-cover"
                                                        }, void 0, false, {
                                                            fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                                            lineNumber: 368,
                                                            columnNumber: 23
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                                        lineNumber: 367,
                                                        columnNumber: 21
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                        className: "text-neutral-600 dark:text-neutral-400",
                                                        children: __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$countries$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["countries"].find((c)=>c.id === candidate.country)?.name
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                                        lineNumber: 377,
                                                        columnNumber: 19
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                                lineNumber: 365,
                                                columnNumber: 17
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                    className: "text-neutral-600 dark:text-neutral-400",
                                                    children: [
                                                        "German: ",
                                                        candidate.germanLanguageLevel || "Not specified",
                                                        ", English: ",
                                                        candidate.englishLanguageLevel || "Not specified"
                                                    ]
                                                }, void 0, true, {
                                                    fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                                    lineNumber: 383,
                                                    columnNumber: 17
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                                lineNumber: 382,
                                                columnNumber: 15
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                        lineNumber: 363,
                                        columnNumber: 13
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                        className: "text-sm text-neutral-500 dark:text-neutral-500 mt-1",
                                        children: [
                                            "Member since ",
                                            candidate.createdAt?.toLocaleDateString() || "N/A"
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                        lineNumber: 389,
                                        columnNumber: 13
                                    }, this)
                                ]
                            }, void 0, true, {
                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                lineNumber: 359,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                        lineNumber: 341,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-xl font-semibold mb-4",
                                children: "Skills"
                            }, void 0, false, {
                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                lineNumber: 396,
                                columnNumber: 11
                            }, this),
                            candidate.skills && candidate.skills.length > 0 ? /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-wrap gap-2",
                                children: candidate.skills.map((skill, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 px-3 py-1 rounded-md",
                                        children: skill
                                    }, index, false, {
                                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                        lineNumber: 400,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                lineNumber: 398,
                                columnNumber: 13
                            }, this) : /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                className: "text-neutral-600 dark:text-neutral-400",
                                children: "No skills specified"
                            }, void 0, false, {
                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                lineNumber: 409,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                        lineNumber: 395,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-xl font-semibold mb-4",
                                children: "Experience"
                            }, void 0, false, {
                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                lineNumber: 414,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$candidate$2f$ExperienceDisplay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                experiences: candidate.workExperience || [],
                                noWorkExperience: candidate.noWorkExperience
                            }, void 0, false, {
                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                lineNumber: 415,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                        lineNumber: 413,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-xl font-semibold mb-4",
                                children: "Education"
                            }, void 0, false, {
                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                lineNumber: 422,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$candidate$2f$EducationDisplay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                educations: candidate.education || []
                            }, void 0, false, {
                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                lineNumber: 423,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                        lineNumber: 421,
                        columnNumber: 9
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-xl font-semibold mb-4",
                                children: "Language Skills"
                            }, void 0, false, {
                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                lineNumber: 427,
                                columnNumber: 11
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$src$2f$components$2f$candidate$2f$LanguageSkillsDisplay$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                germanLevel: candidate.germanLanguageLevel || "A1",
                                englishLevel: candidate.englishLanguageLevel || "A1",
                                otherLanguages: candidate.otherLanguages || []
                            }, void 0, false, {
                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                lineNumber: 428,
                                columnNumber: 11
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                        lineNumber: 426,
                        columnNumber: 9
                    }, this),
                    candidate.benefits && candidate.benefits.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-xl font-semibold mb-4",
                                children: "Job Preferences"
                            }, void 0, false, {
                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                lineNumber: 437,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "grid grid-cols-1 md:grid-cols-2 gap-3",
                                children: candidate.benefits.map((benefitId)=>{
                                    const benefit = __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$data$2f$jobBenefits$2e$ts__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jobBenefits"].find((b)=>b.id === benefitId);
                                    return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex items-center",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("svg", {
                                                xmlns: "http://www.w3.org/2000/svg",
                                                className: "h-5 w-5 text-green-500 mr-2",
                                                viewBox: "0 0 20 20",
                                                fill: "currentColor",
                                                children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("path", {
                                                    fillRule: "evenodd",
                                                    d: "M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z",
                                                    clipRule: "evenodd"
                                                }, void 0, false, {
                                                    fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                                    lineNumber: 444,
                                                    columnNumber: 23
                                                }, this)
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                                lineNumber: 443,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                children: benefit ? benefit.label : benefitId
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                                lineNumber: 446,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, benefitId, true, {
                                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                        lineNumber: 442,
                                        columnNumber: 19
                                    }, this);
                                })
                            }, void 0, false, {
                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                lineNumber: 438,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                        lineNumber: 436,
                        columnNumber: 11
                    }, this),
                    candidate.desiredPositions && candidate.desiredPositions.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "border-t border-neutral-200 dark:border-neutral-700 pt-6 mb-6",
                        children: [
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                                className: "text-xl font-semibold mb-4",
                                children: "Desired Positions"
                            }, void 0, false, {
                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                lineNumber: 456,
                                columnNumber: 13
                            }, this),
                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "flex flex-wrap gap-2",
                                children: candidate.desiredPositions.map((position, index)=>/*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                        className: "bg-neutral-100 dark:bg-neutral-700 text-neutral-800 dark:text-neutral-300 px-3 py-1 rounded-md",
                                        children: position
                                    }, index, false, {
                                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                        lineNumber: 459,
                                        columnNumber: 17
                                    }, this))
                            }, void 0, false, {
                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                lineNumber: 457,
                                columnNumber: 13
                            }, this)
                        ]
                    }, void 0, true, {
                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                        lineNumber: 455,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                lineNumber: 340,
                columnNumber: 7
            }, this),
            employerJobs.length > 0 && /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                className: "bg-white dark:bg-neutral-800 rounded-lg shadow-sm border border-neutral-200 dark:border-neutral-700 p-8",
                children: [
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h3", {
                        className: "text-xl font-semibold mb-6",
                        children: "Match with Your Jobs"
                    }, void 0, false, {
                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                        lineNumber: 473,
                        columnNumber: 11
                    }, this),
                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                        className: "space-y-4",
                        children: employerJobs.map((job)=>{
                            const matchPercentage = calculateMatchPercentage(job);
                            return /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                className: "border border-neutral-200 dark:border-neutral-700 rounded-lg p-4",
                                children: [
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "flex justify-between items-start",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("h4", {
                                                        className: "font-medium",
                                                        children: job.title
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                                        lineNumber: 484,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("p", {
                                                        className: "text-sm text-neutral-600 dark:text-neutral-400",
                                                        children: [
                                                            job.location,
                                                            " • ",
                                                            job.employmentType.replace("-", " ")
                                                        ]
                                                    }, void 0, true, {
                                                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                                        lineNumber: 485,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                                lineNumber: 483,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: "flex items-center",
                                                children: [
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "w-12 h-12 rounded-full bg-neutral-100 dark:bg-neutral-800 border-4 border-neutral-200 dark:border-neutral-700 flex items-center justify-center mr-2",
                                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("span", {
                                                            className: "text-sm font-medium",
                                                            children: [
                                                                matchPercentage,
                                                                "%"
                                                            ]
                                                        }, void 0, true, {
                                                            fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                                            lineNumber: 491,
                                                            columnNumber: 25
                                                        }, this)
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                                        lineNumber: 490,
                                                        columnNumber: 23
                                                    }, this),
                                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                        className: "text-xs",
                                                        children: "Match"
                                                    }, void 0, false, {
                                                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                                        lineNumber: 493,
                                                        columnNumber: 23
                                                    }, this)
                                                ]
                                            }, void 0, true, {
                                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                                lineNumber: 489,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                        lineNumber: 482,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-3 flex items-center",
                                        children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                            className: "w-full bg-neutral-200 dark:bg-neutral-700 rounded-full h-2",
                                            children: /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                                className: `h-2 rounded-full ${matchPercentage >= 80 ? "bg-green-500" : matchPercentage >= 50 ? "bg-yellow-500" : "bg-red-500"}`,
                                                style: {
                                                    width: `${matchPercentage}%`
                                                }
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                                lineNumber: 500,
                                                columnNumber: 23
                                            }, this)
                                        }, void 0, false, {
                                            fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                            lineNumber: 499,
                                            columnNumber: 21
                                        }, this)
                                    }, void 0, false, {
                                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                        lineNumber: 498,
                                        columnNumber: 19
                                    }, this),
                                    /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("div", {
                                        className: "mt-4 flex justify-between items-center",
                                        children: [
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])(__TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$client$2f$app$2d$dir$2f$link$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["default"], {
                                                href: `/employer/jobs/${job.id}`,
                                                className: "text-sm text-neutral-600 hover:text-neutral-900 dark:text-neutral-400 dark:hover:text-white",
                                                children: "View Job"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                                lineNumber: 511,
                                                columnNumber: 21
                                            }, this),
                                            /*#__PURE__*/ (0, __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$dist$2f$compiled$2f$react$2f$jsx$2d$dev$2d$runtime$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["jsxDEV"])("button", {
                                                className: "bg-neutral-900 hover:bg-neutral-800 text-white px-4 py-2 rounded-md text-sm",
                                                onClick: ()=>handleInvite(job.id),
                                                children: remainingInvites > 0 ? "Invite to Apply" : "Buy Invites"
                                            }, void 0, false, {
                                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                                lineNumber: 517,
                                                columnNumber: 21
                                            }, this)
                                        ]
                                    }, void 0, true, {
                                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                        lineNumber: 510,
                                        columnNumber: 19
                                    }, this)
                                ]
                            }, job.id, true, {
                                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                                lineNumber: 478,
                                columnNumber: 17
                            }, this);
                        })
                    }, void 0, false, {
                        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                        lineNumber: 474,
                        columnNumber: 11
                    }, this)
                ]
            }, void 0, true, {
                fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
                lineNumber: 472,
                columnNumber: 9
            }, this)
        ]
    }, void 0, true, {
        fileName: "[project]/src/app/employer/candidates/[id]/page.tsx",
        lineNumber: 271,
        columnNumber: 5
    }, this);
}
_s(CandidateDetailPage, "X8ckvxQMmB00x2LNl4wPxTs6mkg=", false, function() {
    return [
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useParams"],
        __TURBOPACK__imported__module__$5b$project$5d2f$node_modules$2f$next$2f$navigation$2e$js__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useRouter"],
        __TURBOPACK__imported__module__$5b$project$5d2f$src$2f$contexts$2f$AuthContext$2e$tsx__$5b$app$2d$client$5d$__$28$ecmascript$29$__["useAuth"]
    ];
});
_c = CandidateDetailPage;
var _c;
__turbopack_context__.k.register(_c, "CandidateDetailPage");
if (typeof globalThis.$RefreshHelpers$ === 'object' && globalThis.$RefreshHelpers !== null) {
    __turbopack_context__.k.registerExports(module, globalThis.$RefreshHelpers$);
}
}}),
}]);

//# sourceMappingURL=src_5dbd13ec._.js.map